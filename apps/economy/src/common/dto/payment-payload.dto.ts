/**
 * Payment模块的Payload DTO定义
 * 
 * 为payment.controller.ts中的所有@MessagePattern接口生成对应的Payload DTO
 * 所有DTO都继承自BasePayloadDto，基于真实的接口数据结构
 * 包含详细的校验规则和@Expose()装饰器
 */

import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsObject, Length } from 'class-validator';
import { Expose } from 'class-transformer';
import { BasePayloadDto } from '@libs/common/dto/base-payload.dto';

// ==================== 1. 支付处理相关 ====================

/**
 * 处理支付Payload DTO
 * @MessagePattern('payment.process')
 * 基于真实接口结构：{ data: any; injectedContext?: InjectedContext }
 */
export class ProcessPaymentPayloadDto extends BasePayloadDto {
  @ApiProperty({ 
    description: '支付数据', 
    type: 'object', 
    example: { 
      userId: 'user_12345', 
      amount: 100, 
      currency: 'USD', 
      paymentMethod: 'credit_card',
      orderId: 'order_67890'
    } 
  })
  @Expose()
  @IsObject({ message: '支付数据必须是对象' })
  data: any;
}

// ==================== 2. 支付验证相关 ====================

/**
 * 验证支付Payload DTO
 * @MessagePattern('payment.verify')
 * 基于真实接口结构：{ transactionId: string; injectedContext?: InjectedContext }
 */
export class VerifyPaymentPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '交易ID', example: 'txn_12345abcde' })
  @Expose()
  @IsString({ message: '交易ID必须是字符串' })
  @Length(1, 100, { message: '交易ID长度必须在1-100个字符之间' })
  transactionId: string;
}
