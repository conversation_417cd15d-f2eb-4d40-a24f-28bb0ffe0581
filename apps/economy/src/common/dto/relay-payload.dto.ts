/**
 * Relay模块的Payload DTO定义
 * 
 * 为relay.controller.ts中的所有@MessagePattern接口生成对应的Payload DTO
 * 所有DTO都继承自BasePayloadDto，基于真实的接口数据结构
 * 包含详细的校验规则和@Expose()装饰器
 */

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsNumber, IsOptional, Length, Min, Max } from 'class-validator';
import { Expose } from 'class-transformer';
import { BasePayloadDto } from '@libs/common/dto/base-payload.dto';

// ==================== 1. 转播信息相关 ====================

/**
 * 获取转播信息Payload DTO
 * @MessagePattern('relay.getInfo')
 * 基于真实接口结构：{ uid: string; serverId: string; injectedContext?: InjectedContext }
 */
export class GetRelayInfoPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '用户ID', example: 'user_12345' })
  @Expose()
  @IsString({ message: '用户ID必须是字符串' })
  @Length(1, 50, { message: '用户ID长度必须在1-50个字符之间' })
  uid: string;

  @ApiProperty({ description: '服务器ID', example: 'server_001' })
  @Expose()
  @IsString({ message: '服务器ID必须是字符串' })
  @Length(1, 50, { message: '服务器ID长度必须在1-50个字符之间' })
  serverId: string;
}

// ==================== 2. 转播购买相关 ====================

/**
 * 购买转播Payload DTO
 * @MessagePattern('relay.buy')
 * 基于真实接口结构：{ uid: string; serverId: string; injectedContext?: InjectedContext }
 */
export class BuyRelayPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '用户ID', example: 'user_12345' })
  @Expose()
  @IsString({ message: '用户ID必须是字符串' })
  @Length(1, 50, { message: '用户ID长度必须在1-50个字符之间' })
  uid: string;

  @ApiProperty({ description: '服务器ID', example: 'server_001' })
  @Expose()
  @IsString({ message: '服务器ID必须是字符串' })
  @Length(1, 50, { message: '服务器ID长度必须在1-50个字符之间' })
  serverId: string;
}

// ==================== 3. 商城兑换相关 ====================

/**
 * 商城兑换Payload DTO
 * @MessagePattern('relay.convert')
 * 基于真实接口结构：{ uid: string; serverId: string; id: number; injectedContext?: InjectedContext }
 */
export class ConvertibilityPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '用户ID', example: 'user_12345' })
  @Expose()
  @IsString({ message: '用户ID必须是字符串' })
  @Length(1, 50, { message: '用户ID长度必须在1-50个字符之间' })
  uid: string;

  @ApiProperty({ description: '服务器ID', example: 'server_001' })
  @Expose()
  @IsString({ message: '服务器ID必须是字符串' })
  @Length(1, 50, { message: '服务器ID长度必须在1-50个字符之间' })
  serverId: string;

  @ApiProperty({ description: '兑换ID', example: 1001 })
  @Expose()
  @IsNumber({}, { message: '兑换ID必须是数字' })
  @Min(1, { message: '兑换ID不能小于1' })
  @Max(999999, { message: '兑换ID不能大于999999' })
  id: number;
}

// ==================== 4. 奖励领取相关 ====================

/**
 * 领取奖励Payload DTO
 * @MessagePattern('relay.receiveAward')
 * 基于真实接口结构：{ uid: string; serverId: string; awardId: number; injectedContext?: InjectedContext }
 */
export class ReceiveAwardPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '用户ID', example: 'user_12345' })
  @Expose()
  @IsString({ message: '用户ID必须是字符串' })
  @Length(1, 50, { message: '用户ID长度必须在1-50个字符之间' })
  uid: string;

  @ApiProperty({ description: '服务器ID', example: 'server_001' })
  @Expose()
  @IsString({ message: '服务器ID必须是字符串' })
  @Length(1, 50, { message: '服务器ID长度必须在1-50个字符之间' })
  serverId: string;

  @ApiProperty({ description: '奖励ID', example: 2001 })
  @Expose()
  @IsNumber({}, { message: '奖励ID必须是数字' })
  @Min(1, { message: '奖励ID不能小于1' })
  @Max(999999, { message: '奖励ID不能大于999999' })
  awardId: number;
}

/**
 * 一键领取所有奖励Payload DTO
 * @MessagePattern('relay.receiveAllAward')
 * 基于真实接口结构：{ uid: string; serverId: string; injectedContext?: InjectedContext }
 */
export class ReceiveAllAwardPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '用户ID', example: 'user_12345' })
  @Expose()
  @IsString({ message: '用户ID必须是字符串' })
  @Length(1, 50, { message: '用户ID长度必须在1-50个字符之间' })
  uid: string;

  @ApiProperty({ description: '服务器ID', example: 'server_001' })
  @Expose()
  @IsString({ message: '服务器ID必须是字符串' })
  @Length(1, 50, { message: '服务器ID长度必须在1-50个字符之间' })
  serverId: string;
}

// ==================== 5. 积分管理相关 ====================

/**
 * 添加积分Payload DTO
 * @MessagePattern('relay.addIntegral')
 * 基于真实接口结构：{ uid: string; serverId: string; amount: number; injectedContext?: InjectedContext }
 */
export class AddIntegralPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '用户ID', example: 'user_12345' })
  @Expose()
  @IsString({ message: '用户ID必须是字符串' })
  @Length(1, 50, { message: '用户ID长度必须在1-50个字符之间' })
  uid: string;

  @ApiProperty({ description: '服务器ID', example: 'server_001' })
  @Expose()
  @IsString({ message: '服务器ID必须是字符串' })
  @Length(1, 50, { message: '服务器ID长度必须在1-50个字符之间' })
  serverId: string;

  @ApiProperty({ description: '积分数量', example: 100 })
  @Expose()
  @IsNumber({}, { message: '积分数量必须是数字' })
  @Min(1, { message: '积分数量不能小于1' })
  @Max(999999, { message: '积分数量不能大于999999' })
  amount: number;
}

// ==================== 6. 排行榜和统计相关 ====================

/**
 * 获取积分排行榜Payload DTO
 * @MessagePattern('relay.getRanking')
 * 基于真实接口结构：{ limit?: number; injectedContext?: InjectedContext }
 */
export class GetIntegralRankingPayloadDto extends BasePayloadDto {
  @ApiPropertyOptional({ description: '排行榜数量限制', example: 100, minimum: 1, maximum: 500 })
  @Expose()
  @IsOptional()
  @IsNumber({}, { message: '排行榜数量限制必须是数字' })
  @Min(1, { message: '排行榜数量限制不能小于1' })
  @Max(500, { message: '排行榜数量限制不能大于500' })
  limit?: number;
}

/**
 * 获取转播统计信息Payload DTO
 * @MessagePattern('relay.getStats')
 * 基于真实接口结构：{ adminToken?: string; injectedContext?: InjectedContext }
 */
export class GetRelayStatsPayloadDto extends BasePayloadDto {
  @ApiPropertyOptional({ description: '管理员令牌', example: 'admin_token_123' })
  @Expose()
  @IsOptional()
  @IsString({ message: '管理员令牌必须是字符串' })
  adminToken?: string;
}
