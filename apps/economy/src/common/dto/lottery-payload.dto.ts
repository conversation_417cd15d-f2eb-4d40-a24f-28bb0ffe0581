/**
 * Lottery模块的Payload DTO定义
 * 
 * 为lottery.controller.ts中的所有@MessagePattern接口生成对应的Payload DTO
 * 所有DTO都继承自BasePayloadDto，基于真实的接口数据结构
 * 包含详细的校验规则和@Expose()装饰器
 */

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsNumber, IsOptional, Length, Min, Max } from 'class-validator';
import { Expose } from 'class-transformer';
import { BasePayloadDto } from '@libs/common/dto/base-payload.dto';

// ==================== 1. 球员抽奖相关 ====================

/**
 * 球员抽奖Payload DTO
 * @MessagePattern('lottery.lotteryHero')
 * 基于真实接口结构：{ characterId: string; type: number; times: number; serverId?: string; injectedContext?: InjectedContext }
 */
export class LotteryHeroPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ 
    description: '抽奖类型', 
    example: 1, 
    enum: [1, 2],
    enumName: 'LotteryType'
  })
  @Expose()
  @IsNumber({}, { message: '抽奖类型必须是数字' })
  @Min(1, { message: '抽奖类型不能小于1' })
  @Max(2, { message: '抽奖类型不能大于2' })
  type: number; // 1=金币抽奖, 2=代币抽奖

  @ApiProperty({ 
    description: '抽奖次数', 
    example: 1, 
    enum: [1, 10],
    enumName: 'LotteryTimes'
  })
  @Expose()
  @IsNumber({}, { message: '抽奖次数必须是数字' })
  @Min(1, { message: '抽奖次数不能小于1' })
  @Max(10, { message: '抽奖次数不能大于10' })
  times: number; // 1=单抽, 10=十连抽

  @ApiPropertyOptional({ description: '服务器ID', example: 'server_001' })
  @Expose()
  @IsOptional()
  @IsString({ message: '服务器ID必须是字符串' })
  @Length(1, 50, { message: '服务器ID长度必须在1-50个字符之间' })
  serverId?: string;
}

// ==================== 2. 抽奖配置相关 ====================

/**
 * 获取抽奖配置Payload DTO
 * @MessagePattern('lottery.getLotteryConfig')
 * 基于真实接口结构：{ characterId: string; serverId?: string; injectedContext?: InjectedContext }
 */
export class GetLotteryConfigPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiPropertyOptional({ description: '服务器ID', example: 'server_001' })
  @Expose()
  @IsOptional()
  @IsString({ message: '服务器ID必须是字符串' })
  @Length(1, 50, { message: '服务器ID长度必须在1-50个字符之间' })
  serverId?: string;
}

// ==================== 3. 抽奖历史相关 ====================

/**
 * 获取抽奖历史Payload DTO
 * @MessagePattern('lottery.getLotteryHistory')
 * 基于真实接口结构：{ characterId: string; page?: number; limit?: number; serverId?: string; injectedContext?: InjectedContext }
 */
export class GetLotteryHistoryPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiPropertyOptional({ description: '页码', example: 1, minimum: 1 })
  @Expose()
  @IsOptional()
  @IsNumber({}, { message: '页码必须是数字' })
  @Min(1, { message: '页码不能小于1' })
  page?: number;

  @ApiPropertyOptional({ description: '每页数量', example: 10, minimum: 1, maximum: 100 })
  @Expose()
  @IsOptional()
  @IsNumber({}, { message: '每页数量必须是数字' })
  @Min(1, { message: '每页数量不能小于1' })
  @Max(100, { message: '每页数量不能大于100' })
  limit?: number;

  @ApiPropertyOptional({ description: '服务器ID', example: 'server_001' })
  @Expose()
  @IsOptional()
  @IsString({ message: '服务器ID必须是字符串' })
  @Length(1, 50, { message: '服务器ID长度必须在1-50个字符之间' })
  serverId?: string;
}
