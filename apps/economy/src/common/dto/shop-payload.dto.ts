/**
 * Shop模块的Payload DTO定义
 * 
 * 为shop.controller.ts中的所有@MessagePattern接口生成对应的Payload DTO
 * 所有DTO都继承自BasePayloadDto，基于真实的接口数据结构
 * 包含详细的校验规则和@Expose()装饰器
 */

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsNumber, IsOptional, IsEnum, IsObject, ValidateNested, Length, Min, Max } from 'class-validator';
import { Expose, Type } from 'class-transformer';
import { BasePayloadDto } from '@libs/common/dto/base-payload.dto';
import { ShopType, RefreshCycle } from '../schemas/shop.schema';
import { 
  PurchaseGoodsDto, 
  RefreshShopDto, 
  ClaimMonthCardDto, 
  BuyMonthCardDto,
  GetShopListDto,
  GetPurchaseHistoryDto,
  ShopStatsDto,
  VipShopDto,
  LimitShopDto
} from './shop.dto';

// ==================== 1. 商店信息管理相关 ====================

/**
 * 获取商店信息Payload DTO
 * @MessagePattern('shop.getInfo')
 * 基于真实接口结构：{ characterId: string; serverId: string; shopType: ShopType; injectedContext?: InjectedContext }
 */
export class GetShopInfoPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '服务器ID', example: 'server_001' })
  @Expose()
  @IsString({ message: '服务器ID必须是字符串' })
  @Length(1, 50, { message: '服务器ID长度必须在1-50个字符之间' })
  serverId: string;

  @ApiProperty({ description: '商店类型', enum: ShopType, example: ShopType.NORMAL })
  @Expose()
  @IsEnum(ShopType, { message: '商店类型必须是有效的枚举值' })
  shopType: ShopType;
}

/**
 * 获取商店列表Payload DTO
 * @MessagePattern('shop.getList')
 * 基于真实接口结构：{ getShopListDto: GetShopListDto; injectedContext?: InjectedContext }
 */
export class GetShopListPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '商店列表查询条件', type: GetShopListDto })
  @Expose()
  @IsObject({ message: '商店列表查询条件必须是对象' })
  @ValidateNested({ message: '商店列表查询条件格式不正确' })
  @Type(() => GetShopListDto)
  getShopListDto: GetShopListDto;
}

// ==================== 2. 商品购买相关 ====================

/**
 * 购买商品Payload DTO
 * @MessagePattern('shop.purchase')
 * 基于真实接口结构：{ characterId: string; serverId: string; purchaseDto: PurchaseGoodsDto; injectedContext?: InjectedContext }
 */
export class PurchaseGoodsPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '服务器ID', example: 'server_001' })
  @Expose()
  @IsString({ message: '服务器ID必须是字符串' })
  @Length(1, 50, { message: '服务器ID长度必须在1-50个字符之间' })
  serverId: string;

  @ApiProperty({ description: '购买商品数据', type: PurchaseGoodsDto })
  @Expose()
  @IsObject({ message: '购买商品数据必须是对象' })
  @ValidateNested({ message: '购买商品数据格式不正确' })
  @Type(() => PurchaseGoodsDto)
  purchaseDto: PurchaseGoodsDto;
}

/**
 * 刷新商店Payload DTO
 * @MessagePattern('shop.refresh')
 * 基于真实接口结构：{ characterId: string; serverId: string; refreshDto: RefreshShopDto; injectedContext?: InjectedContext }
 */
export class RefreshShopPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '服务器ID', example: 'server_001' })
  @Expose()
  @IsString({ message: '服务器ID必须是字符串' })
  @Length(1, 50, { message: '服务器ID长度必须在1-50个字符之间' })
  serverId: string;

  @ApiProperty({ description: '刷新商店数据', type: RefreshShopDto })
  @Expose()
  @IsObject({ message: '刷新商店数据必须是对象' })
  @ValidateNested({ message: '刷新商店数据格式不正确' })
  @Type(() => RefreshShopDto)
  refreshDto: RefreshShopDto;
}

// ==================== 3. 月卡系统相关 ====================

/**
 * 购买月卡Payload DTO
 * @MessagePattern('shop.buyMonthCard')
 * 基于真实接口结构：{ characterId: string; serverId: string; buyDto: BuyMonthCardDto; injectedContext?: InjectedContext }
 */
export class BuyMonthCardPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '服务器ID', example: 'server_001' })
  @Expose()
  @IsString({ message: '服务器ID必须是字符串' })
  @Length(1, 50, { message: '服务器ID长度必须在1-50个字符之间' })
  serverId: string;

  @ApiProperty({ description: '购买月卡数据', type: BuyMonthCardDto })
  @Expose()
  @IsObject({ message: '购买月卡数据必须是对象' })
  @ValidateNested({ message: '购买月卡数据格式不正确' })
  @Type(() => BuyMonthCardDto)
  buyDto: BuyMonthCardDto;
}

/**
 * 领取月卡奖励Payload DTO
 * @MessagePattern('shop.claimMonthCard')
 * 基于真实接口结构：{ claimDto: ClaimMonthCardDto; injectedContext?: InjectedContext }
 */
export class ClaimMonthCardPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '领取月卡奖励数据', type: ClaimMonthCardDto })
  @Expose()
  @IsObject({ message: '领取月卡奖励数据必须是对象' })
  @ValidateNested({ message: '领取月卡奖励数据格式不正确' })
  @Type(() => ClaimMonthCardDto)
  claimDto: ClaimMonthCardDto;
}

// ==================== 4. VIP商店相关 ====================

/**
 * 获取VIP商店Payload DTO
 * @MessagePattern('shop.getVipShop')
 * 基于真实接口结构：{ vipShopDto: VipShopDto; serverId: string; injectedContext?: InjectedContext }
 */
export class GetVipShopPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: 'VIP商店查询数据', type: VipShopDto })
  @Expose()
  @IsObject({ message: 'VIP商店查询数据必须是对象' })
  @ValidateNested({ message: 'VIP商店查询数据格式不正确' })
  @Type(() => VipShopDto)
  vipShopDto: VipShopDto;

  @ApiProperty({ description: '服务器ID', example: 'server_001' })
  @Expose()
  @IsString({ message: '服务器ID必须是字符串' })
  @Length(1, 50, { message: '服务器ID长度必须在1-50个字符之间' })
  serverId: string;
}

// ==================== 5. 限时商店相关 ====================

/**
 * 获取限时商店Payload DTO
 * @MessagePattern('shop.getLimitShop')
 * 基于真实接口结构：{ limitShopDto: LimitShopDto; serverId: string; injectedContext?: InjectedContext }
 */
export class GetLimitShopPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '限时商店查询数据', type: LimitShopDto })
  @Expose()
  @IsObject({ message: '限时商店查询数据必须是对象' })
  @ValidateNested({ message: '限时商店查询数据格式不正确' })
  @Type(() => LimitShopDto)
  limitShopDto: LimitShopDto;

  @ApiProperty({ description: '服务器ID', example: 'server_001' })
  @Expose()
  @IsString({ message: '服务器ID必须是字符串' })
  @Length(1, 50, { message: '服务器ID长度必须在1-50个字符之间' })
  serverId: string;
}

// ==================== 6. 购买历史和统计相关 ====================

/**
 * 获取购买历史Payload DTO
 * @MessagePattern('shop.getPurchaseHistory')
 * 基于真实接口结构：{ query: GetPurchaseHistoryDto; injectedContext?: InjectedContext }
 */
export class GetPurchaseHistoryPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '购买历史查询条件', type: GetPurchaseHistoryDto })
  @Expose()
  @IsObject({ message: '购买历史查询条件必须是对象' })
  @ValidateNested({ message: '购买历史查询条件格式不正确' })
  @Type(() => GetPurchaseHistoryDto)
  query: GetPurchaseHistoryDto;
}

/**
 * 获取商店统计Payload DTO
 * @MessagePattern('shop.getStats')
 * 基于真实接口结构：{ query: ShopStatsDto; injectedContext?: InjectedContext }
 */
export class GetShopStatsPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '商店统计查询条件', type: ShopStatsDto })
  @Expose()
  @IsObject({ message: '商店统计查询条件必须是对象' })
  @ValidateNested({ message: '商店统计查询条件格式不正确' })
  @Type(() => ShopStatsDto)
  query: ShopStatsDto;
}

// ==================== 7. 管理接口相关 ====================

/**
 * 批量刷新商店Payload DTO
 * @MessagePattern('shop.batchRefresh')
 * 基于真实接口结构：{ cycle: RefreshCycle; injectedContext?: InjectedContext }
 */
export class BatchRefreshShopsPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '刷新周期', enum: RefreshCycle, example: RefreshCycle.DAILY })
  @Expose()
  @IsEnum(RefreshCycle, { message: '刷新周期必须是有效的枚举值' })
  cycle: RefreshCycle;
}
