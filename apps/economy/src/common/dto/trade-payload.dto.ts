/**
 * Trade模块的Payload DTO定义
 * 
 * 为trade.controller.ts中的所有@MessagePattern接口生成对应的Payload DTO
 * 所有DTO都继承自BasePayloadDto，基于真实的接口数据结构
 * 包含详细的校验规则和@Expose()装饰器
 */

import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsObject, Length } from 'class-validator';
import { Expose } from 'class-transformer';
import { BasePayloadDto } from '@libs/common/dto/base-payload.dto';

// ==================== 1. 交易创建相关 ====================

/**
 * 创建交易Payload DTO
 * @MessagePattern('trade.create')
 * 基于真实接口结构：{ tradeDto: any; injectedContext?: InjectedContext }
 */
export class CreateTradePayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '交易数据', type: 'object', example: { sellerId: 'char_12345', buyerId: 'char_67890', itemId: 1001, price: 1000 } })
  @Expose()
  @IsObject({ message: '交易数据必须是对象' })
  tradeDto: any;
}

// ==================== 2. 交易确认相关 ====================

/**
 * 确认交易Payload DTO
 * @MessagePattern('trade.confirm')
 * 基于真实接口结构：{ tradeId: string; injectedContext?: InjectedContext }
 */
export class ConfirmTradePayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '交易ID', example: 'trade_12345' })
  @Expose()
  @IsString({ message: '交易ID必须是字符串' })
  @Length(1, 50, { message: '交易ID长度必须在1-50个字符之间' })
  tradeId: string;
}
