/**
 * 联赛转播控制器
 * 基于old项目relay.js接口迁移
 */

import { Controller, Logger } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { Cacheable, CacheEvict } from '@libs/redis';
import { RelayService } from './relay.service';

import { InjectedContext } from '@libs/common/types';

@Controller()
export class RelayController {
  private readonly logger = new Logger(RelayController.name);

  constructor(private readonly relayService: RelayService) {}

  /**
   * 获取联赛转播信息
   */
  @MessagePattern('relay.getInfo')
  @Cacheable({
    key: 'relay:info:#{payload.uid}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 300
  })
  async getRelayInfo(@Payload() payload: { uid: string; serverId: string; injectedContext?: InjectedContext }) {
    this.logger.log(`获取联赛转播信息: ${payload.uid}`);
    const relayInfo = await this.relayService.getRelayInfo(payload.uid, payload.serverId);
    return {
      code: 0,
      message: '获取成功',
      data: relayInfo,
    };
  }

  /**
   * 购买联赛转播
   * 对应old项目中的buyRelay方法
   */
  @MessagePattern('relay.buy')
  @CacheEvict({
    key: 'relay:info:#{payload.uid}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async buyRelay(@Payload() payload: { uid: string; serverId: string; injectedContext?: InjectedContext }) {
    this.logger.log(`购买联赛转播: ${payload.uid}`);
    const result = await this.relayService.buyRelay(payload.uid, payload.serverId);
    return {
      code: 0,
      message: '购买成功',
      data: result,
    };
  }

  /**
   * 商城兑换
   * 对应old项目中的convertibility方法
   */
  @MessagePattern('relay.convert')
  @CacheEvict({
    key: 'relay:info:#{payload.uid}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async convertibility(@Payload() payload: { uid: string; serverId: string; id: number; injectedContext?: InjectedContext }) {
    this.logger.log(`商城兑换: ${payload.uid}, 兑换ID: ${payload.id}`);
    const result = await this.relayService.convertibility(
      payload.uid, 
      payload.serverId, 
      payload.id
    );
    return {
      code: 0,
      message: '兑换成功',
      data: result,
    };
  }

  /**
   * 领取奖励
   * 对应old项目中的receiveAward方法
   */
  @MessagePattern('relay.receiveAward')
  @CacheEvict({
    key: 'relay:info:#{payload.uid}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async receiveAward(@Payload() payload: { uid: string; serverId: string; awardId: number; injectedContext?: InjectedContext }) {
    this.logger.log(`领取转播奖励: ${payload.uid}, 奖励ID: ${payload.awardId}`);
    const result = await this.relayService.receiveAward(
      payload.uid, 
      payload.serverId, 
      payload.awardId
    );
    return {
      code: 0,
      message: '奖励领取成功',
      data: result,
    };
  }

  /**
   * 一键领取所有奖励
   * 对应old项目中的receiveAllAward方法
   */
  @MessagePattern('relay.receiveAllAward')
  @CacheEvict({
    key: 'relay:info:#{payload.uid}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async receiveAllAward(@Payload() payload: { uid: string; serverId: string; injectedContext?: InjectedContext }) {
    this.logger.log(`一键领取转播奖励: ${payload.uid}`);
    const result = await this.relayService.receiveAllAward(payload.uid, payload.serverId);
    return {
      code: 0,
      message: '奖励领取成功',
      data: result,
    };
  }

  /**
   * 添加积分（内部接口）
   */
  @MessagePattern('relay.addIntegral')
  @CacheEvict({
    key: 'relay:info:#{payload.uid}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async addIntegral(@Payload() payload: { uid: string; serverId: string; amount: number; injectedContext?: InjectedContext }) {
    this.logger.log(`添加转播积分: ${payload.uid}, 数量: ${payload.amount}`);
    const result = await this.relayService.addIntegral(
      payload.uid, 
      payload.serverId, 
      payload.amount
    );
    return {
      code: 0,
      message: '积分添加成功',
      data: result,
    };
  }

  /**
   * 获取积分排行榜
   */
  @MessagePattern('relay.getRanking')
  @Cacheable({
    key: 'relay:ranking',
    dataType: 'global',
    ttl: 600
  })
  async getIntegralRanking(@Payload() payload: { limit?: number; injectedContext?: InjectedContext }) {
    this.logger.log(`获取积分排行榜: 限制 ${payload.limit || 100}`);
    // TODO: 实现积分排行榜逻辑
    return {
      code: 0,
      message: '获取成功',
      data: {
        rankings: [],
        limit: payload.limit || 100,
      },
    };
  }

  /**
   * 获取转播统计信息（管理接口）
   */
  @MessagePattern('relay.getStats')
  async getRelayStats(@Payload() payload: { adminToken?: string; injectedContext?: InjectedContext }) {
    this.logger.log('获取转播统计信息');
    // TODO: 验证管理员权限
    // const stats = await this.relayService.getRelayStats();
    return {
      code: 0,
      message: '获取成功',
      data: {
        totalPlayers: 0,
        activePlayers: 0,
        totalIntegral: 0,
        totalAwardsReceived: 0,
      },
    };
  }
}
