/**
 * 兑换大厅控制器
 * 基于old项目exchangeHall.js接口迁移
 */

import { Controller, Logger } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { Cacheable, CacheEvict } from '@libs/redis';
import { ExchangeService } from './exchange.service';

import {
  CompoundItemPayloadDto,
  DecomposeItemPayloadDto,
  ExchangeItemPayloadDto,
  GetExchangeInfoPayloadDto,
  GetExchangeStatsPayloadDto,
  RefreshExchangeHallPayloadDto
} from "@economy/common/dto/exchange-payload.dto";

@Controller()
export class ExchangeController {
  private readonly logger = new Logger(ExchangeController.name);

  constructor(private readonly exchangeService: ExchangeService) {}

  /**
   * 获取兑换大厅信息
   * 对应old项目中的getExchangeHallInfo方法
   */
  @MessagePattern('exchange.getInfo')
  @Cacheable({
    key: 'exchange:info:#{payload.uid}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 300
  })
  async getExchangeHallInfo(@Payload() payload: GetExchangeInfoPayloadDto) {
    this.logger.log(`获取兑换大厅信息: ${payload.uid}`);
    const exchangeInfo = await this.exchangeService.getExchangeHallInfo(payload.uid, payload.serverId);
    return {
      code: 0,
      message: '获取成功',
      data: exchangeInfo,
    };
  }

  /**
   * 合成物品
   * 对应old项目中的compoundItem方法
   */
  @MessagePattern('exchange.compound')
  @CacheEvict({
    key: 'exchange:info:#{payload.uid}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async compoundItem(@Payload() payload: CompoundItemPayloadDto) {
    this.logger.log(`合成物品: ${payload.uid}, 物品: ${payload.resId}, 数量: ${payload.num}`);
    const result = await this.exchangeService.compoundItem(
      payload.uid, 
      payload.serverId, 
      payload.resId,
      payload.num
    );
    return {
      code: 0,
      message: '合成成功',
      data: result,
    };
  }

  /**
   * 分解物品
   * 对应old项目中的decomposeItem方法
   */
  @MessagePattern('exchange.decompose')
  @CacheEvict({
    key: 'exchange:info:#{payload.uid}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async decomposeItem(@Payload() payload: DecomposeItemPayloadDto) {
    this.logger.log(`分解物品: ${payload.uid}, 物品: ${payload.resId}, 数量: ${payload.num}`);
    const result = await this.exchangeService.decomposeItem(
      payload.uid, 
      payload.serverId, 
      payload.resId,
      payload.num
    );
    return {
      code: 0,
      message: '分解成功',
      data: result,
    };
  }

  /**
   * 刷新兑换大厅
   * 对应old项目中的flushExchangeHall方法
   */
  @MessagePattern('exchange.refresh')
  @CacheEvict({
    key: 'exchange:info:#{payload.uid}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async flushExchangeHall(@Payload() payload: RefreshExchangeHallPayloadDto) {
    this.logger.log(`刷新兑换大厅: ${payload.uid}, 类型: ${payload.type}`);
    const result = await this.exchangeService.flushExchangeHall(
      payload.uid, 
      payload.serverId, 
      payload.type,
      payload.teamId
    );
    return {
      code: 0,
      message: '刷新成功',
      data: result,
    };
  }

  /**
   * 兑换物品
   * 对应old项目中的exchangeItem方法
   */
  @MessagePattern('exchange.exchangeItem')
  @CacheEvict({
    key: 'exchange:info:#{payload.uid}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async exchangeItem(@Payload() payload:ExchangeItemPayloadDto) {
    this.logger.log(`兑换物品: ${payload.uid}, 兑换ID: ${payload.id}`);
    const result = await this.exchangeService.exchangeItem(
      payload.uid, 
      payload.serverId, 
      payload.id
    );
    return {
      code: 0,
      message: '兑换成功',
      data: result,
    };
  }

  /**
   * 获取兑换统计信息（管理接口）
   */
  @MessagePattern('exchange.getStats')
  async getExchangeStats(@Payload() payload: GetExchangeStatsPayloadDto) {
    this.logger.log('获取兑换统计信息');
    // TODO: 验证管理员权限
    // const stats = await this.exchangeService.getExchangeStats();
    return {
      code: 0,
      message: '获取成功',
      data: {
        totalPlayers: 0,
        totalCompounds: 0,
        totalDecomposes: 0,
        totalExchanges: 0,
      },
    };
  }
}
