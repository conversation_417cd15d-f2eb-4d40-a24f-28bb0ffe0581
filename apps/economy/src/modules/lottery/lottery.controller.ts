import { Controller, Logger } from '@nestjs/common';
import {MessagePattern, Payload} from '@nestjs/microservices';
import { LotteryService } from './lottery.service';

import { InjectedContext } from '@libs/common/types';
import {
  GetLotteryConfigPayloadDto,
  GetLotteryHistoryPayloadDto,
  LotteryHeroPayloadDto
} from "@economy/common/dto/lottery-payload.dto";

/**
 * 传统抽奖系统控制器
 * 基于old项目Player.prototype.lotteryHero的功能实现
 * 
 * 核心功能：
 * - 金币抽奖（单抽/十连抽）
 * - 代币抽奖（单抽/十连抽）
 * - 权重随机算法
 * - 抽奖历史记录
 */
@Controller()
export class LotteryController {
  private readonly logger = new Logger(LotteryController.name);

  constructor(private readonly lotteryService: LotteryService) {}

  /**
   * 球员抽奖
   * 基于old项目: Player.prototype.lotteryHero
   *
   */
  @MessagePattern('lottery.lotteryHero')
  async lotteryHero(@Payload() payload: LotteryHeroPayloadDto) {
    this.logger.log(`球员抽奖请求: ${JSON.stringify(payload)}`);

    try {
      const result = await this.lotteryService.lotteryHero(
        payload.characterId,
        payload.type,
        payload.times
      );

      this.logger.log(`球员抽奖完成: ${payload.characterId}, 结果: ${result.code}`);
      return result; // 直接返回service层的标准格式结果
    } catch (error) {
      this.logger.error('球员抽奖失败', error);
      return {
        code: -1,
        message: '抽奖系统异常',
        data: null,
      };
    }
  }

  /**
   * 获取抽奖配置信息
   *
   * @returns 抽奖配置
   * @param payload
   */
  @MessagePattern('lottery.getLotteryConfig')
  async getLotteryConfig(@Payload() payload: GetLotteryConfigPayloadDto) {
    this.logger.log(`获取抽奖配置: ${payload}`);

    try {
      const config = await this.lotteryService.getLotteryConfig();

      return {
        code: 0,
        message: '获取成功',
        data: config,
      };
    } catch (error) {
      this.logger.error('获取抽奖配置失败', error);
      return {
        code: -1,
        message: '获取配置失败',
        data: null,
      };
    }
  }

  /**
   * 获取抽奖历史记录
   * 
   * @param payload 请求参数
   * @returns 抽奖历史
   */
  @MessagePattern('lottery.getLotteryHistory')
  async getLotteryHistory(@Payload() payload: GetLotteryHistoryPayloadDto) {
    this.logger.log(`获取抽奖历史: ${payload.characterId}`);

    try {
      const history = await this.lotteryService.getLotteryHistory(
        payload.characterId,
        payload.page || 1,
        payload.limit || 10
      );

      return {
        code: 0,
        message: '获取成功',
        data: history,
      };
    } catch (error) {
      this.logger.error('获取抽奖历史失败', error);
      return {
        code: -1,
        message: '获取历史失败',
        data: null,
      };
    }
  }
}
