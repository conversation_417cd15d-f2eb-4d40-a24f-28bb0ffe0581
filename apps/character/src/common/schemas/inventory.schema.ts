/**
 * 统一的角色物品背包Schema
 * 合并item.schema.ts和inventory.schema.ts的功能
 * 基于old项目的完整设计，消除数据重复，适配Result模式
 */

import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

// ==================== 枚举定义 ====================

// 页签类型枚举 - 基于old项目bagBookMarkConfig
export enum InventoryTabType {
  EQUIPMENT = 1,    // 装备页签
  CONSUMABLE = 2,   // 消耗品页签
  MATERIAL = 3,     // 材料页签
  CARD = 4,         // 卡片页签
  GIFT = 5,         // 礼包页签
  CURRENCY = 6,     // 货币页签
}

// 物品使用类型枚举 - 基于old项目BagUseItemTypeEnum
export enum ItemUseType {
  CURRENCY = 1,     // 货币类
  CONSUME = 2,      // 消耗品类
  GIFT_BAG = 3,     // 礼包类
  EQUIPMENT = 4,    // 装备类
}

// ==================== 类型安全接口定义 ====================

/**
 * 客户端背包条目接口
 */
export interface InventorySummary {
  // 基础信息
  id: number;
  name: string;
  type: InventoryTabType;

  // 容量信息
  capacity: number;
  usedSlots: number;

  // 扩展信息
  expandCount: number;
  nextExpandCost: number;
  canExpand: boolean;

  // 物品信息（优化）
  itemCount: number;        // 替代itemIds.length，减少数据传输
  itemIds?: string[];       // 可选，按需包含

  // 时间信息
  createTime: number;
  updateTime: number;
}

/**
 * 物品使用结果接口
 */
export interface ItemUseResult {
  success: boolean;
  code: number;
  message?: string;
  rewards?: ItemReward[];
  consumedItems?: ConsumedItem[];
  updatedCurrency?: CurrencyUpdate[];
}

/**
 * 物品奖励接口
 */
export interface ItemReward {
  type: 'currency' | 'item' | 'hero' | 'experience';
  id?: number;
  amount: number;
  description?: string;
}

/**
 * 消耗物品接口
 */
export interface ConsumedItem {
  itemId: string;
  configId: number;
  quantity: number;
}

/**
 * 货币更新接口
 */
export interface CurrencyUpdate {
  currencyType: string;
  oldAmount: number;
  newAmount: number;
  changeAmount: number;
}

// ==================== Schema定义 ====================

// 物品实例 - 基于old项目newItem方法，优化字段
@Schema({ _id: false })
export class Item {
  @Prop({ required: true })
  itemId: string;                    // 物品实例UID

  @Prop({ required: true })
  configId: number;                  // 配置ID（引用libs/game-config）

  @Prop({ required: true })
  quantity: number;                    // 数量

  @Prop({ default: 0 })
  bind: number;                   // 绑定状态：0-未绑定，1-已绑定

  @Prop({ required: true })
  type: number;                   // 物品类型

  @Prop({ default: 0 })
  timeLimit: number;              // 时间限制：0xFFFF 无限制时间

  @Prop({ default: 0 })
  invalid: number;                // 是否失效：0-未失效，1-已失效

  // 新增：背包位置信息
  @Prop({ default: -1 })
  slot: number;                   // 在页签中的位置，-1表示未放入背包

  @Prop({ default: 0 })
  tabId: number;                  // 所属页签ID，0表示未分配

  @Prop({ default: Date.now })
  acquiredTime: number;           // 获得时间

  @Prop({ default: 0 })
  expireTime: number;             // 过期时间（0表示永不过期）
}

// 物品堆叠映射项 - 基于old项目configToItems结构
@Schema({ _id: false })
export class ConfigItemMapping {
  @Prop({ required: true })
  itemId: string;                // 物品ID

  @Prop({ required: true })
  configId: number;              // 物品配置ID
}

// 物品到页签的映射 - 基于old项目itemUidTotabId
@Schema({ _id: false })
export class ItemTabMapping {
  @Prop({ required: true })
  itemId: string;                // 物品ID

  @Prop({ required: true })
  tabId: number;                  // 页签ID
}


// 页签实例 - 基于old项目bag Map结构，优化虚拟字段
@Schema({ _id: false })
export class InventoryTab {
  @Prop({ required: true })
  id: number;                     // 页签ID

  @Prop({ required: true })
  name: string;                   // 页签名称

  @Prop({ required: true })
  type: InventoryTabType;         // 页签类型

  @Prop({ default: 50 })
  capacity: number;               // 当前容量

  @Prop({ default: 0 })
  expandCount: number;            // 扩展次数

  @Prop({ default: 1000 })
  nextExpandCost: number;         // 下次扩展费用

  @Prop({ default: Date.now })
  createTime: number;             // 创建时间

  @Prop({ default: Date.now })
  updateTime: number;             // 更新时间

  // 注意：usedSlots作为虚拟字段，通过计算获得，不存储在数据库中
}

// 主Schema - 统一的角色物品背包数据
@Schema({
  collection: 'inventorys',
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
})
export class Inventory {
  @Prop({ required: true, unique: true, index: true })
  characterId: string;                    // 角色ID

  // ========== 物品实例数据（来自item.schema.ts）==========
  @Prop({ type: [Item], default: [] })
  items: Item[];          // 所有物品实例

  @Prop({ type: Object, default: {} })
  configToItems: Record<string, ConfigItemMapping[]>;  // 堆叠映射表

  // ========== 背包管理数据（来自inventory.schema.ts）==========
  @Prop({ type: [InventoryTab], default: [] })
  tabs: InventoryTab[];           // 背包页签

  @Prop({ type: [ItemTabMapping], default: [] })
  itemToTab: ItemTabMapping[];    // 物品到页签的映射

  // ========== 统一的时间管理 ==========
  @Prop({ default: Date.now })
  lastActiveTime: number;         // 最后活跃时间

  @Prop({ default: Date.now })
  createTime: number;             // 创建时间

  @Prop({ default: Date.now })
  updateTime: number;             // 更新时间
}

export const InventorySchema = SchemaFactory.createForClass(Inventory);

// ==================== 索引优化 ====================
InventorySchema.index({ characterId: 1 }, { unique: true });
InventorySchema.index({ 'items.configId': 1 });  // 按配置ID查询
InventorySchema.index({ 'items.expireTime': 1 }, { sparse: true });  // 过期清理
InventorySchema.index({ 'items.tabId': 1, 'items.slot': 1 });  // 背包查询
InventorySchema.index({ lastActiveTime: 1 });  // 非活跃用户清理
InventorySchema.index({ characterId: 1, 'items.type': 1 });  // 用户特定类型查询
InventorySchema.index({ characterId: 1, 'tabs.id': 1 });  // 用户页签查询

// ==================== 方法接口定义 ====================
export interface InventoryMethods {
  // 物品管理方法
  getItem(itemId: string): Item | null;
  hasItemByConfigId(configId: number): boolean;
  getPartialStackItemId(configId: number, limitNum: number): string;
  addToConfigMapping(configId: number, itemId: string): void;
  removeFromConfigMapping(configId: number, itemId: string): void;
  removeItem(itemId: string): void;
  getItemQuantity(itemId: string): number;
  getItemQuantityByConfigId(configId: number): number;
  checkItemIsEnough(configId: number, needNum: number): boolean;
  addItemUseCount(configId: number, num: number, config: any): number;

  // 页签管理方法
  getOneTab(id: number): InventoryTab | null;
  deleteTab(id: number): boolean;
  addItemToTabMapping(itemId: string, tabId: number): void;
  removeItemFromTabMapping(itemId: string): boolean;
  getTabIdByItemId(itemId: string): number | null;
  clearItemIdToTabId(): void;

  // 背包操作方法
  addToTab(tabId: number, itemId: string, slot?: number): boolean;
  removeFromTab(tabId: number, itemId: string): boolean;
  getItemTab(itemId: string): { tabId: number; slot: number } | null;
  sortTab(tabId: number, sortType: string): void;

  // 扩展方法
  expandTab(tabId: number, expandCount: number): { success: boolean; cost: number };
  calculateExpandCost(tabId: number, expandCount: number): number;
  canExpand(tabId: number): boolean;

  // 数据转换方法
  makeInventorySummaries(): InventorySummary[];

  // 统一的业务方法
  getNextAvailableSlot(tabId: number): number;
  addItemWithStacking(configId: number, quantity: number): Promise<string[]>;
  createNewItem(configId: number, quantity: number, type: number): Item;
  getExpiredItems(): Item[];
  cleanExpiredItems(): number;
  moveItemToSlot(itemId: string, targetSlot: number): boolean;
  batchAddItems(itemsToAdd: Array<{configId: number, quantity: number}>): string[];
}

export type InventoryDocument = Inventory & Document & InventoryMethods;

// ==================== 实例方法实现 ====================

/**
 * 获取物品实例
 * 基于old项目: getItem方法
 */
InventorySchema.methods.getItem = function(itemId: string): Item | null {
  return this.items.find((item: Item) => item.itemId === itemId) || null;
};

/**
 * 检查物品是否存在
 * 基于old项目: _hasItemResID2Uid方法
 */
InventorySchema.methods.hasItemByConfigId = function(configId: number): boolean {
  return !!this.configToItems[configId.toString()] && this.configToItems[configId.toString()].length > 0;
};

/**
 * 获取指定配置ID的未满堆叠的物品UID
 * 基于old项目: _getItemNotEnoughUidByResID方法
 */
InventorySchema.methods.getPartialStackItemId = function(configId: number, limitNum: number): string {
  const configIdStr = configId.toString();
  if (!this.configToItems[configIdStr]) {
    return '';
  }

  for (const uidItem of this.configToItems[configIdStr]) {
    const item:Item = this.getItem(uidItem.itemId);
    if (item && item.quantity < limitNum) {
      return uidItem.itemId;
    }
  }

  return '';
};

/**
 * 添加物品到配置ID映射
 * 基于old项目: _addResId2Uid方法
 */
InventorySchema.methods.addToConfigMapping = function(configId: number, itemId: string): void {
  const configIdStr = configId.toString();
  if (!this.configToItems[configIdStr]) {
    this.configToItems[configIdStr] = [];
  }

  this.configToItems[configIdStr].push({
    itemId: itemId,
    configId: configId
  });
};

/**
 * 从配置ID映射中删除物品
 * 基于old项目: _delResId2Uid方法
 */
InventorySchema.methods.removeFromConfigMapping = function(configId: number, itemId: string): void {
  const configIdStr = configId.toString();
  if (!this.configToItems[configIdStr]) {
    return;
  }

  const index = this.configToItems[configIdStr].findIndex(item => item.itemId === itemId);
  if (index !== -1) {
    this.configToItems[configIdStr].splice(index, 1);

    // 如果数组为空，删除整个键
    if (this.configToItems[configIdStr].length === 0) {
      delete this.configToItems[configIdStr];
    }
  }
};

/**
 * 删除物品实例
 * 基于old项目: _delItem方法
 */
InventorySchema.methods.removeItem = function(itemId: string): void {
  const index = this.items.findIndex(item => item.itemId === itemId);
  if (index !== -1) {
    this.items.splice(index, 1);
  }
};

/**
 * 获取物品数量
 * 基于old项目: getItemNum方法
 */
InventorySchema.methods.getItemQuantity = function(itemId: string): number {
  const item = this.getItem(itemId);
  return item ? item.quantity : 0;
};

/**
 * 根据ResID获取物品总数量
 * 基于old项目: getItemNumByResID方法
 */
InventorySchema.methods.getItemQuantityByConfigId = function(configId: number): number {
  const configIdStr = configId.toString();
  if (!this.configToItems[configIdStr]) {
    return 0;
  }

  let totalNum = 0;
  for (const uidItem of this.configToItems[configIdStr]) {
    const item = this.getItem(uidItem.itemId);
    if (item) {
      totalNum += item.quantity;
    }
  }

  return totalNum;
};

/**
 * 检查是否有足够的物品
 * 基于old项目: checkItemIsEnough方法
 */
InventorySchema.methods.checkItemIsEnough = function(configId: number, needNum: number): boolean {
  const haveNum = this.getItemNumByConfigId(configId);
  return haveNum >= needNum;
};

/**
 * 计算添加物品需要的格子数
 * 基于old项目: addItemUseCount方法
 */
InventorySchema.methods.addItemUseCount = function(configId: number, num: number, config: any): number {
  if (!config) {
    return 9999999; // 最大值
  }

  const isSingle = config.isSingle;

  // 不能支持堆叠的物品，物品的数量是多少就占用多少个格子
  if (isSingle === 0) {
    return num;
  }

  const limitNum = config.isSingleParameter;
  if (!num || !limitNum || num <= 0 || limitNum <= 0) {
    return 9999999;
  }

  // 检查现有物品是否有空间
  let remainingNum = num;
  const configIdStr = configId.toString();

  if (this.configToItems[configIdStr]) {
    for (const uidItem of this.configToItems[configIdStr]) {
      const item = this.getItem(uidItem.itemId);
      if (item && item.quantity < limitNum) {
        const canAdd = limitNum - item.quantity;
        remainingNum -= Math.min(canAdd, remainingNum);
        if (remainingNum <= 0) {
          return 0; // 不需要新格子
        }
      }
    }
  }

  // 计算需要的新格子数
  return Math.ceil(remainingNum / limitNum);
};

// ==================== 页签管理方法 ====================

/**
 * 获取指定页签
 * 基于old项目: getOneBookMark方法
 */
InventorySchema.methods.getOneTab = function(id: number): InventoryTab | null {
  return this.tabs.find((entry: InventoryTab) => entry.id === id) || null;
};

/**
 * 删除指定页签
 * 基于old项目: delOneBookMark方法
 */
InventorySchema.methods.deleteTab = function(id: number): boolean {
  const index = this.tabs.findIndex((entry: InventoryTab) => entry.id === id);
  if (index !== -1) {
    this.tabs.splice(index, 1);
    return true;
  }
  return false;
};

/**
 * 添加物品到页签映射
 * 基于old项目: itemUidTotabId Map操作
 */
InventorySchema.methods.addItemToTabMapping = function(itemId: string, tabId: number): void {
  // 先检查是否已存在
  const existing = this.itemToTab.find((mapping: ItemTabMapping) => mapping.itemId === itemId);
  if (existing) {
    existing.tabId = tabId;
  } else {
    this.itemToTab.push({
      itemId: itemId,
      tabId: tabId
    });
  }
};

/**
 * 从页签映射中移除物品
 * 基于old项目: itemUidTotabId Map操作
 */
InventorySchema.methods.removeItemFromTabMapping = function(itemId: string): boolean {
  const index = this.itemToTab.findIndex((mapping: ItemTabMapping) => mapping.itemId === itemId);
  if (index !== -1) {
    this.itemToTab.splice(index, 1);
    return true;
  }
  return false;
};

/**
 * 根据物品ID获取页签ID
 * 基于old项目: itemUidTotabId Map查询
 */
InventorySchema.methods.getTabIdByItemId = function(itemId: string): number | null {
  const mapping = this.itemToTab.find((mapping: ItemTabMapping) => mapping.itemId === itemId);
  return mapping ? mapping.tabId : null;
};

/**
 * 清除所有物品映射
 * 基于old项目: _clearItemIdTotabId方法
 */
InventorySchema.methods.clearItemIdToTabId = function(): void {
  this.itemToTab = [];
};

// ==================== 背包操作方法 ====================

/**
 * 添加物品到页签
 * 基于old项目: addToTab方法，优化为统一逻辑
 */
InventorySchema.methods.addToTab = function(tabId: number, itemId: string, slot?: number): boolean {
  // 1. 查找物品实例
  const item = this.getItem(itemId);
  if (!item) return false;

  // 2. 查找页签
  const tab = this.getOneTab(tabId);
  if (!tab) return false;

  // 3. 检查容量
  const usedSlots = this.items.filter(i => i.tabId === tabId).length;
  if (usedSlots >= tab.capacity) return false;

  // 4. 分配位置
  item.tabId = tabId;
  item.slot = slot || this.getNextAvailableSlot(tabId);

  // 5. 更新映射
  this.addItemToTabMapping(itemId, tabId);

  // 6. 更新时间
  tab.updateTime = Date.now();

  return true;
};

/**
 * 从页签移除物品
 * 基于old项目: removeFromTab方法，优化为统一逻辑
 */
InventorySchema.methods.removeFromTab = function(tabId: number, itemId: string): boolean {
  const tab = this.getOneTab(tabId);
  if (!tab) {
    return false;
  }

  const item = this.getItem(itemId);
  if (!item || item.tabId !== tabId) {
    return false;
  }

  // 重置物品的页签信息
  item.tabId = 0;
  item.slot = -1;
  tab.updateTime = Date.now();

  // 从映射中移除
  this.removeItemFromTabMapping(itemId);

  return true;
};

/**
 * 在背包中查找物品
 * 返回物品所在的页签ID和位置索引
 */
InventorySchema.methods.getItemTab = function(itemId: string): { tabId: number; slot: number } | null {
  const item = this.getItem(itemId);
  if (!item || item.tabId <= 0) {
    return null;
  }

  return {
    tabId: item.tabId,
    slot: item.slot
  };
};

/**
 * 背包排序
 * 基于old项目: sortTab方法
 */
InventorySchema.methods.sortTab = function(tabId: number, sortType: string): void {
  const itemsInTab = this.items.filter(item => item.tabId === tabId);

  // 根据排序类型进行排序
  itemsInTab.sort((a, b) => {
    switch (sortType) {
      case 'configId':
        return a.configId - b.configId;
      case 'type':
        return a.type - b.type;
      case 'quantity':
        return b.quantity - a.quantity; // 数量降序
      case 'acquiredTime':
        return b.acquiredTime - a.acquiredTime; // 获得时间降序
      default:
        return a.slot - b.slot; // 默认按位置排序
    }
  });

  // 重新分配槽位
  itemsInTab.forEach((item, index) => {
    item.slot = index + 1;
  });

  // 更新页签时间
  const tab = this.getOneTab(tabId);
  if (tab) {
    tab.updateTime = Date.now();
  }
};

// ==================== 扩展方法 ====================

/**
 * 扩展背包
 * 基于old项目: expandTab方法
 */
InventorySchema.methods.expandTab = function(tabId: number, expandCount: number): { success: boolean; cost: number } {
  const tab = this.getOneTab(tabId);
  if (!tab) {
    return { success: false, cost: 0 };
  }

  if (!this.canExpand(tabId)) {
    return { success: false, cost: 0 };
  }

  const cost = this.calculateExpandCost(tabId, expandCount);

  // TODO: 检查玩家是否有足够的货币

  tab.capacity += expandCount * 10; // 每次扩展10个槽位
  tab.expandCount += expandCount;
  tab.nextExpandCost = this.calculateExpandCost(tabId, 1);
  tab.updateTime = Date.now();

  return { success: true, cost };
};

/**
 * 计算扩展费用
 * 基于old项目: calculateExpandCost方法
 */
InventorySchema.methods.calculateExpandCost = function(tabId: number, expandCount: number): number {
  const tab = this.getOneTab(tabId);
  if (!tab) {
    return 0;
  }

  const baseCost = 1000; // 基础费用
  const multiplier = 1.5; // 倍数
  let totalCost = 0;

  for (let i = 0; i < expandCount; i++) {
    const currentExpand = tab.expandCount + i;
    const cost = Math.floor(baseCost * Math.pow(multiplier, currentExpand));
    totalCost += cost;
  }

  return totalCost;
};

/**
 * 检查是否可以扩展
 * 基于old项目: canExpand方法
 */
InventorySchema.methods.canExpand = function(tabId: number): boolean {
  const tab = this.getOneTab(tabId);
  if (!tab) {
    return false;
  }

  return tab.capacity < 200; // 假设最大容量为200
};

// ==================== 数据转换方法 ====================

/**
 * 生成客户端背包数据
 * 基于old项目: makeClientBagList方法，使用严格类型
 */
InventorySchema.methods.makeInventorySummaries = function(includeItemIds: boolean = true): InventorySummary[] {
  const summaries: InventorySummary[] = [];

  for (const tab of this.tabs) {
    const tabItems = this.items.filter(item => item.tabId === tab.id);
    const usedSlots = tabItems.length;

    const summary: InventorySummary = {
      id: tab.id,
      name: tab.name,
      type: tab.type,
      capacity: tab.capacity,
      usedSlots: usedSlots,
      expandCount: tab.expandCount,
      nextExpandCost: tab.nextExpandCost,
      canExpand: tab.capacity < 200,
      itemCount: usedSlots,
      createTime: tab.createTime,
      updateTime: tab.updateTime,
    };

    // 按需包含物品ID列表
    if (includeItemIds) {
      summary.itemIds = tabItems
        .sort((a, b) => a.slot - b.slot)
        .map(item => item.itemId);
    }

    summaries.push(summary);
  }

  return summaries;
};

// ==================== 统一的业务方法 ====================
/**
 * 获取页签中下一个可用的槽位
 */
InventorySchema.methods.getNextAvailableSlot = function(tabId: number): number {
  const usedSlots = this.items
    .filter(i => i.tabId === tabId)
    .map(i => i.slot)
    .sort((a, b) => a - b);

  for (let i = 1; i <= 200; i++) {
    if (!usedSlots.includes(i)) {
      return i;
    }
  }
  return -1; // 无可用位置
};

/**
 * 智能堆叠添加物品
 * 统一的物品添加逻辑，支持自动堆叠
 */
InventorySchema.methods.addItemWithStacking = async function(configId: number, quantity: number): Promise<string[]> {
  // 注意：这里需要获取配置，在实际使用时需要注入配置服务
  // const config = await this.getItemConfig(configId);
  // 这里先使用占位符逻辑

  const addedIds: string[] = [];
  let remainingQuantity = quantity;

  // 1. 尝试堆叠到现有物品（假设可堆叠）
  const existingItems = this.items.filter(item =>
    item.configId === configId &&
    item.quantity < 999 // 假设最大堆叠数为999
  );

  for (const item of existingItems) {
    const canStack = 999 - item.quantity;
    const stackAmount = Math.min(canStack, remainingQuantity);

    item.quantity += stackAmount;
    remainingQuantity -= stackAmount;
    addedIds.push(item.itemId);

    if (remainingQuantity <= 0) break;
  }

  // 2. 创建新物品实例
  while (remainingQuantity > 0) {
    const newQuantity = Math.min(remainingQuantity, 999);
    const newItem = this.createNewItem(configId, newQuantity, 1); // 假设类型为1
    this.items.push(newItem);
    this.addToConfigMapping(configId, newItem.itemId);

    addedIds.push(newItem.itemId);
    remainingQuantity -= newQuantity;
  }

  return addedIds;
};

/**
 * 创建新物品实例
 */
InventorySchema.methods.createNewItem = function(configId: number, quantity: number, type: number): Item {
  const uid = `item_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

  return {
    itemId: uid,
    configId: configId,
    quantity: quantity,
    bind: 0,
    type: type,
    timeLimit: 0,
    invalid: 0,
    slot: -1,
    tabId: 0,
    acquiredTime: Date.now(),
    expireTime: 0
  };
};

/**
 * 获取过期物品
 */
InventorySchema.methods.getExpiredItems = function(): Item[] {
  const now = Date.now();
  return this.items.filter(item => item.expireTime > 0 && item.expireTime < now);
};

/**
 * 清理过期物品
 */
InventorySchema.methods.cleanExpiredItems = function(): number {
  const expiredItems = this.getExpiredItems();
  let cleanedCount = 0;

  for (const expiredItem of expiredItems) {
    this.removeItem(expiredItem.itemId);
    this.removeFromConfigMapping(expiredItem.configId, expiredItem.itemId);
    this.removeItemFromTabMapping(expiredItem.itemId);
    cleanedCount++;
  }

  return cleanedCount;
};

/**
 * 移动物品到指定槽位
 */
InventorySchema.methods.moveItemToSlot = function(itemId: string, targetSlot: number): boolean {
  const item = this.getItem(itemId);
  if (!item || item.tabId <= 0) return false;

  // 检查目标位置是否被占用
  const existingItem = this.items.find(i =>
    i.tabId === item.tabId &&
    i.slot === targetSlot &&
    i.itemId !== itemId
  );

  if (existingItem) {
    return false; // 位置被占用
  }

  item.slot = targetSlot;
  return true;
};

/**
 * 批量添加物品
 */
InventorySchema.methods.batchAddItems = function(itemsToAdd: Array<{configId: number, quantity: number}>): string[] {
  const addedIds: string[] = [];

  for (const {configId, quantity} of itemsToAdd) {
    // 简化版批量添加，实际使用时应该调用addItemWithStacking
    const newItem = this.createNewItem(configId, quantity, 1);
    this.items.push(newItem);
    this.addToConfigMapping(configId, newItem.itemId);
    addedIds.push(newItem.itemId);
  }

  return addedIds;
};
