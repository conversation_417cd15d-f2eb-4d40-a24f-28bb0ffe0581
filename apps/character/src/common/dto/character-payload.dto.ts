/**
 * Character模块的Payload DTO定义
 * 
 * 为character.controller.ts中的所有@MessagePattern接口生成对应的Payload DTO
 * 所有DTO都继承自BasePayloadDto，包含详细的校验规则和@Expose()装饰器
 */

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsNumber, IsOptional, ValidateNested, Min, Max, Length, IsObject, IsArray } from 'class-validator';
import { Expose, Type } from 'class-transformer';
import { BasePayloadDto, IdDtoPayloadDto, DtoPayloadDto } from '@libs/common/dto/base-payload.dto';
import {
  CreateCharacterDto,
  UpdateCharacterDto,
  LoginCharacterDto,
  CurrencyOperationDto,
  BuyEnergyDto,
  LevelUpDto,
  GetCharacterListDto
} from './character.dto';

// ==================== 1. 角色创建相关 ====================

/**
 * 创建角色Payload DTO
 * @MessagePattern('character.create')
 * payload结构: { createDto: CreateCharacterDto, injectedContext?: InjectedContext }
 */
export class CreateCharacterPayloadDto extends DtoPayloadDto<CreateCharacterDto> {
  @ApiProperty({ description: '角色创建数据', type: CreateCharacterDto })
  @Expose()
  @ValidateNested({ message: '角色创建数据格式不正确' })
  @Type(() => CreateCharacterDto)
  dto: CreateCharacterDto;

  // 为了更清晰，提供别名
  get createDto(): CreateCharacterDto {
    return this.dto;
  }
}

/**
 * 角色登录Payload DTO
 * @MessagePattern('character.login')
 * payload结构: { loginDto: LoginCharacterDto, injectedContext?: InjectedContext }
 */
export class LoginCharacterPayloadDto extends DtoPayloadDto<LoginCharacterDto> {
  @ApiProperty({ description: '角色登录数据', type: LoginCharacterDto })
  @Expose()
  @ValidateNested({ message: '角色登录数据格式不正确' })
  @Type(() => LoginCharacterDto)
  dto: LoginCharacterDto;

  // 为了更清晰，提供别名
  get loginDto(): LoginCharacterDto {
    return this.dto;
  }
}

// ==================== 2. 角色基础操作相关 ====================

/**
 * 角色登出Payload DTO
 * @MessagePattern('character.logout')
 * payload结构: { characterId: string, serverId?: string, injectedContext?: InjectedContext }
 */
export class LogoutCharacterPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;
}

/**
 * 获取角色信息Payload DTO
 * @MessagePattern('character.getInfo')
 * payload结构: { characterId: string, serverId?: string, injectedContext?: InjectedContext }
 */
export class GetCharacterInfoPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;
}

/**
 * 更新角色信息Payload DTO
 * @MessagePattern('character.update')
 * payload结构: { characterId: string, updateDto: UpdateCharacterDto, serverId?: string, injectedContext?: InjectedContext }
 */
export class UpdateCharacterPayloadDto extends IdDtoPayloadDto<UpdateCharacterDto> {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  id: string;

  get characterId(): string {
    return this.id;
  }

  @ApiProperty({ description: '角色更新数据', type: UpdateCharacterDto })
  @Expose()
  @ValidateNested({ message: '角色更新数据格式不正确' })
  @Type(() => UpdateCharacterDto)
  dto: UpdateCharacterDto;

  get updateDto(): UpdateCharacterDto {
    return this.dto;
  }
}

/**
 * 获取角色列表Payload DTO
 * @MessagePattern('character.getList')
 * payload结构: { query: GetCharacterListDto, injectedContext?: InjectedContext }
 */
export class GetCharacterListPayloadDto extends DtoPayloadDto<GetCharacterListDto> {
  @ApiProperty({ description: '查询参数', type: GetCharacterListDto })
  @Expose()
  @ValidateNested({ message: '查询参数格式不正确' })
  @Type(() => GetCharacterListDto)
  dto: GetCharacterListDto;

  get query(): GetCharacterListDto {
    return this.dto;
  }
}

// ==================== 3. 货币和资源相关 ====================

/**
 * 添加货币Payload DTO
 * @MessagePatternInternal('character.currency.add')
 * payload结构: { characterId: string, currencyDto: CurrencyOperationDto, serverId?: string, injectedContext?: InjectedContext }
 */
export class AddCurrencyPayloadDto extends IdDtoPayloadDto<CurrencyOperationDto> {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  id: string;

  get characterId(): string {
    return this.id;
  }

  @ApiProperty({ description: '货币操作数据', type: CurrencyOperationDto })
  @Expose()
  @ValidateNested({ message: '货币操作数据格式不正确' })
  @Type(() => CurrencyOperationDto)
  dto: CurrencyOperationDto;

  get currencyDto(): CurrencyOperationDto {
    return this.dto;
  }
}

/**
 * 扣除货币Payload DTO
 * @MessagePatternInternal('character.currency.subtract')
 * payload结构: { characterId: string, currencyDto: CurrencyOperationDto, serverId?: string, injectedContext?: InjectedContext }
 */
export class SubtractCurrencyPayloadDto extends IdDtoPayloadDto<CurrencyOperationDto> {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  id: string;

  get characterId(): string {
    return this.id;
  }

  @ApiProperty({ description: '货币操作数据', type: CurrencyOperationDto })
  @Expose()
  @ValidateNested({ message: '货币操作数据格式不正确' })
  @Type(() => CurrencyOperationDto)
  dto: CurrencyOperationDto;

  get currencyDto(): CurrencyOperationDto {
    return this.dto;
  }
}

/**
 * 购买体力Payload DTO
 * @MessagePattern('character.energy.buy')
 * payload结构: { characterId: string, buyDto: BuyEnergyDto, serverId?: string, injectedContext?: InjectedContext }
 */
export class BuyEnergyPayloadDto extends IdDtoPayloadDto<BuyEnergyDto> {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  id: string;

  get characterId(): string {
    return this.id;
  }

  @ApiProperty({ description: '购买体力数据', type: BuyEnergyDto })
  @Expose()
  @ValidateNested({ message: '购买体力数据格式不正确' })
  @Type(() => BuyEnergyDto)
  dto: BuyEnergyDto;

  get buyDto(): BuyEnergyDto {
    return this.dto;
  }
}

/**
 * 角色升级Payload DTO
 * @MessagePattern('character.levelup')
 * payload结构: { characterId: string, levelUpDto: LevelUpDto, serverId?: string, injectedContext?: InjectedContext }
 */
export class LevelUpPayloadDto extends IdDtoPayloadDto<LevelUpDto> {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  id: string;

  get characterId(): string {
    return this.id;
  }

  @ApiProperty({ description: '升级数据', type: LevelUpDto })
  @Expose()
  @ValidateNested({ message: '升级数据格式不正确' })
  @Type(() => LevelUpDto)
  dto: LevelUpDto;

  get levelUpDto(): LevelUpDto {
    return this.dto;
  }
}
