/**
 * Character模块的Payload DTO定义
 *
 * 为character.controller.ts中的所有@MessagePattern接口生成对应的Payload DTO
 * 所有DTO都继承自BasePayloadDto，直接包含字段而不嵌套dto
 * 包含详细的校验规则和@Expose()装饰器
 */

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsNumber, IsOptional, Min, Max, Length, IsObject, IsArray, IsBoolean } from 'class-validator';
import { Expose } from 'class-transformer';
import { BasePayloadDto } from '@libs/common/dto/base-payload.dto';

// ==================== 1. 角色创建相关 ====================

/**
 * 创建角色Payload DTO
 * @MessagePattern('character.create')
 * 扩展CreateCharacterDto，合并BasePayloadDto内容
 */
export class CreateCharacterPayloadDto extends BasePayloadDto {
  @ApiPropertyOptional({ description: '角色ID（由Auth服务提供）', example: 'char_12345' })
  @Expose()
  @IsOptional()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId?: string;

  @ApiProperty({ description: '用户ID', example: 'user_67890' })
  @Expose()
  @IsString({ message: '用户ID必须是字符串' })
  @Length(1, 50, { message: '用户ID长度必须在1-50个字符之间' })
  userId: string;

  @ApiProperty({ description: '区服ID', example: 'server_001' })
  @Expose()
  @IsString({ message: '区服ID必须是字符串' })
  @Length(1, 20, { message: '区服ID长度必须在1-20个字符之间' })
  serverId: string;

  @ApiProperty({ description: '平台外部ID', example: 'openid_123456' })
  @Expose()
  @IsString({ message: '平台外部ID必须是字符串' })
  @Length(1, 100, { message: '平台外部ID长度必须在1-100个字符之间' })
  openId: string;

  @ApiProperty({ description: '角色名称', example: 'HeroPlayer', minLength: 2, maxLength: 20 })
  @Expose()
  @IsString({ message: '角色名称必须是字符串' })
  @Length(2, 20, { message: '角色名称长度必须在2-20个字符之间' })
  name: string;

  @ApiPropertyOptional({ description: '头像URL', example: 'avatar.jpg' })
  @Expose()
  @IsOptional()
  @IsString({ message: '头像必须是字符串' })
  @Length(0, 200, { message: '头像URL长度不能超过200个字符' })
  avatar?: string;

  @ApiPropertyOptional({ description: '头像ID', example: 1 })
  @Expose()
  @IsOptional()
  @IsNumber({}, { message: '头像ID必须是数字' })
  @Min(0, { message: '头像ID不能小于0' })
  @Max(999, { message: '头像ID不能大于999' })
  faceIcon?: number;
}

/**
 * 角色登录Payload DTO
 * @MessagePattern('character.login')
 * 扩展LoginCharacterDto，合并BasePayloadDto内容
 */
export class LoginCharacterPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '用户ID', example: 'user_67890' })
  @Expose()
  @IsString({ message: '用户ID必须是字符串' })
  @Length(1, 50, { message: '用户ID长度必须在1-50个字符之间' })
  userId: string;

  @ApiProperty({ description: '区服ID', example: 'server_001' })
  @Expose()
  @IsString({ message: '区服ID必须是字符串' })
  @Length(1, 20, { message: '区服ID长度必须在1-20个字符之间' })
  serverId: string;

  @ApiPropertyOptional({ description: '前端ID（可选）', example: 'frontend_001' })
  @Expose()
  @IsOptional()
  @IsString({ message: '前端ID必须是字符串' })
  @Length(1, 50, { message: '前端ID长度必须在1-50个字符之间' })
  frontendId?: string;

  @ApiPropertyOptional({ description: '会话ID（可选）', example: 'session_123' })
  @Expose()
  @IsOptional()
  @IsString({ message: '会话ID必须是字符串' })
  @Length(1, 100, { message: '会话ID长度必须在1-100个字符之间' })
  sessionId?: string;
}

// ==================== 2. 角色基础操作相关 ====================

/**
 * 角色登出Payload DTO
 * @MessagePattern('character.logout')
 */
export class LogoutCharacterPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;
}

/**
 * 获取角色信息Payload DTO
 * @MessagePattern('character.getInfo')
 */
export class GetCharacterInfoPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;
}

/**
 * 更新角色信息Payload DTO
 * @MessagePattern('character.update')
 * 扩展UpdateCharacterDto，合并BasePayloadDto内容
 */
export class UpdateCharacterPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiPropertyOptional({ description: '角色名称', example: 'NewPlayerName' })
  @Expose()
  @IsOptional()
  @IsString({ message: '角色名称必须是字符串' })
  @Length(2, 20, { message: '角色名称长度必须在2-20个字符之间' })
  name?: string;

  @ApiPropertyOptional({ description: '头像ID', example: 2 })
  @Expose()
  @IsOptional()
  @IsNumber({}, { message: '头像ID必须是数字' })
  @Min(0, { message: '头像ID不能小于0' })
  @Max(999, { message: '头像ID不能大于999' })
  faceIcon?: number;

  @ApiPropertyOptional({ description: '等级', example: 10 })
  @Expose()
  @IsOptional()
  @IsNumber({}, { message: '等级必须是数字' })
  @Min(1, { message: '等级不能小于1' })
  @Max(100, { message: '等级不能大于100' })
  level?: number;

  @ApiPropertyOptional({ description: '经验值', example: 1500 })
  @Expose()
  @IsOptional()
  @IsNumber({}, { message: '经验值必须是数字' })
  @Min(0, { message: '经验值不能小于0' })
  exp?: number;

  @ApiPropertyOptional({ description: '体力', example: 80 })
  @Expose()
  @IsOptional()
  @IsNumber({}, { message: '体力必须是数字' })
  @Min(0, { message: '体力不能小于0' })
  @Max(100, { message: '体力不能大于100' })
  energy?: number;
}

/**
 * 获取角色列表Payload DTO
 * @MessagePattern('character.getList')
 * 扩展GetCharacterListDto，合并BasePayloadDto内容
 */
export class GetCharacterListPayloadDto extends BasePayloadDto {
  @ApiPropertyOptional({ description: '页码', example: 1 })
  @Expose()
  @IsOptional()
  @IsNumber({}, { message: '页码必须是数字' })
  @Min(1, { message: '页码不能小于1' })
  page?: number;

  @ApiPropertyOptional({ description: '每页数量', example: 20 })
  @Expose()
  @IsOptional()
  @IsNumber({}, { message: '每页数量必须是数字' })
  @Min(1, { message: '每页数量不能小于1' })
  @Max(100, { message: '每页数量不能大于100' })
  limit?: number;

  @ApiPropertyOptional({ description: '角色名称（模糊搜索）', example: 'Player' })
  @Expose()
  @IsOptional()
  @IsString({ message: '角色名称必须是字符串' })
  @Length(1, 20, { message: '角色名称长度必须在1-20个字符之间' })
  name?: string;

  @ApiPropertyOptional({ description: '最小等级', example: 1 })
  @Expose()
  @IsOptional()
  @IsNumber({}, { message: '最小等级必须是数字' })
  @Min(1, { message: '最小等级不能小于1' })
  minLevel?: number;

  @ApiPropertyOptional({ description: '最大等级', example: 100 })
  @Expose()
  @IsOptional()
  @IsNumber({}, { message: '最大等级必须是数字' })
  @Min(1, { message: '最大等级不能小于1' })
  @Max(100, { message: '最大等级不能大于100' })
  maxLevel?: number;
}

// ==================== 3. 货币和资源相关 ====================

/**
 * 添加货币Payload DTO
 * @MessagePatternInternal('character.currency.add')
 * 扩展CurrencyOperationDto，合并BasePayloadDto内容
 */
export class AddCurrencyPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '货币类型', example: 'gold', enum: ['cash', 'gold', 'energy', 'worldCoin', 'chip', 'integral'] })
  @Expose()
  @IsString({ message: '货币类型必须是字符串' })
  @Length(1, 20, { message: '货币类型长度必须在1-20个字符之间' })
  currencyType: string;

  @ApiProperty({ description: '货币数量', example: 1000 })
  @Expose()
  @IsNumber({}, { message: '货币数量必须是数字' })
  @Min(1, { message: '货币数量必须大于0' })
  @Max(999999999, { message: '货币数量不能超过999999999' })
  amount: number;

  @ApiPropertyOptional({ description: '操作原因', example: '任务奖励' })
  @Expose()
  @IsOptional()
  @IsString({ message: '操作原因必须是字符串' })
  @Length(1, 100, { message: '操作原因长度必须在1-100个字符之间' })
  reason?: string;
}

/**
 * 扣除货币Payload DTO
 * @MessagePatternInternal('character.currency.subtract')
 * 扩展CurrencyOperationDto，合并BasePayloadDto内容
 */
export class SubtractCurrencyPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '货币类型', example: 'gold', enum: ['cash', 'gold', 'energy', 'worldCoin', 'chip', 'integral'] })
  @Expose()
  @IsString({ message: '货币类型必须是字符串' })
  @Length(1, 20, { message: '货币类型长度必须在1-20个字符之间' })
  currencyType: string;

  @ApiProperty({ description: '货币数量', example: 500 })
  @Expose()
  @IsNumber({}, { message: '货币数量必须是数字' })
  @Min(1, { message: '货币数量必须大于0' })
  @Max(999999999, { message: '货币数量不能超过999999999' })
  amount: number;

  @ApiPropertyOptional({ description: '操作原因', example: '购买物品' })
  @Expose()
  @IsOptional()
  @IsString({ message: '操作原因必须是字符串' })
  @Length(1, 100, { message: '操作原因长度必须在1-100个字符之间' })
  reason?: string;
}

/**
 * 购买体力Payload DTO
 * @MessagePattern('character.energy.buy')
 * 扩展BuyEnergyDto，合并BasePayloadDto内容
 */
export class BuyEnergyPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '购买次数', example: 1 })
  @Expose()
  @IsNumber({}, { message: '购买次数必须是数字' })
  @Min(1, { message: '购买次数不能小于1' })
  @Max(10, { message: '购买次数不能大于10' })
  count: number;

  @ApiPropertyOptional({ description: '使用的货币类型', example: 'cash', enum: ['cash', 'gold'] })
  @Expose()
  @IsOptional()
  @IsString({ message: '货币类型必须是字符串' })
  currencyType?: string;
}

/**
 * 角色升级Payload DTO
 * @MessagePattern('character.levelup')
 * 扩展LevelUpDto，合并BasePayloadDto内容
 */
export class LevelUpPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiPropertyOptional({ description: '目标等级（可选，不指定则升1级）', example: 10 })
  @Expose()
  @IsOptional()
  @IsNumber({}, { message: '目标等级必须是数字' })
  @Min(1, { message: '目标等级不能小于1' })
  @Max(100, { message: '目标等级不能大于100' })
  targetLevel?: number;

  @ApiPropertyOptional({ description: '是否使用道具升级', example: false })
  @Expose()
  @IsOptional()
  @IsBoolean({ message: '是否使用道具必须是布尔值' })
  useItems?: boolean;
}

// ==================== 4. 游戏进度相关 ====================

/**
 * 完成创建步骤Payload DTO
 * @MessagePattern('character.progress.completeStep')
 */
export class CompleteStepPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '步骤编号', example: 1 })
  @Expose()
  @IsNumber({}, { message: '步骤编号必须是数字' })
  @Min(0, { message: '步骤编号不能小于0' })
  @Max(100, { message: '步骤编号不能大于100' })
  step: number;
}

/**
 * 完成新手引导Payload DTO
 * @MessagePattern('character.progress.finishGuide')
 */
export class FinishGuidePayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;
}

// ==================== 5. 角色属性设置相关 ====================

/**
 * 设置角色信仰Payload DTO
 * @MessagePattern('character.setBelief')
 */
export class SetBeliefPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '信仰ID', example: 1 })
  @Expose()
  @IsNumber({}, { message: '信仰ID必须是数字' })
  @Min(0, { message: '信仰ID不能小于0' })
  @Max(999, { message: '信仰ID不能大于999' })
  beliefId: number;
}

/**
 * 使用兑换码Payload DTO
 * @MessagePattern('character.useRedeemCode')
 */
export class UseRedeemCodePayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '兑换码组别', example: 'newbie' })
  @Expose()
  @IsString({ message: '兑换码组别必须是字符串' })
  @Length(1, 20, { message: '兑换码组别长度必须在1-20个字符之间' })
  group: string;

  @ApiProperty({ description: '兑换码ID', example: 'WELCOME2024' })
  @Expose()
  @IsString({ message: '兑换码ID必须是字符串' })
  @Length(1, 50, { message: '兑换码ID长度必须在1-50个字符之间' })
  codeId: string;
}

/**
 * 更新持续Buff Payload DTO
 * @MessagePattern('character.updateBuff')
 */
export class UpdateBuffPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: 'Buff持续时间（秒）', example: 3600 })
  @Expose()
  @IsNumber({}, { message: 'Buff持续时间必须是数字' })
  @Min(1, { message: 'Buff持续时间不能小于1秒' })
  @Max(86400, { message: 'Buff持续时间不能大于24小时' })
  buffDuration: number;
}

// ==================== 6. 搜索和查询相关 ====================

/**
 * 根据名称搜索角色Payload DTO
 * @MessagePattern('character.searchByName')
 */
export class SearchByNamePayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色名称', example: 'PlayerName' })
  @Expose()
  @IsString({ message: '角色名称必须是字符串' })
  @Length(1, 20, { message: '角色名称长度必须在1-20个字符之间' })
  name: string;

  @ApiPropertyOptional({ description: '服务器ID（可选）', example: 'server_001' })
  @Expose()
  @IsOptional()
  @IsString({ message: '服务器ID必须是字符串' })
  serverId?: string;
}

/**
 * 获取个人信息Payload DTO
 * @MessagePattern('character.getPersonInfo')
 */
export class GetPersonInfoPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;
}

/**
 * 创建角色（完善角色信息）Payload DTO
 * @MessagePattern('character.createRole')
 */
export class CreateRolePayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '资质等级', example: 1 })
  @Expose()
  @IsNumber({}, { message: '资质等级必须是数字' })
  @Min(1, { message: '资质等级不能小于1' })
  @Max(5, { message: '资质等级不能大于5' })
  qualified: number;

  @ApiProperty({ description: '角色名称', example: 'HeroPlayer' })
  @Expose()
  @IsString({ message: '角色名称必须是字符串' })
  @Length(2, 20, { message: '角色名称长度必须在2-20个字符之间' })
  name: string;

  @ApiProperty({ description: '头像ID', example: 1 })
  @Expose()
  @IsNumber({}, { message: '头像ID必须是数字' })
  @Min(1, { message: '头像ID不能小于1' })
  @Max(999, { message: '头像ID不能大于999' })
  faceIcon: number;
}

/**
 * 修改角色名称Payload DTO
 * @MessagePattern('character.modifyName')
 */
export class ModifyCharacterNamePayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '新角色名称', example: 'NewHeroName' })
  @Expose()
  @IsString({ message: '角色名称必须是字符串' })
  @Length(2, 20, { message: '角色名称长度必须在2-20个字符之间' })
  name: string;
}

// ==================== 7. 资源操作相关 ====================

/**
 * 添加资源Payload DTO
 * @MessagePattern('character.addResource')
 */
export class AddResourcePayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '资源类型', example: 'gold', enum: ['cash', 'gold', 'energy', 'fame', 'trophy', 'worldCoin', 'chip', 'integral'] })
  @Expose()
  @IsString({ message: '资源类型必须是字符串' })
  @Length(1, 20, { message: '资源类型长度必须在1-20个字符之间' })
  resourceType: string;

  @ApiProperty({ description: '资源数量', example: 1000 })
  @Expose()
  @IsNumber({}, { message: '资源数量必须是数字' })
  @Min(1, { message: '资源数量必须大于0' })
  @Max(999999999, { message: '资源数量不能超过999999999' })
  amount: number;

  @ApiPropertyOptional({ description: '操作原因', example: '任务奖励' })
  @Expose()
  @IsOptional()
  @IsString({ message: '操作原因必须是字符串' })
  @Length(1, 100, { message: '操作原因长度必须在1-100个字符之间' })
  reason?: string;
}

/**
 * 获取体力奖励Payload DTO
 * @MessagePattern('character.getEnergyReward')
 */
export class GetEnergyRewardPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '奖励类型', example: 1, enum: [1, 2] })
  @Expose()
  @IsNumber({}, { message: '奖励类型必须是数字' })
  @Min(1, { message: '奖励类型必须是1或2' })
  @Max(2, { message: '奖励类型必须是1或2' })
  type: number;
}

/**
 * 消耗金币任务Payload DTO
 * @MessagePattern('character.costCashTask')
 */
export class CostCashTaskPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '消耗金币数量', example: 500 })
  @Expose()
  @IsNumber({}, { message: '消耗金币数量必须是数字' })
  @Min(1, { message: '消耗金币数量必须大于0' })
  @Max(999999999, { message: '消耗金币数量不能超过999999999' })
  amount: number;

  @ApiPropertyOptional({ description: '消耗原因', example: '购买道具' })
  @Expose()
  @IsOptional()
  @IsString({ message: '消耗原因必须是字符串' })
  @Length(1, 100, { message: '消耗原因长度必须在1-100个字符之间' })
  reason?: string;
}

/**
 * 扣除资源Payload DTO
 * @MessagePattern('character.deductResource')
 */
export class DeductResourcePayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '资源类型', example: 'gold', enum: ['cash', 'gold', 'energy', 'worldCoin', 'chip', 'integral'] })
  @Expose()
  @IsString({ message: '资源类型必须是字符串' })
  @Length(1, 20, { message: '资源类型长度必须在1-20个字符之间' })
  resourceType: string;

  @ApiProperty({ description: '扣除数量', example: 100 })
  @Expose()
  @IsNumber({}, { message: '扣除数量必须是数字' })
  @Min(1, { message: '扣除数量必须大于0' })
  @Max(999999999, { message: '扣除数量不能超过999999999' })
  amount: number;

  @ApiPropertyOptional({ description: '扣除原因', example: '购买物品' })
  @Expose()
  @IsOptional()
  @IsString({ message: '扣除原因必须是字符串' })
  @Length(1, 100, { message: '扣除原因长度必须在1-100个字符之间' })
  reason?: string;
}

// ==================== 8. 球探系统相关 ====================

/**
 * 获取球探数据Payload DTO
 * @MessagePattern('character.getScoutData')
 */
export class GetScoutDataPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;
}

/**
 * 更新球探数据Payload DTO
 * @MessagePattern('character.updateScoutData')
 */
export class UpdateScoutDataPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '球探数据', example: {} })
  @Expose()
  @IsObject({ message: '球探数据必须是对象' })
  scoutData: any;
}

// ==================== 9. 系统初始化相关 ====================

/**
 * 从Auth服务初始化角色Payload DTO
 * @MessagePattern('character.initializeFromAuth')
 */
export class InitializeFromAuthPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '用户ID', example: 'user_67890' })
  @Expose()
  @IsString({ message: '用户ID必须是字符串' })
  @Length(1, 50, { message: '用户ID长度必须在1-50个字符之间' })
  userId: string;

  @ApiProperty({ description: '区服ID', example: 'server_001' })
  @Expose()
  @IsString({ message: '区服ID必须是字符串' })
  @Length(1, 20, { message: '区服ID长度必须在1-20个字符之间' })
  serverId: string;

  @ApiProperty({ description: '角色名称', example: 'InitialPlayer' })
  @Expose()
  @IsString({ message: '角色名称必须是字符串' })
  @Length(2, 20, { message: '角色名称长度必须在2-20个字符之间' })
  characterName: string;

  @ApiPropertyOptional({ description: '初始化数据（可选）', example: {} })
  @Expose()
  @IsOptional()
  @IsObject({ message: '初始化数据必须是对象' })
  initialData?: any;
}

// ==================== 导出所有Payload DTO ====================

export {
  // 角色创建相关
  CreateCharacterPayloadDto,
  LoginCharacterPayloadDto,

  // 角色基础操作相关
  LogoutCharacterPayloadDto,
  GetCharacterInfoPayloadDto,
  UpdateCharacterPayloadDto,
  GetCharacterListPayloadDto,

  // 货币和资源相关
  AddCurrencyPayloadDto,
  SubtractCurrencyPayloadDto,
  BuyEnergyPayloadDto,
  LevelUpPayloadDto,

  // 游戏进度相关
  CompleteStepPayloadDto,
  FinishGuidePayloadDto,

  // 角色属性设置相关
  SetBeliefPayloadDto,
  UseRedeemCodePayloadDto,
  UpdateBuffPayloadDto,

  // 搜索和查询相关
  SearchByNamePayloadDto,
  GetPersonInfoPayloadDto,
  CreateRolePayloadDto,
  ModifyCharacterNamePayloadDto,

  // 资源操作相关
  AddResourcePayloadDto,
  GetEnergyRewardPayloadDto,
  CostCashTaskPayloadDto,
  DeductResourcePayloadDto,

  // 球探系统相关
  GetScoutDataPayloadDto,
  UpdateScoutDataPayloadDto,

  // 系统初始化相关
  InitializeFromAuthPayloadDto,
};
