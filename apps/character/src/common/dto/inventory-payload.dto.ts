/**
 * Inventory模块的Payload DTO定义
 * 
 * 为inventory.controller.ts中的所有@MessagePattern接口生成对应的Payload DTO
 * 所有DTO都继承自BasePayloadDto，直接包含字段而不嵌套dto
 * 包含详细的校验规则和@Expose()装饰器
 */

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsNumber, IsOptional, Min, Max, Length, IsArray, ValidateNested } from 'class-validator';
import { Expose, Type } from 'class-transformer';
import { BasePayloadDto } from '@libs/common/dto/base-payload.dto';

// ==================== 1. 基础物品操作相关 ====================

/**
 * 获取背包物品Payload DTO
 * @MessagePattern('inventory.get')
 */
export class GetInventoryPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;
}

/**
 * 添加物品Payload DTO
 * @MessagePattern('inventory.addItem')
 */
export class AddItemPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '物品配置ID', example: 1001 })
  @Expose()
  @IsNumber({}, { message: '物品配置ID必须是数字' })
  @Min(1, { message: '物品配置ID不能小于1' })
  @Max(999999, { message: '物品配置ID不能大于999999' })
  resId: number;

  @ApiProperty({ description: '物品数量', example: 10 })
  @Expose()
  @IsNumber({}, { message: '物品数量必须是数字' })
  @Min(1, { message: '物品数量不能小于1' })
  @Max(999999, { message: '物品数量不能大于999999' })
  num: number;
}

/**
 * 移除物品Payload DTO
 * @MessagePattern('inventory.removeItem')
 */
export class RemoveItemPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '物品唯一ID', example: 'item_67890' })
  @Expose()
  @IsString({ message: '物品ID必须是字符串' })
  @Length(1, 50, { message: '物品ID长度必须在1-50个字符之间' })
  itemId: string;

  @ApiProperty({ description: '移除数量', example: 5 })
  @Expose()
  @IsNumber({}, { message: '移除数量必须是数字' })
  @Min(1, { message: '移除数量不能小于1' })
  @Max(999999, { message: '移除数量不能大于999999' })
  quantity: number;
}

/**
 * 使用物品Payload DTO
 * @MessagePattern('inventory.useItem')
 */
export class UseItemPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '物品唯一ID', example: 'item_67890' })
  @Expose()
  @IsString({ message: '物品ID必须是字符串' })
  @Length(1, 50, { message: '物品ID长度必须在1-50个字符之间' })
  itemId: string;

  @ApiPropertyOptional({ description: '使用数量（默认为1）', example: 1 })
  @Expose()
  @IsOptional()
  @IsNumber({}, { message: '使用数量必须是数字' })
  @Min(1, { message: '使用数量不能小于1' })
  @Max(999999, { message: '使用数量不能大于999999' })
  quantity?: number;
}

// ==================== 2. 物品查询相关 ====================

/**
 * 获取物品数量Payload DTO
 * @MessagePattern('inventory.getItemQuantity')
 */
export class GetItemQuantityPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '物品唯一ID', example: 'item_67890' })
  @Expose()
  @IsString({ message: '物品ID必须是字符串' })
  @Length(1, 50, { message: '物品ID长度必须在1-50个字符之间' })
  itemId: string;
}

/**
 * 根据配置ID获取物品数量Payload DTO
 * @MessagePattern('inventory.getItemQuantityByConfigId')
 */
export class GetItemQuantityByConfigIdPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '物品配置ID', example: 1001 })
  @Expose()
  @IsNumber({}, { message: '物品配置ID必须是数字' })
  @Min(1, { message: '物品配置ID不能小于1' })
  @Max(999999, { message: '物品配置ID不能大于999999' })
  configId: number;
}

/**
 * 检查物品数量是否足够Payload DTO
 * @MessagePattern('inventory.checkItemSufficient')
 */
export class CheckItemSufficientPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '物品配置ID', example: 1001 })
  @Expose()
  @IsNumber({}, { message: '物品配置ID必须是数字' })
  @Min(1, { message: '物品配置ID不能小于1' })
  @Max(999999, { message: '物品配置ID不能大于999999' })
  configId: number;

  @ApiProperty({ description: '需要的数量', example: 5 })
  @Expose()
  @IsNumber({}, { message: '需要的数量必须是数字' })
  @Min(1, { message: '需要的数量不能小于1' })
  @Max(999999, { message: '需要的数量不能大于999999' })
  requiredQuantity: number;
}

// ==================== 3. 批量操作相关 ====================

/**
 * 批量添加物品项DTO
 */
export class BatchAddItemDto {
  @ApiProperty({ description: '物品配置ID', example: 1001 })
  @Expose()
  @IsNumber({}, { message: '物品配置ID必须是数字' })
  @Min(1, { message: '物品配置ID不能小于1' })
  @Max(999999, { message: '物品配置ID不能大于999999' })
  configId: number;

  @ApiProperty({ description: '物品数量', example: 10 })
  @Expose()
  @IsNumber({}, { message: '物品数量必须是数字' })
  @Min(1, { message: '物品数量不能小于1' })
  @Max(999999, { message: '物品数量不能大于999999' })
  quantity: number;
}

/**
 * 批量添加物品Payload DTO
 * @MessagePattern('inventory.batchAddItems')
 */
export class BatchAddItemsPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '物品列表', type: [BatchAddItemDto] })
  @Expose()
  @IsArray({ message: '物品列表必须是数组' })
  @ValidateNested({ each: true, message: '物品列表中的每个项目格式不正确' })
  @Type(() => BatchAddItemDto)
  items: BatchAddItemDto[];
}

/**
 * 批量移除物品项DTO
 */
export class BatchRemoveItemDto {
  @ApiProperty({ description: '物品唯一ID', example: 'item_67890' })
  @Expose()
  @IsString({ message: '物品ID必须是字符串' })
  @Length(1, 50, { message: '物品ID长度必须在1-50个字符之间' })
  itemId: string;

  @ApiProperty({ description: '移除数量', example: 5 })
  @Expose()
  @IsNumber({}, { message: '移除数量必须是数字' })
  @Min(1, { message: '移除数量不能小于1' })
  @Max(999999, { message: '移除数量不能大于999999' })
  quantity: number;
}

/**
 * 批量移除物品Payload DTO
 * @MessagePattern('inventory.batchRemoveItems')
 */
export class BatchRemoveItemsPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '物品列表', type: [BatchRemoveItemDto] })
  @Expose()
  @IsArray({ message: '物品列表必须是数组' })
  @ValidateNested({ each: true, message: '物品列表中的每个项目格式不正确' })
  @Type(() => BatchRemoveItemDto)
  items: BatchRemoveItemDto[];
}

// ==================== 4. 背包概览和页签相关 ====================

/**
 * 获取背包概览Payload DTO
 * @MessagePattern('inventory.getSummaries')
 */
export class GetInventorySummariesPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;
}

/**
 * 获取页签数据Payload DTO
 * @MessagePattern('inventory.getTab')
 */
export class GetTabPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '页签ID', example: 1 })
  @Expose()
  @IsNumber({}, { message: '页签ID必须是数字' })
  @Min(1, { message: '页签ID不能小于1' })
  @Max(99, { message: '页签ID不能大于99' })
  tabId: number;
}

/**
 * 添加物品到页签Payload DTO
 * @MessagePattern('inventory.addItemToBag')
 */
export class AddItemToTabPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '页签ID', example: 1 })
  @Expose()
  @IsNumber({}, { message: '页签ID必须是数字' })
  @Min(1, { message: '页签ID不能小于1' })
  @Max(99, { message: '页签ID不能大于99' })
  tabId: number;

  @ApiProperty({ description: '物品唯一ID', example: 'item_67890' })
  @Expose()
  @IsString({ message: '物品ID必须是字符串' })
  @Length(1, 50, { message: '物品ID长度必须在1-50个字符之间' })
  itemId: string;
}

/**
 * 从页签移除物品Payload DTO
 * @MessagePattern('inventory.removeFromTab')
 */
export class RemoveFromTabPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '页签ID', example: 1 })
  @Expose()
  @IsNumber({}, { message: '页签ID必须是数字' })
  @Min(1, { message: '页签ID不能小于1' })
  @Max(99, { message: '页签ID不能大于99' })
  tabId: number;

  @ApiProperty({ description: '物品唯一ID', example: 'item_67890' })
  @Expose()
  @IsString({ message: '物品ID必须是字符串' })
  @Length(1, 50, { message: '物品ID长度必须在1-50个字符之间' })
  itemId: string;
}

/**
 * 扩展背包Payload DTO
 * @MessagePattern('inventory.expandBag')
 */
export class ExpandTabPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '页签ID', example: 1 })
  @Expose()
  @IsNumber({}, { message: '页签ID必须是数字' })
  @Min(1, { message: '页签ID不能小于1' })
  @Max(99, { message: '页签ID不能大于99' })
  tabId: number;

  @ApiPropertyOptional({ description: '扩展数量（默认为1）', example: 1 })
  @Expose()
  @IsOptional()
  @IsNumber({}, { message: '扩展数量必须是数字' })
  @Min(1, { message: '扩展数量不能小于1' })
  @Max(10, { message: '扩展数量不能大于10' })
  expandCount?: number;
}

/**
 * 整理背包Payload DTO
 * @MessagePattern('inventory.sortBag')
 */
export class SortTabPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '页签ID', example: 1 })
  @Expose()
  @IsNumber({}, { message: '页签ID必须是数字' })
  @Min(1, { message: '页签ID不能小于1' })
  @Max(99, { message: '页签ID不能大于99' })
  tabId: number;

  @ApiPropertyOptional({ description: '排序类型', example: 'type', enum: ['type', 'rarity', 'name', 'quantity'] })
  @Expose()
  @IsOptional()
  @IsString({ message: '排序类型必须是字符串' })
  @Length(1, 20, { message: '排序类型长度必须在1-20个字符之间' })
  sortType?: string;
}
