import { Controller, UsePipes } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { MessagePatternInternal } from '@libs/common/decorators';
import { BaseController } from '@libs/common/controller';
import { CharacterService } from './character.service';
import {
  CharacterInfoDto,
  LoginResultDto,
  LevelUpResultDto,
  BuyEnergyResultDto
} from '@character/common/dto/character.dto';
import {
  CreateCharacterPayloadDto,
  LoginCharacterPayloadDto,
  LogoutCharacterPayloadDto,
  GetCharacterInfoPayloadDto,
  UpdateCharacterPayloadDto,
  GetCharacterListPayloadDto,
  AddCurrencyPayloadDto,
  SubtractCurrencyPayloadDto,
  BuyEnergyPayloadDto,
  LevelUpPayloadDto,
  CompleteStepPayloadDto,
  FinishGuidePayloadDto,
  SetBeliefPayloadDto,
  UseRedeemCodePayloadDto,
  UpdateBuffPayloadDto,
  SearchByNamePayloadDto,
  GetPersonInfoPayloadDto,
  CreateRolePayloadDto,
  ModifyCharacterNamePayloadDto,
  AddResourcePayloadDto,
  GetEnergyRewardPayloadDto,
  CostCashTaskPayloadDto,
  DeductResourcePayloadDto,
  GetScoutDataPayloadDto,
  UpdateScoutDataPayloadDto,
  InitializeFromAuthPayloadDto
} from '@character/common/dto';
import { Cacheable, CacheEvict, CachePut } from '@libs/redis';
import { CharacterDocument } from '@character/common/schemas/character.schema';
import { XResponse, PaginationResult, XResultUtils } from '@libs/common/types/result.type';
import { StandardMicroserviceValidationPipe } from "@libs/common";

/**
 * 角色控制器
 * 继承BaseController，提供统一的微服务接口处理和Result模式集成
 *
 * 🎯 核心功能：
 * - 角色创建和管理
 * - 角色登录和认证
 * - 角色属性和资源管理
 * - 角色升级和能量购买
 * - 角色列表查询和分页
 *
 * 🚀 优化特性：
 * - 统一的请求处理和响应格式
 * - 完整的Result模式错误处理
 * - 自动的性能监控和日志记录
 * - 标准化的缓存装饰器集成
 * - 参数验证和提取框架
 */
@Controller()
export class CharacterController extends BaseController {
  constructor(private readonly characterService: CharacterService) {
    super('CharacterController', {
      enableRequestLogging: true,
      enablePerformanceMonitoring: true,
      autoHandleExceptions: true
    });
  }

  // ========== 角色管理接口 ==========

  /**
   * 创建新角色
   * 使用BaseController的统一请求处理框架和新的Payload DTO
   */
  @MessagePattern('character.create')
  @UsePipes(StandardMicroserviceValidationPipe)
  @CacheEvict({
    key: 'user:characters:#{payload.userId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async createCharacter(@Payload() payload: CreateCharacterPayloadDto): Promise<XResponse<CharacterDocument>> {
    return this.handleRequest(async () => {
      const result = await this.characterService.createCharacter(payload);
      return this.fromResult(result, '角色创建成功');
    }, payload);
  }

  /**
   * 角色登录
   * 使用BaseController的统一请求处理框架和新的Payload DTO
   */
  @MessagePattern('character.login')
  @UsePipes(StandardMicroserviceValidationPipe)
  @CachePut({
    key: 'character:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 3600
  })
  async loginCharacter(@Payload() payload: LoginCharacterPayloadDto): Promise<XResponse<LoginResultDto>> {
    return this.handleRequest(async () => {
      const result = await this.characterService.loginCharacter(payload);
      return this.fromResult(result, '登录成功');
    }, payload);
  }

  /**
   * 角色登出
   * 使用BaseController的统一请求处理框架
   */
  @MessagePattern('character.logout')
  @UsePipes(StandardMicroserviceValidationPipe)
  @CacheEvict({
    key: 'character:info:#{payload.characterId}',
    dataType: 'server'
  })
  async logoutCharacter(@Payload() payload: LogoutCharacterPayloadDto): Promise<XResponse<any>> {
    return this.handleRequest(async () => {
      const result = await this.characterService.logoutCharacter(payload.characterId);
      return this.fromResult(result, '登出成功');
    }, payload);
  }

  /**
   * 获取角色信息
   * 使用BaseController的统一请求处理框架
   */
  @MessagePattern('character.getInfo')
  @UsePipes(StandardMicroserviceValidationPipe)
  @Cacheable({
    key: 'character:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 3600
  })
  async getCharacterInfo(@Payload() payload: GetCharacterInfoPayloadDto): Promise<XResponse<CharacterInfoDto>> {
    return this.handleRequest(async () => {
      const result = await this.characterService.getCharacterInfo(payload.characterId);
      return this.fromResult(result, '获取成功');
    }, payload);
  }

  /**
   * 更新角色信息
   * 使用BaseController的统一请求处理框架
   */
  @MessagePattern('character.update')
  @UsePipes(StandardMicroserviceValidationPipe)
  @CachePut({
    key: 'character:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 3600
  })
  async updateCharacter(@Payload() payload: UpdateCharacterPayloadDto): Promise<XResponse<CharacterDocument>> {
    return this.handleRequest(async () => {
      const result = await this.characterService.updateCharacter(payload.characterId, payload);
      return this.fromResult(result, '更新成功');
    }, payload);
  }

  /**
   * 获取角色列表
   * 使用BaseController的统一请求处理框架和分页支持
   */
  @MessagePattern('character.getList')
  @UsePipes(StandardMicroserviceValidationPipe)
  @Cacheable({
    key: 'user:characters:#{payload.userId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 1800
  })
  async getCharacterList(@Payload() payload: GetCharacterListPayloadDto): Promise<XResponse<PaginationResult<any>>> {
    return this.handleRequest(async () => {
      const result = await this.characterService.getCharacterList(payload);
      return this.fromResult(result, '获取成功');
    }, payload);
  }

  // ========== 货币操作接口（内部服务调用）==========

  /**
   * 增加货币 - 仅内部服务调用
   * 使用BaseController的统一请求处理框架
   */
  @MessagePatternInternal('character.currency.add')
  @UsePipes(StandardMicroserviceValidationPipe)
  @CacheEvict({
    key: 'character:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async addCurrency(@Payload() payload: AddCurrencyPayloadDto) {
    return this.handleRequest(async () => {
      const result = await this.characterService.addCurrency(payload.characterId, payload);
      return this.fromResult(result, '货币增加成功');
    }, payload);
  }

  /**
   * 扣除货币 - 仅内部服务调用
   * 使用BaseController的统一请求处理框架
   */
  @MessagePatternInternal('character.currency.subtract')
  @UsePipes(StandardMicroserviceValidationPipe)
  @CacheEvict({
    key: 'character:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async subtractCurrency(@Payload() payload: SubtractCurrencyPayloadDto): Promise<XResponse<any>> {
    return this.handleRequest(async () => {
      const result = await this.characterService.subtractCurrency(payload.characterId, payload);
      return this.fromResult(result, '货币扣除成功');
    }, payload);
  }

  // ========== 角色功能接口 ==========

  /**
   * 购买体力
   * 使用BaseController的统一请求处理框架
   */
  @MessagePattern('character.energy.buy')
  @UsePipes(StandardMicroserviceValidationPipe)
  @CacheEvict({
    key: 'character:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async buyEnergy(@Payload() payload: BuyEnergyPayloadDto): Promise<XResponse<BuyEnergyResultDto>> {
    return this.handleRequest(async () => {
      const result = await this.characterService.buyEnergy(payload.characterId, payload);
      return this.fromResult(result, '购买体力成功');
    }, payload);
  }

  /**
   * 角色升级
   * 使用BaseController的统一请求处理框架
   */
  @MessagePattern('character.levelup')
  @UsePipes(StandardMicroserviceValidationPipe)
  @CacheEvict({
    key: 'character:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async levelUp(@Payload() payload: LevelUpPayloadDto): Promise<XResponse<LevelUpResultDto>> {
    return this.handleRequest(async () => {
      const result = await this.characterService.levelUp(payload.characterId, payload.levelUpDto);
      return this.fromResult(result, '升级成功');
    }, payload);
  }

  /**
   * 完成创角步骤
   * 使用BaseController的统一请求处理框架
   */
  @MessagePattern('character.progress.completeStep')
  @UsePipes(StandardMicroserviceValidationPipe)
  @CacheEvict({
    key: 'character:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async completeCreateStep(@Payload() payload: CompleteStepPayloadDto): Promise<XResponse<CharacterDocument>> {
    return this.handleRequest(async () => {
      const result = await this.characterService.completeCreateStep(payload.characterId, payload.step);
      return this.fromResult(result, '步骤完成成功');
    }, payload);
  }

  /**
   * 完成新手引导
   * 使用BaseController的统一请求处理框架
   */
  @MessagePattern('character.progress.finishGuide')
  @UsePipes(StandardMicroserviceValidationPipe)
  @CacheEvict({
    key: 'character:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async finishGuide(@Payload() payload: FinishGuidePayloadDto): Promise<XResponse<CharacterDocument>> {
    return this.handleRequest(async () => {
      const result = await this.characterService.finishGuide(payload.characterId);
      return this.fromResult(result, '新手引导完成');
    }, payload);
  }

  /**
   * 设置角色信仰
   * 使用BaseController的统一请求处理框架
   */
  @MessagePattern('character.setBelief')
  @UsePipes(StandardMicroserviceValidationPipe)
  async setCharacterBelief(@Payload() payload: SetBeliefPayloadDto): Promise<XResponse<CharacterDocument>> {
    return this.handleRequest(async () => {
      const result = await this.characterService.setCharacterBelief(payload.characterId, payload.beliefId);
      return this.fromResult(result, '设置成功');
    }, payload);
  }

  /**
   * 使用兑换码
   * 使用BaseController的统一请求处理框架
   */
  @MessagePattern('character.useRedeemCode')
  @UsePipes(StandardMicroserviceValidationPipe)
  async useRedeemCode(@Payload() payload: UseRedeemCodePayloadDto): Promise<XResponse<any>> {
    return this.handleRequest(async () => {
      const result = await this.characterService.useRedeemCode(payload.characterId, payload.group, payload.codeId);
      return this.fromResult(result, '兑换成功');
    }, payload);
  }

  /**
   * 更新持续buff
   * 使用BaseController的统一请求处理框架
   */
  @MessagePattern('character.updateBuff')
  @UsePipes(StandardMicroserviceValidationPipe)
  async updateContinuedBuff(@Payload() payload: UpdateBuffPayloadDto): Promise<XResponse<CharacterDocument>> {
    return this.handleRequest(async () => {
      const result = await this.characterService.updateContinuedBuff(payload.characterId, payload.buffDuration);
      return this.fromResult(result, '更新成功');
    }, payload);
  }

  /**
   * 根据名称搜索角色
   * 基于old项目: accountService.searchPlayerName
   * 用于商业赛等功能的对手搜索
   * 使用BaseController的统一请求处理框架
   */
  @MessagePattern('character.searchByName')
  @UsePipes(StandardMicroserviceValidationPipe)
  @Cacheable({
    key: 'character:search:name:#{payload.name}:#{payload.serverId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 300
  })
  async searchCharacterByName(@Payload() payload: SearchByNamePayloadDto): Promise<XResponse<CharacterDocument[]>> {
    return this.handleRequest(async () => {
      const result = await this.characterService.searchByName(payload.name, payload.serverId);
      return this.fromResult(result, '搜索成功');
    }, payload);
  }

  // ========== old项目核心API（兼容性接口）==========

  /**
   * 获取个人信息
   * 对应old项目: game.player.getPersonInfo
   * 使用BaseController的统一请求处理框架
   */
  @MessagePattern('character.getPersonInfo')
  @UsePipes(StandardMicroserviceValidationPipe)
  @Cacheable({
    key: 'character:person:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 300
  })
  async getPersonInfo(@Payload() payload: GetPersonInfoPayloadDto): Promise<XResponse<CharacterInfoDto>> {
    return this.handleRequest(async () => {
      const result = await this.characterService.getPersonInfo(payload.characterId);
      // 注意：这里直接返回service的结果，因为old项目的service方法已经返回了XResponse格式
      return result;
    }, payload);
  }

  /**
   * 创建角色
   * 对应old项目: game.player.createRole
   * 使用BaseController的统一请求处理框架
   */
  @MessagePattern('character.createRole')
  @UsePipes(StandardMicroserviceValidationPipe)
  @CacheEvict({
    key: 'character:person:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async createRole(@Payload() payload: CreateRolePayloadDto): Promise<XResponse<CharacterDocument>> {
    return this.handleRequest(async () => {
      const result = await this.characterService.createRole(payload.characterId, {
        qualified: payload.qualified,
        name: payload.name,
        faceIcon: payload.faceIcon,
      });
      // 注意：这里直接返回service的结果，因为old项目的service方法已经返回了XResponse格式
      return result;
    }, payload);
  }

  /**
   * 修改玩家名称
   * 对应old项目: game.player.modifyPlayerName
   * 使用BaseController的统一请求处理框架
   */
  @MessagePattern('character.modifyCharacterName')
  @UsePipes(StandardMicroserviceValidationPipe)
  @CacheEvict({
    key: 'character:person:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async modifyCharacterName(@Payload() payload: ModifyCharacterNamePayloadDto): Promise<XResponse<CharacterDocument>> {
    return this.handleRequest(async () => {
      const result = await this.characterService.modifyCharacterName(payload.characterId, {
        name: payload.name,
      });
      // 注意：这里直接返回service的结果，因为old项目的service方法已经返回了XResponse格式
      return result;
    }, payload);
  }

  /**
   * 添加资源 - 仅内部服务调用
   * 对应old项目: game.player.addResource
   * 使用BaseController的统一请求处理框架
   */
  @MessagePatternInternal('character.addResource')
  @UsePipes(StandardMicroserviceValidationPipe)
  @CacheEvict({
    key: 'character:person:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async addResource(@Payload() payload: AddResourcePayloadDto) {
    return this.handleRequest(async () => {
      const result = await this.characterService.addResource(
        payload.characterId,
        payload.resourceType,
        payload.amount,
        payload.reason
      );
      // 注意：这里直接返回service的结果，因为old项目的service方法已经返回了XResponse格式
      return result;
    }, payload);
  }

  /**
   * 获取体力奖励
   * 对应old项目: game.player.getEnergyReward
   * 使用BaseController的统一请求处理框架
   */
  @MessagePattern('character.getEnergyReward')
  @UsePipes(StandardMicroserviceValidationPipe)
  @CacheEvict({
    key: 'character:person:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async getEnergyReward(@Payload() payload: GetEnergyRewardPayloadDto): Promise<XResponse<any>> {
    return this.handleRequest(async () => {
      const result = await this.characterService.getEnergyReward(payload.characterId, payload.type);
      // 注意：这里直接返回service的结果，因为old项目的service方法已经返回了XResponse格式
      return result;
    }, payload);
  }

  /**
   * 消耗金币任务
   * 对应old项目: game.player.costCashTask
   * 使用BaseController的统一请求处理框架
   */
  @MessagePattern('character.costCashTask')
  @UsePipes(StandardMicroserviceValidationPipe)
  @CacheEvict({
    key: 'character:person:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async costCashTask(@Payload() payload: CostCashTaskPayloadDto): Promise<XResponse<any>> {
    return this.handleRequest(async () => {
      const result = await this.characterService.costCashTask(
        payload.characterId,
        payload.amount,
        payload.reason
      );
      // 注意：这里直接返回service的结果，因为old项目的service方法已经返回了XResponse格式
      return result;
    }, payload);
  }

  /**
   * 扣除资源 - 仅内部服务调用
   * 对应old项目: game.player.deductResource
   * 使用BaseController的统一请求处理框架
   */
  @MessagePatternInternal('character.deductResource')
  @UsePipes(StandardMicroserviceValidationPipe)
  @CacheEvict({
    key: 'character:person:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async deductResource(@Payload() payload: DeductResourcePayloadDto) {
    return this.handleRequest(async () => {
      // 验证必需参数
      const validation = this.validateRequiredFields(payload, ['characterId', 'resourceType', 'amount']);
      if (XResultUtils.isFailure(validation)) {
        return this.fromResult(validation);
      }

      const result = await this.characterService.deductResource(
        payload.characterId,
        payload.resourceType,
        payload.amount,
        payload.reason
      );
      // 注意：这里直接返回service的结果，因为old项目的service方法已经返回了XResponse格式
      return result;
    }, payload);
  }

  // ========== 球探系统API ==========

  /**
   * 获取角色球探数据
   * 基于old项目Scout实体
   * 使用BaseController的统一请求处理框架
   */
  @MessagePattern('character.getScoutData')
  @UsePipes(StandardMicroserviceValidationPipe)
  @Cacheable({
    key: 'character:scout:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 300
  })
  async getScoutData(@Payload() payload: GetScoutDataPayloadDto): Promise<XResponse<any>> {
    return this.handleRequest(async () => {
      // 验证必需参数
      const validation = this.validateRequiredFields(payload, ['characterId']);
      if (XResultUtils.isFailure(validation)) {
        return this.fromResult(validation);
      }

      const result = await this.characterService.getScoutData(payload.characterId);
      return this.fromResult(result, '获取球探数据成功');
    }, payload);
  }

  /**
   * 更新角色球探数据
   * 基于old项目Scout实体
   * 使用BaseController的统一请求处理框架
   */
  @MessagePattern('character.updateScoutData')
  @UsePipes(StandardMicroserviceValidationPipe)
  @CacheEvict({
    key: 'character:scout:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async updateScoutData(@Payload() payload: UpdateScoutDataPayloadDto): Promise<XResponse<any>> {
    return this.handleRequest(async () => {
      const result = await this.characterService.updateScoutData(payload.characterId, payload.scoutData);
      return this.fromResult(result, '更新球探数据成功');
    }, payload);
  }

  // ========== 服务间通信接口 ==========

  /**
   * 接收Auth服务的角色初始化通知
   * 使用BaseController的统一请求处理框架
   */
  @MessagePattern('character.initializeFromAuth')
  @UsePipes(StandardMicroserviceValidationPipe)
  async initializeFromAuth(@Payload() payload: InitializeFromAuthPayloadDto): Promise<XResponse<CharacterDocument>> {
    return this.handleRequest(async () => {
      this.logger.log(`📨 收到Auth服务角色初始化通知: ${payload.characterId}`);

      const result = await this.characterService.initializeFromAuth(payload);
      if (XResultUtils.isSuccess(result)) {
        return this.fromResult(XResultUtils.ok({
          characterId: result.data.characterId,
          success: true,
        }), '角色游戏数据初始化成功');
      } else {
        return this.fromResult(result, '角色游戏数据初始化失败');
      }
    }, payload);
  }
}
