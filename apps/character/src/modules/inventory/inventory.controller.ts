/**
 * 统一的角色物品背包控制器
 * 合并ItemController和InventoryController的功能
 * 继承BaseController，严格遵循项目WebSocket微服务模式
 * 覆盖item.controller.ts和inventory.controller.ts的所有接口
 */

import { Controller } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { InventoryService } from './inventory.service';
import { Cacheable, CacheEvict } from '@libs/redis';
import { InjectedContext, XResultUtils } from '@libs/common/types';
import { XResponse } from '@libs/common/types/result.type';
import { BaseController, BasePayload } from '@libs/common/controller/base-controller';

/**
 * 统一的角色物品背包控制器
 * 继承BaseController，提供标准化的微服务接口处理
 *
 * 🎯 核心功能：
 * - 物品管理（覆盖item.controller.ts的所有接口）
 * - 背包管理（覆盖inventory.controller.ts的所有接口）
 * - 统一的Result模式响应
 * - Redis缓存装饰器支持
 * - 完整的参数验证和错误处理
 *
 * 🚀 架构特性：
 * - 继承BaseController的标准化处理
 * - WebSocket微服务接口(@MessagePattern)
 * - Redis缓存装饰器(@Cacheable/@CacheEvict)
 * - 统一的XResponse响应格式
 * - 完整的日志记录和性能监控
 */
@Controller()
export class InventoryController extends BaseController {
  constructor(
    private readonly inventoryService: InventoryService
  ) {
    super('InventoryController');
  }

  // ========== 物品管理API（覆盖item.controller.ts）==========

  /**
   * 获取角色物品数据
   * 覆盖item.getItems接口
   * 对应old项目: Item实体的toJSONforDB
   */
  @MessagePattern('inventory.get')
  @Cacheable({
    key: 'character:inventory:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 300
  })
  async getInventory(@Payload() payload: { characterId: string; serverId?: string; injectedContext?: InjectedContext }): Promise<XResponse<any>> {
    return this.handleRequest(async () => {
      this.logger.log(`获取角色物品数据: ${payload.characterId}`);
      
      const result = await this.inventoryService.getInventory(payload.characterId);
      if (!result.data) {
        // 如果不存在，初始化物品数据
        const initResult = await this.inventoryService.initializeInventory(payload.characterId);
        return this.fromResult(initResult);
      }

      return this.fromResult(result);
    }, payload);
  }

  /**
   * 添加物品
   * 覆盖item.addItem接口
   * 对应old项目: addItem方法
   */
  @MessagePattern('inventory.addItem')
  @CacheEvict({
    key: 'character:items:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async addItem(@Payload() payload: { characterId: string; resId: number; num: number; serverId?: string; injectedContext?: InjectedContext }): Promise<XResponse<any>> {
    return this.handleRequest(async () => {
      this.logger.log(`添加物品: ${payload.characterId}, 配置ID: ${payload.resId}, 数量: ${payload.num}`);
      
      const result = await this.inventoryService.addItem(
        payload.characterId,
        payload.resId,
        payload.num
      );

      return this.fromResult(result);
    }, payload);
  }

  /**
   * 移除物品
   * 覆盖item.removeItem接口
   * 对应old项目: delItem方法，优化API命名
   */
  @MessagePattern('inventory.removeItem')
  @CacheEvict({
    key: 'character:items:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async removeItem(@Payload() payload: { characterId: string; itemId: string; quantity: number; serverId?: string; injectedContext?: InjectedContext }): Promise<XResponse<any>> {
    return this.handleRequest(async () => {
      this.logger.log(`移除物品: ${payload.characterId}, UID: ${payload.itemId}, 数量: ${payload.quantity}`);

      const result = await this.inventoryService.removeItem(
        payload.characterId,
        payload.itemId,
        payload.quantity
      );

      return this.fromResult(result);
    }, payload);
  }

  /**
   * 使用物品
   * 覆盖item.useItem接口
   * 对应old项目: useItem方法
   */
  @MessagePattern('inventory.useItem')
  @CacheEvict({
    key: 'character:items:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async useItem(@Payload() payload: { characterId: string; itemId: string; quantity?: number; serverId?: string; injectedContext?: InjectedContext }): Promise<XResponse<any>> {
    return this.handleRequest(async () => {
      this.logger.log(`使用物品: ${payload.characterId}, UID: ${payload.itemId}, 数量: ${payload.quantity || 1}`);

      const result = await this.inventoryService.useItem(
        payload.characterId,
        payload.itemId,
        payload.quantity || 1
      );

      return this.fromResult(result);
    }, payload);
  }

  /**
   * 获取物品数量
   * 覆盖item.getItemQuantity接口
   * 对应old项目: getItemNum方法，优化API命名
   */
  @MessagePattern('inventory.getItemQuantity')
  @Cacheable({
    key: 'character:item:num:#{payload.characterId}:#{payload.itemId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 60
  })
  async getItemQuantity(@Payload() payload: { characterId: string; itemId: string; serverId?: string; injectedContext?: InjectedContext }): Promise<XResponse<any>> {
    return this.handleRequest(async () => {
      this.logger.log(`获取物品数量: ${payload.characterId}, UID: ${payload.itemId}`);

      const result = await this.inventoryService.getInventory(payload.characterId);
      if (!result.data) {
        return this.toSuccessResponse({ quantity: 0 });
      }

      const quantity = result.data.getItemQuantity(payload.itemId);
      return this.toSuccessResponse({ quantity });
    }, payload);
  }

  /**
   * 根据配置ID获取物品总数量
   * 覆盖item.getItemQuantityByConfigId接口
   * 对应old项目: getItemNumByResID方法，优化API命名
   */
  @MessagePattern('inventory.getItemQuantityByConfigId')
  @Cacheable({
    key: 'character:item:numByResId:#{payload.characterId}:#{payload.configId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 60
  })
  async getItemQuantityByConfigId(@Payload() payload: { characterId: string; configId: number; serverId?: string; injectedContext?: InjectedContext }): Promise<XResponse<any>> {
    return this.handleRequest(async () => {
      this.logger.log(`根据配置ID获取物品总数量: ${payload.characterId}, 配置ID: ${payload.configId}`);

      const result = await this.inventoryService.getItemQuantityByConfigId(
        payload.characterId,
        payload.configId
      );

      return this.fromResult(result, '查询成功');
    }, payload);
  }

  /**
   * 检查物品数量是否足够
   * 覆盖item.checkItemSufficient接口
   * 对应old项目: checkItemIsEnough方法，优化API命名
   */
  @MessagePattern('inventory.checkItemSufficient')
  @Cacheable({
    key: 'character:item:check:#{payload.characterId}:#{payload.configId}:#{payload.requiredQuantity}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 30
  })
  async checkItemSufficient(@Payload() payload: { characterId: string; configId: number; requiredQuantity: number; serverId?: string; injectedContext?: InjectedContext }): Promise<XResponse<any>> {
    return this.handleRequest(async () => {
      this.logger.log(`检查物品数量是否足够: ${payload.characterId}, 配置ID: ${payload.configId}, 需要数量: ${payload.requiredQuantity}`);

      const result = await this.inventoryService.checkItemSufficient(
        payload.characterId,
        payload.configId,
        payload.requiredQuantity
      );

      if (result.data !== undefined) {
        return this.toSuccessResponse({ isSufficient: result.data });
      } else {
        return this.toErrorResponse('检查失败');
      }
    }, payload);
  }

  /**
   * 批量添加物品
   * 覆盖item.batchAddItems接口
   * 对应old项目的批量操作逻辑
   */
  @MessagePattern('inventory.batchAddItems')
  @CacheEvict({
    key: 'character:items:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async batchAddItems(@Payload() payload: { characterId: string; items: Array<{ configId: number; quantity: number }>; serverId?: string; injectedContext?: InjectedContext }): Promise<XResponse<any>> {
    return this.handleRequest(async () => {
      this.logger.log(`批量添加物品: ${payload.characterId}, 物品数量: ${payload.items.length}`);

      const results: any[] = [];

      for (const itemData of payload.items) {
        const result = await this.inventoryService.addItem(
          payload.characterId,
          itemData.configId,
          itemData.quantity
        );
        results.push({
          configId: itemData.configId,
          quantity: itemData.quantity,
          success: result.success,
          data: result.data
        });
      }

      return this.toSuccessResponse({ results });
    }, payload);
  }

  /**
   * 批量移除物品
   * 覆盖item.batchRemoveItems接口
   * 对应old项目的批量删除逻辑，优化API命名
   */
  @MessagePattern('inventory.batchRemoveItems')
  @CacheEvict({
    key: 'character:items:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async batchRemoveItems(@Payload() payload: { characterId: string; items: Array<{ itemId: string; quantity: number }>; serverId?: string; injectedContext?: InjectedContext }): Promise<XResponse<any>> {
    return this.handleRequest(async () => {
      this.logger.log(`批量移除物品: ${payload.characterId}, 物品数量: ${payload.items.length}`);
      
      const results: any[] = [];
      
      for (const itemData of payload.items) {
        const result = await this.inventoryService.removeItem(
          payload.characterId,
          itemData.itemId,
          itemData.quantity
        );
        results.push({
          itemId: itemData.itemId,
          quantity: itemData.quantity,
          success: result.success,
          data: result.data
        });
      }

      return this.toSuccessResponse({ results });
    }, payload);
  }

  // ========== 背包管理API（覆盖inventory.controller.ts）==========

  /**
   * 获取角色背包概览
   * 统一接口，包含自动初始化逻辑
   *
   * 覆盖inventory.getBagList接口
   * 对应old项目: makeClientBagList方法
   */
  @MessagePattern('inventory.getSummaries')
  @Cacheable({
    key: 'character:inventory:summary:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 300
  })
  async getInventorySummaries(@Payload() payload: { characterId: string; serverId?: string; injectedContext?: InjectedContext }): Promise<XResponse<any>> {
    return this.handleRequest(async () => {
      this.logger.log(`获取角色背包概览: ${payload.characterId}`);

      // 统一的获取逻辑，包含自动初始化
      let result = await this.inventoryService.getInventorySummaries(payload.characterId);

      if (XResultUtils.isFailure(result) || !result.data) {
        // 自动初始化
        const initResult = await this.inventoryService.initializeInventory(payload.characterId);
        if (XResultUtils.isSuccess(initResult)) {
          result = await this.inventoryService.getInventorySummaries(payload.characterId);
        }
      }

      return this.fromResult(result);
    }, payload);
  }

  /**
   * 获取指定页签数据
   * 覆盖inventory.getTab接口
   * 对应old项目: getOneBookMark方法
   */
  @MessagePattern('inventory.getTab')
  @Cacheable({
    key: 'character:tab:#{payload.characterId}:#{payload.tabId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 300
  })
  async getTab(@Payload() payload: { characterId: string; tabId: number; serverId?: string; injectedContext?: InjectedContext }): Promise<XResponse<any>> {
    return this.handleRequest(async () => {
      this.logger.log(`获取页签数据: ${payload.characterId}, 页签ID: ${payload.tabId}`);

      const result = await this.inventoryService.getInventory(payload.characterId);
      if (!result.data) {
        return this.toErrorResponse('角色不存在');
      }

      const tab = result.data.getOneTab(payload.tabId);
      if (tab) {
        return this.toSuccessResponse(tab);
      } else {
        return this.toErrorResponse('页签不存在');
      }
    }, payload);
  }

  /**
   * 添加物品到背包
   * 覆盖inventory.addItemToBag接口
   * 对应old项目: addToBag方法
   */
  @MessagePattern('inventory.addItemToBag')
  @CacheEvict({
    key: 'character:bag*:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async addItemToTab(@Payload() payload: { characterId: string; tabId: number; itemId: string; serverId?: string; injectedContext?: InjectedContext }): Promise<XResponse<any>> {
    return this.handleRequest(async () => {
      this.logger.log(`添加物品到背包: ${JSON.stringify(payload)}`);

      const result = await this.inventoryService.moveItem(
        payload.characterId,
        payload.itemId,
        payload.tabId
      );

      return this.fromResult(result);
    }, payload);
  }

  /**
   * 从背包移除物品
   * 覆盖inventory.removeItemFromBag接口
   * 对应old项目: removeFromBag方法
   */
  @MessagePattern('inventory.removeFromTab')
  @CacheEvict({
    key: 'character:bag*:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async removeFromTab(@Payload() payload: { characterId: string; tabId: number; itemId: string; serverId?: string; injectedContext?: InjectedContext }): Promise<XResponse<any>> {
    return this.handleRequest(async () => {
      this.logger.log(`从背包移除物品: ${JSON.stringify(payload)}`);

      const result = await this.inventoryService.getInventory(payload.characterId);
      if (!result.data) {
        return this.toErrorResponse('角色不存在');
      }

      const character = result.data;
      const success = character.removeFromTab(payload.tabId, payload.itemId);

      if (success) {
        await character.save();
        return this.toSuccessResponse({ success: true });
      } else {
        return this.toErrorResponse('移除失败');
      }
    }, payload);
  }

  /**
   * 扩展背包容量
   * 覆盖inventory.expandBag接口
   * 对应old项目: expandBag方法
   */
  @MessagePattern('inventory.expandBag')
  @CacheEvict({
    key: 'character:bag*:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async expandTab(@Payload() payload: { characterId: string; tabId: number; expandCount?: number; serverId?: string; injectedContext?: InjectedContext }): Promise<XResponse<any>> {
    return this.handleRequest(async () => {
      this.logger.log(`扩展背包: ${JSON.stringify(payload)}`);

      const result = await this.inventoryService.expandTab(
        payload.characterId,
        payload.tabId,
        payload.expandCount || 1
      );

      return this.fromResult(result);
    }, payload);
  }

  /**
   * 整理背包
   * 覆盖inventory.sortBag接口
   * 对应old项目: sortBag方法
   */
  @MessagePattern('inventory.sortBag')
  @CacheEvict({
    key: 'character:bag*:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async sortTab(@Payload() payload: { characterId: string; tabId: number; sortType?: string; serverId?: string; injectedContext?: InjectedContext }): Promise<XResponse<any>> {
    return this.handleRequest(async () => {
      this.logger.log(`整理背包: ${JSON.stringify(payload)}`);

      const result = await this.inventoryService.sortTab(
        payload.characterId,
        payload.tabId,
        payload.sortType || 'slot'
      );

      return this.fromResult(result);
    }, payload);
  }
}
