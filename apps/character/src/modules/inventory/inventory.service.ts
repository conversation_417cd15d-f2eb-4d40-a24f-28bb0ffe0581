/**
 * 统一的角色物品背包业务逻辑层
 * 合并ItemService和InventoryService的功能
 * 提供统一的业务接口和Result模式错误处理
 */

import { Injectable, Logger } from '@nestjs/common';
import { InventoryRepository } from '../../common/repositories/inventory.repository';
import {
  InventoryDocument,
  Item,
  InventoryTab,
  InventoryTabType,
  InventoryTabSummary,
  ItemUseResult,
  ItemReward,
  ConsumedItem,
  CurrencyUpdate
} from '../../common/schemas/inventory.schema';
import { XResult, XResultUtils } from '@libs/common/types/result.type';

/**
 * 统一的角色物品背包业务逻辑层
 * 提供完整的物品管理、背包管理、物品使用等业务功能
 *
 * 🎯 核心功能：
 * - 物品添加、移除、查询
 * - 背包页签管理和扩展
 * - 物品使用和奖励处理
 * - 物品堆叠和排序
 * - 过期物品清理
 *
 * 🚀 性能优化：
 * - 使用Repository层的缓存机制
 * - 批量操作减少数据库访问
 * - 事务支持确保数据一致性
 * - 智能堆叠算法优化存储
 */
@Injectable()
export class InventoryService {
  private readonly logger = new Logger(InventoryService.name);

  constructor(
    private readonly inventoryRepository: InventoryRepository
  ) {}

  // ========== 角色物品数据管理 ==========

  /**
   * 初始化角色背包数据
   * 为新角色创建默认的背包配置
   */
  async initializeCharacterInventory(characterId: string): Promise<XResult<InventoryDocument>> {
    try {
      // 检查是否已存在
      const existingResult = await this.inventoryRepository.findById(characterId);
      if (XResultUtils.isSuccess(existingResult) && existingResult.data) {
        this.logger.log(`角色背包已存在: ${characterId}`);
        return existingResult;
      }

      // 创建新的背包数据
      const result = await this.inventoryRepository.initializeInventory(characterId);
      
      if (XResultUtils.isSuccess(result)) {
        this.logger.log(`角色背包初始化成功: ${characterId}`);
      }

      return result;
    } catch (error) {
      this.logger.error(`初始化角色背包失败: ${characterId}`, error);
      return XResultUtils.error('初始化角色背包失败', 'INITIALIZE_BAG_FAILED');
    }
  }

  /**
   * 获取角色背包数据
   * 返回完整的角色物品数据
   */
  async getCharacterItems(uid: string): Promise<XResult<InventoryDocument>> {
    try {
      const result = await this.inventoryRepository.findById(uid);
      
      if (XResultUtils.isFailure(result) || !result.data) {
        // 如果不存在，自动初始化
        return this.initializeCharacterInventory(uid);
      }

      return result;
    } catch (error) {
      this.logger.error(`获取角色背包数据失败: ${uid}`, error);
      return XResultUtils.error('获取角色背包数据失败', 'GET_CHARACTER_ITEMS_FAILED');
    }
  }

  /**
   * 获取角色背包列表（客户端格式）
   * 返回优化的客户端显示数据
   */
  async getCharacterTabSummaries(characterId: string): Promise<XResult<InventoryTabSummary[]>> {
    try {
      const result = await this.inventoryRepository.getTabSummaries(characterId);
      
      if (XResultUtils.isSuccess(result)) {
        this.logger.debug(`获取角色背包列表成功: ${characterId}, 页签数量: ${result.data.length}`);
      }

      return result;
    } catch (error) {
      this.logger.error(`获取角色背包列表失败: ${characterId}`, error);
      return XResultUtils.error('获取角色背包列表失败', 'GET_BAG_LIST_FAILED');
    }
  }

  // ========== 物品管理方法 ==========

  /**
   * 添加物品到角色背包
   * 支持自动堆叠和背包空间检查
   */
  async addItemToCharacter(
    characterId: string,
    configId: number,
    quantity: number,
    itemConfig?: any
  ): Promise<XResult<{ success: boolean; addedIds: string[]; slotsUsed: number }>> {
    try {
      // 验证参数
      if (!characterId || !configId || quantity <= 0) {
        return XResultUtils.error('参数无效', 'INVALID_PARAMETERS');
      }

      const result = await this.inventoryRepository.addItem(characterId, configId, quantity, itemConfig);
      
      if (XResultUtils.isSuccess(result)) {
        this.logger.log(`添加物品成功: 角色${characterId}, 物品${configId}, 数量${quantity}, 使用槽位${result.data.slotsUsed}`);
      }

      return result;
    } catch (error) {
      this.logger.error(`添加物品失败: 角色${characterId}, 物品${configId}, 数量${quantity}`, error);
      return XResultUtils.error('添加物品失败', 'ADD_ITEM_FAILED');
    }
  }

  /**
   * 从角色背包移除物品
   * 支持部分移除和完全移除
   */
  async removeItemFromCharacter(
    characterId: string,
    itemId: string,
    quantity?: number
  ): Promise<XResult<{ success: boolean; removedQuantity: number }>> {
    try {
      // 验证参数
      if (!characterId || !itemId) {
        return XResultUtils.error('参数无效', 'INVALID_PARAMETERS');
      }

      if (quantity !== undefined && quantity <= 0) {
        return XResultUtils.error('移除数量必须大于0', 'INVALID_QUANTITY');
      }

      const result = await this.inventoryRepository.removeItem(characterId, itemId, quantity);
      
      if (XResultUtils.isSuccess(result)) {
        this.logger.log(`移除物品成功: 角色${characterId}, 物品${itemId}, 移除数量${result.data.removedQuantity}`);
      }

      return result;
    } catch (error) {
      this.logger.error(`移除物品失败: 角色${characterId}, 物品${itemId}`, error);
      return XResultUtils.error('移除物品失败', 'REMOVE_ITEM_FAILED');
    }
  }

  /**
   * 移动物品到指定页签和位置
   * 支持背包整理和物品重新排列
   */
  async moveItemToTab(
    characterId: string,
    itemId: string,
    tabId: number,
    slot?: number
  ): Promise<XResult<{ success: boolean; newSlot: number }>> {
    try {
      // 验证参数
      if (!characterId || !itemId || !tabId) {
        return XResultUtils.error('参数无效', 'INVALID_PARAMETERS');
      }

      const result = await this.inventoryRepository.moveItem(characterId, itemId, tabId, slot);

      if (XResultUtils.isSuccess(result)) {
        this.logger.log(`移动物品成功: 角色${characterId}, 物品${itemId}, 页签${tabId}, 位置${result.data.newSlot}`);
      }

      return result;
    } catch (error) {
      this.logger.error(`移动物品失败: 角色${characterId}, 物品${itemId}`, error);
      return XResultUtils.error('移动物品失败', 'MOVE_ITEM_FAILED');
    }
  }

  /**
   * 批量添加物品
   * 优化的批量操作，减少数据库访问次数
   */
  async batchAddItems(
    characterId: string,
    itemsToAdd: Array<{configId: number, quantity: number}>
  ): Promise<XResult<{ success: boolean; addedIds: string[] }>> {
    try {
      // 验证参数
      if (!characterId || !Array.isArray(itemsToAdd) || itemsToAdd.length === 0) {
        return XResultUtils.error('参数无效', 'INVALID_PARAMETERS');
      }

      // 验证每个物品数据
      for (const item of itemsToAdd) {
        if (!item.configId || item.quantity <= 0) {
          return XResultUtils.error('物品数据无效', 'INVALID_ITEM_DATA');
        }
      }

      const result = await this.inventoryRepository.batchAddItems(characterId, itemsToAdd);
      
      if (XResultUtils.isSuccess(result)) {
        this.logger.log(`批量添加物品成功: 角色${characterId}, 物品种类${itemsToAdd.length}, 实例数量${result.data.addedIds.length}`);
      }

      return result;
    } catch (error) {
      this.logger.error(`批量添加物品失败: 角色${characterId}`, error);
      return XResultUtils.error('批量添加物品失败', 'BATCH_ADD_ITEMS_FAILED');
    }
  }

  // ========== 查询和统计方法 ==========

  /**
   * 根据配置ID查询物品数量
   * 返回角色拥有的指定物品总数量
   */
  async getItemQuantityByResId(characterId: string, configId: number): Promise<XResult<number>> {
    try {
      if (!characterId || !configId) {
        return XResultUtils.error('参数无效', 'INVALID_PARAMETERS');
      }

      const result = await this.inventoryRepository.getItemQuantityByConfigId(characterId, configId);
      
      if (XResultUtils.isSuccess(result)) {
        this.logger.debug(`查询物品数量成功: 角色${characterId}, 物品${configId}, 数量${result.data}`);
      }

      return result;
    } catch (error) {
      this.logger.error(`查询物品数量失败: 角色${characterId}, 物品${configId}`, error);
      return XResultUtils.error('查询物品数量失败', 'GET_ITEM_QUANTITY_FAILED');
    }
  }

  /**
   * 检查物品是否充足
   * 验证角色是否拥有足够数量的指定物品
   */
  async checkItemSufficient(characterId: string, configId: number, needQuantity: number): Promise<XResult<boolean>> {
    try {
      if (!characterId || !configId || needQuantity <= 0) {
        return XResultUtils.error('参数无效', 'INVALID_PARAMETERS');
      }

      const result = await this.inventoryRepository.checkItemSufficient(characterId, configId, needQuantity);
      
      if (XResultUtils.isSuccess(result)) {
        this.logger.debug(`检查物品充足性: 角色${characterId}, 物品${configId}, 需要${needQuantity}, 充足${result.data}`);
      }

      return result;
    } catch (error) {
      this.logger.error(`检查物品充足性失败: 角色${characterId}, 物品${configId}`, error);
      return XResultUtils.error('检查物品充足性失败', 'CHECK_ITEM_SUFFICIENT_FAILED');
    }
  }

  /**
   * 根据页签ID获取物品列表
   * 返回指定页签中的所有物品
   */
  async getItemsByTab(characterId: string, tabId: number): Promise<XResult<Item[]>> {
    try {
      if (!characterId || !tabId) {
        return XResultUtils.error('参数无效', 'INVALID_PARAMETERS');
      }

      const result = await this.inventoryRepository.getItemsByTab(characterId, tabId);

      if (XResultUtils.isSuccess(result)) {
        this.logger.debug(`获取页签物品成功: 角色${characterId}, 页签${tabId}, 物品数量${result.data.length}`);
      }

      return result;
    } catch (error) {
      this.logger.error(`获取页签物品失败: 角色${characterId}, 页签${tabId}`, error);
      return XResultUtils.error('获取页签物品失败', 'GET_ITEMS_BY_TAB_FAILED');
    }
  }

  // ========== 背包管理方法 ==========

  /**
   * 扩展背包页签
   * 增加指定页签的容量
   */
  async expandBag(
    characterId: string,
    tabId: number,
    expandCount: number = 1
  ): Promise<XResult<{ success: boolean; cost: number; newCapacity: number }>> {
    try {
      if (!characterId || !tabId || expandCount <= 0) {
        return XResultUtils.error('参数无效', 'INVALID_PARAMETERS');
      }

      const result = await this.inventoryRepository.expandTab(characterId, tabId, expandCount);

      if (XResultUtils.isSuccess(result)) {
        this.logger.log(`扩展背包成功: 角色${characterId}, 页签${tabId}, 扩展${expandCount}次, 费用${result.data.cost}`);
      }

      return result;
    } catch (error) {
      this.logger.error(`扩展背包失败: 角色${characterId}, 页签${tabId}`, error);
      return XResultUtils.error('扩展背包失败', 'EXPAND_BAG_FAILED');
    }
  }

  /**
   * 背包排序
   * 按指定规则对页签中的物品进行排序
   */
  async sortBag(
    characterId: string,
    tabId: number,
    sortType: string = 'slot'
  ): Promise<XResult<{ success: boolean }>> {
    try {
      if (!characterId || !tabId) {
        return XResultUtils.error('参数无效', 'INVALID_PARAMETERS');
      }

      const validSortTypes = ['slot', 'configId', 'type', 'quantity', 'acquiredTime'];
      if (!validSortTypes.includes(sortType)) {
        return XResultUtils.error('排序类型无效', 'INVALID_SORT_TYPE');
      }

      const result = await this.inventoryRepository.sortTab(characterId, tabId, sortType);

      if (XResultUtils.isSuccess(result)) {
        this.logger.log(`背包排序成功: 角色${characterId}, 页签${tabId}, 排序类型${sortType}`);
      }

      return result;
    } catch (error) {
      this.logger.error(`背包排序失败: 角色${characterId}, 页签${tabId}`, error);
      return XResultUtils.error('背包排序失败', 'SORT_BAG_FAILED');
    }
  }

  /**
   * 清理过期物品
   * 自动清理角色背包中的过期物品
   */
  async cleanExpiredItems(characterId: string): Promise<XResult<{ cleanedCount: number }>> {
    try {
      if (!characterId) {
        return XResultUtils.error('参数无效', 'INVALID_PARAMETERS');
      }

      const result = await this.inventoryRepository.cleanExpiredItems(characterId);

      if (XResultUtils.isSuccess(result) && result.data.cleanedCount > 0) {
        this.logger.log(`清理过期物品成功: 角色${characterId}, 清理${result.data.cleanedCount}个物品`);
      }

      return result;
    } catch (error) {
      this.logger.error(`清理过期物品失败: 角色${characterId}`, error);
      return XResultUtils.error('清理过期物品失败', 'CLEAN_EXPIRED_ITEMS_FAILED');
    }
  }

  // ========== 物品使用方法 ==========

  /**
   * 使用物品
   * 处理物品使用逻辑，包括消耗品、礼包等
   */
  async useItem(
    characterId: string,
    itemId: string,
    useCount: number = 1
  ): Promise<XResult<ItemUseResult>> {
    try {
      if (!characterId || !itemId || useCount <= 0) {
        return XResultUtils.error('参数无效', 'INVALID_PARAMETERS');
      }

      // 获取角色物品数据
      const characterResult = await this.getCharacterItems(characterId);
      if (XResultUtils.isFailure(characterResult) || !characterResult.data) {
        return XResultUtils.error('角色不存在', 'CHARACTER_NOT_FOUND');
      }

      const character = characterResult.data;
      const item = character.getItem(itemId);

      if (!item) {
        return XResultUtils.error('物品不存在', 'ITEM_NOT_FOUND');
      }

      if (item.quantity < useCount) {
        return XResultUtils.error('物品数量不足', 'INSUFFICIENT_ITEM_QUANTITY');
      }

      // TODO: 根据物品配置处理使用逻辑
      // 这里需要集成libs/game-config来获取物品配置
      // const itemConfig = await this.gameConfigService.getItemConfig(item.configId);

      // 简化版使用逻辑
      const useResult: ItemUseResult = {
        success: true,
        code: 0,
        message: '物品使用成功',
        rewards: [],
        consumedItems: [{
          itemId: itemId,
          configId: item.configId,
          quantity: useCount
        }],
        updatedCurrency: []
      };

      // 消耗物品
      const removeResult = await this.removeItemFromCharacter(characterId, itemId, useCount);
      if (XResultUtils.isFailure(removeResult)) {
        return XResultUtils.error('消耗物品失败', 'CONSUME_ITEM_FAILED');
      }

      this.logger.log(`使用物品成功: 角色${characterId}, 物品${itemId}, 使用数量${useCount}`);

      return XResultUtils.ok(useResult);
    } catch (error) {
      this.logger.error(`使用物品失败: 角色${characterId}, 物品${itemId}`, error);
      return XResultUtils.error('使用物品失败', 'USE_ITEM_FAILED');
    }
  }

  /**
   * 批量使用物品
   * 优化的批量使用操作
   */
  async batchUseItems(
    characterId: string,
    itemsToUse: Array<{itemId: string, useCount: number}>
  ): Promise<XResult<{ success: boolean; results: ItemUseResult[] }>> {
    try {
      if (!characterId || !Array.isArray(itemsToUse) || itemsToUse.length === 0) {
        return XResultUtils.error('参数无效', 'INVALID_PARAMETERS');
      }

      const results: ItemUseResult[] = [];

      for (const {itemId, useCount} of itemsToUse) {
        const useResult = await this.useItem(characterId, itemId, useCount);
        if (XResultUtils.isSuccess(useResult)) {
          results.push(useResult.data);
        } else {
          // 记录失败但继续处理其他物品
          this.logger.warn(`批量使用物品中的单个物品失败: ${itemId}`);
          results.push({
            success: false,
            code: -1,
            message: '物品使用失败',
            rewards: [],
            consumedItems: [],
            updatedCurrency: []
          });
        }
      }

      this.logger.log(`批量使用物品完成: 角色${characterId}, 处理${itemsToUse.length}个物品`);

      return XResultUtils.ok({
        success: true,
        results: results
      });
    } catch (error) {
      this.logger.error(`批量使用物品失败: 角色${characterId}`, error);
      return XResultUtils.error('批量使用物品失败', 'BATCH_USE_ITEMS_FAILED');
    }
  }
}
