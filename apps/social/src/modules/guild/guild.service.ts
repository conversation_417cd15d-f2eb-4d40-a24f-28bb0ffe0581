/**
 * 公会系统服务
 * 基于old项目association.js实体迁移，使用Repository模式
 */

import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { ErrorCode, ErrorMessages } from '@libs/game-constants';
import { GuildRepository } from '@social/common/repositories/guild.repository';

// 公会职位枚举
enum GuildPosition {
  PRESIDENT = 1,      // 会长
  VICE_PRESIDENT = 2, // 副会长
  MEMBER = 3,         // 普通成员
}
import { 
  CreateGuildDto,
  ApplyJoinGuildDto,
  ProcessApplicationDto,
  LeaveGuildDto,
  UpdateGuildDto,
  GuildInfoDto,
  GuildMemberListDto,
  GuildApplicationListDto,
  GuildOperationResultDto,
  SearchGuildDto,
  GuildListDto,
  ChangePositionDto,
  TransferPresidencyDto
} from '@social/common/dto/guild.dto';


@Injectable()
export class GuildService {
  private readonly logger = new Logger(GuildService.name);

  constructor(
    private readonly guildRepository: GuildRepository,
  ) {}

  /**
   * 创建公会（基于old项目createGuild方法）
   */
  async createGuild(
    characterId: string,
    characterName: string,
    guildName: string,
    faceId: number,
    strength: number,
    gid: string,
    faceUrl: string,
    frontendId?: string,
    sessionId?: string
  ): Promise<GuildOperationResultDto> {
    try {
      // 检查玩家是否已在公会中
      const existingGuild = await this.guildRepository.findByCharacterId(characterId);
      if (existingGuild) {
        throw new BadRequestException({
          code: ErrorCode.ALREADY_IN_GUILD,
          message: ErrorMessages[ErrorCode.ALREADY_IN_GUILD],
        });
      }

      // 检查公会名称是否已存在
      const nameExists = await this.guildRepository.findByName(guildName);
      if (nameExists) {
        throw new BadRequestException({
          code: ErrorCode.GUILD_NAME_EXISTS,
          message: ErrorMessages[ErrorCode.GUILD_NAME_EXISTS],
        });
      }

      // 生成公会ID
      const guildId = this.generateGuildId();

      // 创建公会数据
      const guildData = {
        guildId: guildId,
        creator: characterName,
        createTime: Date.now(),
        guildNotice: "欢迎大家加入公会",
        faceId,
        guildName: guildName,
        guildLevel: 1,
        exp: 0,
        contribute: 0,
        allCharacter: [{
          characterId,
          characterName,
          isOnLine: 1,
          leaveTime: Date.now(),
          exp: 0,
          contribute: 0,
          faceUrl,
          strength,
          pos: GuildPosition.PRESIDENT,
          gid,
          frontendId,
          sessionId,
          joinTime: Date.now(),
        }],
        approvalList: [],
        vicePresident: 0,
        notifyList: [{
          time: Date.now(),
          text: `${characterName} 创建了公会`,
        }],
      };

      // 保存公会
      const savedGuild = await this.guildRepository.create(guildData);

      this.logger.log(`公会创建成功: ${guildName}, 会长: ${characterName}`);

      return {
        success: true,
        guildId,
        operationTime: Date.now(),
        message: '公会创建成功',
      };
    } catch (error) {
      this.logger.error('创建公会失败', error);
      throw error;
    }
  }

  /**
   * 申请加入公会（基于old项目applyJoinGuild方法）
   */
  async applyJoinGuild(
    guildId: string,
    characterId: string,
    characterName: string,
    gid: string,
    faceUrl: string,
    strength: number,
    frontendId?: string,
    sessionId?: string
  ): Promise<GuildOperationResultDto> {
    try {
      // 检查玩家是否已在公会中
      const existingGuild = await this.guildRepository.findByCharacterId(characterId);
      if (existingGuild) {
        throw new BadRequestException({
          code: ErrorCode.ALREADY_IN_GUILD,
          message: ErrorMessages[ErrorCode.ALREADY_IN_GUILD],
        });
      }

      // 查找目标公会
      const guild = await this.guildRepository.findById(guildId);
      if (!guild) {
        throw new NotFoundException({
          code: ErrorCode.GUILD_NOT_FOUND,
          message: ErrorMessages[ErrorCode.GUILD_NOT_FOUND],
        });
      }

      // 检查公会是否已满
      if (guild.isFull) {
        throw new BadRequestException({
          code: ErrorCode.GUILD_FULL,
          message: ErrorMessages[ErrorCode.GUILD_FULL],
        });
      }

      // 检查是否已有申请
      const existingApplication = guild.approvalList.find(app => app.characterId === characterId);
      if (existingApplication) {
        throw new BadRequestException({
          code: ErrorCode.GUILD_APPLICATION_EXISTS,
          message: ErrorMessages[ErrorCode.GUILD_APPLICATION_EXISTS],
        });
      }

      // 添加申请
      const applicationData = {
        characterId,
        characterName,
        askTime: Date.now(),
        gid,
        faceUrl,
        strength,
        frontendId,
        sessionId,
      };

      await this.guildRepository.addApplication(guildId, applicationData);

      // 添加日志
      await this.guildRepository.addNotify(guildId, `${characterName} 申请加入公会`);

      this.logger.log(`公会申请提交: ${characterName} -> ${guild.guildName}`);

      return {
        success: true,
        guildId,
        operationTime: Date.now(),
        message: '申请已提交',
      };
    } catch (error) {
      this.logger.error('申请加入公会失败', error);
      throw error;
    }
  }

  /**
   * 处理公会申请（基于old项目processApplication方法）
   */
  async processApplication(
    guildId: string,
    approverId: string,
    applicantId: string,
    approved: boolean
  ): Promise<GuildOperationResultDto> {
    try {
      // 查找公会
      const guild = await this.guildRepository.findById(guildId);
      if (!guild) {
        throw new NotFoundException({
          code: ErrorCode.GUILD_NOT_FOUND,
          message: ErrorMessages[ErrorCode.GUILD_NOT_FOUND],
        });
      }

      // 检查处理者权限
      const approver = guild.allCharacter.find(member => member.characterId === approverId);
      if (!approver || (approver.pos !== GuildPosition.PRESIDENT && approver.pos !== GuildPosition.VICE_PRESIDENT)) {
        throw new BadRequestException({
          code: ErrorCode.GUILD_NO_PERMISSION,
          message: ErrorMessages[ErrorCode.GUILD_NO_PERMISSION],
        });
      }

      // 查找申请
      const application = guild.approvalList.find(app => app.characterId === applicantId);
      if (!application) {
        throw new NotFoundException({
          code: ErrorCode.GUILD_APPLICATION_NOT_FOUND,
          message: ErrorMessages[ErrorCode.GUILD_APPLICATION_NOT_FOUND],
        });
      }

      // 移除申请
      await this.guildRepository.removeApplication(guildId, applicantId);

      if (approved) {
        // 检查公会是否还有空位
        if (guild.isFull) {
          throw new BadRequestException({
            code: ErrorCode.GUILD_FULL,
            message: ErrorMessages[ErrorCode.GUILD_FULL],
          });
        }

        // 添加成员
        const memberData = {
          characterId: application.characterId,
          characterName: application.characterName,
          isOnLine: 1,
          leaveTime: Date.now(),
          exp: 0,
          contribute: 0,
          faceUrl: application.faceUrl,
          strength: application.strength,
          pos: GuildPosition.MEMBER,
          gid: application.gid,
          frontendId: application.frontendId,
          sessionId: application.sessionId,
          joinTime: Date.now(),
        };

        await this.guildRepository.addMember(guildId, memberData);

        // 添加日志
        await this.guildRepository.addNotify(guildId, `${application.characterName} 加入了公会`);

        this.logger.log(`公会申请通过: ${application.characterName} 加入 ${guild.guildName}`);
      } else {
        // 添加日志
        await this.guildRepository.addNotify(guildId, `${application.characterName} 的申请被拒绝`);

        this.logger.log(`公会申请拒绝: ${application.characterName} -> ${guild.guildName}`);
      }

      return {
        success: true,
        guildId,
        operationTime: Date.now(),
        message: approved ? '申请已通过' : '申请已拒绝',
      };
    } catch (error) {
      this.logger.error('处理公会申请失败', error);
      throw error;
    }
  }

  /**
   * 退出公会（基于old项目leaveGuild方法）
   */
  async leaveGuild(characterId: string, exitType: number = 1): Promise<GuildOperationResultDto> {
    try {
      // 查找玩家所在公会
      const guild = await this.guildRepository.findByCharacterId(characterId);
      if (!guild) {
        throw new NotFoundException({
          code: ErrorCode.NOT_IN_GUILD,
          message: ErrorMessages[ErrorCode.NOT_IN_GUILD],
        });
      }

      // 查找成员信息
      const member = guild.allCharacter.find(m => m.characterId === characterId);
      if (!member) {
        throw new NotFoundException({
          code: ErrorCode.NOT_IN_GUILD,
          message: ErrorMessages[ErrorCode.NOT_IN_GUILD],
        });
      }

      // 检查是否是会长
      if (member.pos === GuildPosition.PRESIDENT) {
        throw new BadRequestException({
          code: ErrorCode.GUILD_PRESIDENT_CANNOT_LEAVE,
          message: ErrorMessages[ErrorCode.GUILD_PRESIDENT_CANNOT_LEAVE],
        });
      }

      // 移除成员
      await this.guildRepository.removeMember(guild.guildId, characterId);

      // 添加日志
      const exitText = exitType === 1 ? '主动退出' : '被踢出';
      await this.guildRepository.addNotify(guild.guildId, `${member.characterName} ${exitText}了公会`);

      this.logger.log(`公会退出: ${member.characterName} ${exitText} ${guild.guildName}`);

      return {
        success: true,
        guildId: guild.guildId,
        operationTime: Date.now(),
        message: '退出公会成功',
      };
    } catch (error) {
      this.logger.error('退出公会失败', error);
      throw error;
    }
  }

  /**
   * 获取公会信息（基于old项目getGuildInfo方法）
   */
  async getGuildInfo(guildId: string): Promise<GuildInfoDto> {
    try {
      const guild = await this.guildRepository.findById(guildId);
      if (!guild) {
        throw new NotFoundException({
          code: ErrorCode.GUILD_NOT_FOUND,
          message: ErrorMessages[ErrorCode.GUILD_NOT_FOUND],
        });
      }

      return {
        guildId: guild.guildId,
        guildName: guild.guildName,
        creator: guild.creator,
        createTime: guild.createTime,
        guildNotice: guild.guildNotice,
        faceId: guild.faceId,
        guildLevel: guild.guildLevel,
        exp: guild.exp,
        contribute: guild.contribute,
        memberCount: guild.memberCount,
        maxMembers: 10, // TODO: 从配置获取
        onlineMemberCount: guild.onlineMemberCount,
        vicePresidentCount: guild.vicePresidents.length,
        maxVicePresidents: 2, // TODO: 从配置获取
        applicationCount: guild.pendingApplications,
        isFull: guild.isFull,
      };
    } catch (error) {
      this.logger.error('获取公会信息失败', error);
      throw error;
    }
  }

  /**
   * 获取公会成员列表（基于old项目getGuildMembers方法）
   */
  async getGuildMembers(guildId: string): Promise<GuildMemberListDto> {
    try {
      const guild = await this.guildRepository.findById(guildId);
      if (!guild) {
        throw new NotFoundException({
          code: ErrorCode.GUILD_NOT_FOUND,
          message: ErrorMessages[ErrorCode.GUILD_NOT_FOUND],
        });
      }

      const members = guild.allCharacter.map(member => ({
        characterId: member.characterId,
        characterName: member.characterName,
        isOnLine: member.isOnLine,
        leaveTime: member.leaveTime,
        exp: member.exp,
        contribute: member.contribute,
        faceUrl: member.faceUrl,
        strength: member.strength,
        pos: member.pos,
        gid: member.gid,
        frontendId: member.frontendId,
        sessionId: member.sessionId,
        joinTime: member.joinTime,
      }));

      return {
        members,
        total: members.length,
        maxMembers: 10, // TODO: 从配置获取
      };
    } catch (error) {
      this.logger.error('获取公会成员列表失败', error);
      throw error;
    }
  }

  /**
   * 更新公会信息（基于old项目updateGuildInfo方法）
   */
  async updateGuildInfo(guildId: string, operatorId: string, updateData: UpdateGuildDto): Promise<GuildOperationResultDto> {
    try {
      const guild = await this.guildRepository.findById(guildId);
      if (!guild) {
        throw new NotFoundException({
          code: ErrorCode.GUILD_NOT_FOUND,
          message: ErrorMessages[ErrorCode.GUILD_NOT_FOUND],
        });
      }

      // 检查操作者权限
      const operator = guild.allCharacter.find(member => member.characterId === operatorId);
      if (!operator || (operator.pos !== GuildPosition.PRESIDENT && operator.pos !== GuildPosition.VICE_PRESIDENT)) {
        throw new BadRequestException({
          code: ErrorCode.GUILD_NO_PERMISSION,
          message: ErrorMessages[ErrorCode.GUILD_NO_PERMISSION],
        });
      }

      // 更新公会信息
      const updates: any = {};
      if (updateData.notice !== undefined) {
        updates.notice = updateData.notice;
      }
      if (updateData.faceId !== undefined) {
        updates.faceId = updateData.faceId;
      }

      await this.guildRepository.update(guildId, updates);

      // 添加日志
      await this.guildRepository.addNotify(guildId, `${operator.characterName} 更新了公会信息`);

      this.logger.log(`公会信息更新: ${guild.guildName}, 操作者: ${operator.characterName}`);

      return {
        success: true,
        guildId,
        operationTime: Date.now(),
        message: '公会信息更新成功',
      };
    } catch (error) {
      this.logger.error('更新公会信息失败', error);
      throw error;
    }
  }

  /**
   * 踢出成员（基于old项目kickMember方法）
   */
  async kickMember(guildId: string, operatorId: string, targetCharacterId: string): Promise<GuildOperationResultDto> {
    try {
      const guild = await this.guildRepository.findById(guildId);
      if (!guild) {
        throw new NotFoundException({
          code: ErrorCode.GUILD_NOT_FOUND,
          message: ErrorMessages[ErrorCode.GUILD_NOT_FOUND],
        });
      }

      // 检查操作者权限
      const operator = guild.allCharacter.find(member => member.characterId === operatorId);
      if (!operator || (operator.pos !== GuildPosition.PRESIDENT && operator.pos !== GuildPosition.VICE_PRESIDENT)) {
        throw new BadRequestException({
          code: ErrorCode.GUILD_NO_PERMISSION,
          message: ErrorMessages[ErrorCode.GUILD_NO_PERMISSION],
        });
      }

      // 查找目标成员
      const targetMember = guild.allCharacter.find(member => member.characterId === targetCharacterId);
      if (!targetMember) {
        throw new NotFoundException({
          code: ErrorCode.GUILD_MEMBER_NOT_FOUND,
          message: ErrorMessages[ErrorCode.GUILD_MEMBER_NOT_FOUND],
        });
      }

      // 不能踢出会长
      if (targetMember.pos === GuildPosition.PRESIDENT) {
        throw new BadRequestException({
          code: ErrorCode.GUILD_CANNOT_KICK_PRESIDENT,
          message: ErrorMessages[ErrorCode.GUILD_CANNOT_KICK_PRESIDENT],
        });
      }

      // 副会长不能踢出副会长
      if (operator.pos === GuildPosition.VICE_PRESIDENT && targetMember.pos === GuildPosition.VICE_PRESIDENT) {
        throw new BadRequestException({
          code: ErrorCode.GUILD_NO_PERMISSION,
          message: ErrorMessages[ErrorCode.GUILD_NO_PERMISSION],
        });
      }

      // 移除成员
      await this.guildRepository.removeMember(guildId, targetCharacterId);

      // 添加日志
      await this.guildRepository.addNotify(guildId, `${targetMember.characterName} 被 ${operator.characterName} 踢出公会`);

      this.logger.log(`踢出成员: ${targetMember.characterName} 被 ${operator.characterName} 踢出 ${guild.guildName}`);

      return {
        success: true,
        guildId,
        operationTime: Date.now(),
        message: '成员已被踢出',
      };
    } catch (error) {
      this.logger.error('踢出成员失败', error);
      throw error;
    }
  }







  // ==================== 私有辅助方法 ====================



  /**
   * 生成公会ID
   */
  private generateGuildId(): string {
    return `guild_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // ==================== 缺失功能补充 ====================

  /**
   * 获取申请列表
   * 对应old项目中的getApprovalList方法
   */
  async getApplicationList(characterId: string, guildId: string): Promise<any> {
    try {
      const guild = await this.guildRepository.findByGuildId(guildId);
      if (!guild) {
        throw new NotFoundException({
          code: ErrorCode.GUILD_NOT_FOUND,
          message: ErrorMessages[ErrorCode.GUILD_NOT_FOUND],
        });
      }

      // 检查权限
      if (!this.checkJurisdiction(guild, characterId)) {
        throw new BadRequestException({
          code: ErrorCode.GUILD_NO_PERMISSION,
          message: ErrorMessages[ErrorCode.GUILD_NO_PERMISSION],
        });
      }

      // 按申请时间排序
      const sortedApplications = guild.approvalList.sort((a, b) => a.askTime - b.askTime);

      this.logger.log(`获取申请列表: ${guildId}, 申请数量: ${sortedApplications.length}`);

      return {
        approvalList: sortedApplications,
        total: sortedApplications.length,
      };
    } catch (error) {
      this.logger.error('获取申请列表失败', error);
      throw error;
    }
  }

  /**
   * 同意加入申请
   * 对应old项目中的agreeJoinAssociation方法
   */
  async approveApplication(characterId: string, guildId: string, agreeId: string): Promise<GuildOperationResultDto> {
    try {
      const guild = await this.guildRepository.findByGuildId(guildId);
      if (!guild) {
        throw new NotFoundException({
          code: ErrorCode.GUILD_NOT_FOUND,
          message: ErrorMessages[ErrorCode.GUILD_NOT_FOUND],
        });
      }

      // 检查权限
      if (!this.checkJurisdiction(guild, characterId)) {
        throw new BadRequestException({
          code: ErrorCode.GUILD_NO_PERMISSION,
          message: ErrorMessages[ErrorCode.GUILD_NO_PERMISSION],
        });
      }

      // 检查人数限制
      if (guild.allCharacter.length >= 10) {
        throw new BadRequestException({
          code: ErrorCode.GUILD_MEMBER_LIMIT,
          message: ErrorMessages[ErrorCode.GUILD_MEMBER_LIMIT],
        });
      }

      // 查找申请
      const applicationIndex = guild.approvalList.findIndex(app => app.characterId === agreeId);
      if (applicationIndex === -1) {
        throw new NotFoundException({
          code: ErrorCode.APPLICATION_NOT_FOUND,
          message: ErrorMessages[ErrorCode.APPLICATION_NOT_FOUND],
        });
      }

      const application = guild.approvalList[applicationIndex];

      // 添加成员
      const newMember = {
        characterId: agreeId,
        characterName: application.characterName,
        pos: 3, // 普通成员
        joinTime: Date.now(),
        contribute: 0,
        isOnLine: 1,
        leaveTime: 0,
        exp: 0, // 添加缺失的exp属性
        gid: application.gid,
        faceUrl: application.faceUrl,
        strength: application.strength,
        frontendId: application.frontendId,
        sessionId: application.sessionId,
      };

      guild.allCharacter.push(newMember);

      // 删除申请
      guild.approvalList.splice(applicationIndex, 1);

      // 添加公会日志
      this.addNotify(guild, 'member_join', application.characterName);

      await guild.save();

      // TODO: 通知Character服务更新玩家公会状态

      this.logger.log(`同意申请成功: ${guildId}, 新成员: ${agreeId}`);

      return {
        success: true,
        operationTime: Date.now(),
        message: '成员加入成功',
        newMember,
      };
    } catch (error) {
      this.logger.error('同意申请失败', error);
      throw error;
    }
  }

  /**
   * 拒绝加入申请
   * 对应old项目中的agreeJoinAssociation方法（type=2）
   */
  async rejectApplication(characterId: string, guildId: string, rejectId: string): Promise<GuildOperationResultDto> {
    try {
      const guild = await this.guildRepository.findByGuildId(guildId);
      if (!guild) {
        throw new NotFoundException({
          code: ErrorCode.GUILD_NOT_FOUND,
          message: ErrorMessages[ErrorCode.GUILD_NOT_FOUND],
        });
      }

      // 检查权限
      if (!this.checkJurisdiction(guild, characterId)) {
        throw new BadRequestException({
          code: ErrorCode.GUILD_NO_PERMISSION,
          message: ErrorMessages[ErrorCode.GUILD_NO_PERMISSION],
        });
      }

      // 查找申请
      const applicationIndex = guild.approvalList.findIndex(app => app.characterId === rejectId);
      if (applicationIndex === -1) {
        throw new NotFoundException({
          code: ErrorCode.APPLICATION_NOT_FOUND,
          message: ErrorMessages[ErrorCode.APPLICATION_NOT_FOUND],
        });
      }

      // 删除申请
      guild.approvalList.splice(applicationIndex, 1);

      await guild.save();

      // TODO: 通知Character服务更新玩家申请状态

      this.logger.log(`拒绝申请成功: ${guildId}, 申请者: ${rejectId}`);

      return {
        success: true,
        operationTime: Date.now(),
        message: '申请已拒绝',
      };
    } catch (error) {
      this.logger.error('拒绝申请失败', error);
      throw error;
    }
  }

  /**
   * 职位变更
   * 对应old项目中的changeAssociationPosition方法
   */
  async changePosition(characterId: string, guildId: string, targetCharacterId: string, newPosition: number): Promise<GuildOperationResultDto> {
    try {
      const guild = await this.guildRepository.findByGuildId(guildId);
      if (!guild) {
        throw new NotFoundException({
          code: ErrorCode.GUILD_NOT_FOUND,
          message: ErrorMessages[ErrorCode.GUILD_NOT_FOUND],
        });
      }

      const executorPos = this.getCharacterPosition(guild, characterId);
      const targetPos = this.getCharacterPosition(guild, targetCharacterId);

      if (executorPos === 0 || targetPos === 0) {
        throw new BadRequestException({
          code: ErrorCode.CHARACTER_NOT_IN_GUILD,
          message: ErrorMessages[ErrorCode.CHARACTER_NOT_IN_GUILD],
        });
      }

      // 权限检查
      if (executorPos === 3) {
        throw new BadRequestException({
          code: ErrorCode.GUILD_NO_PERMISSION,
          message: ErrorMessages[ErrorCode.GUILD_NO_PERMISSION],
        });
      }

      // 不能操作比自己职位高或相等的人
      if (newPosition < executorPos || executorPos === targetPos) {
        throw new BadRequestException({
          code: ErrorCode.GUILD_NO_PERMISSION,
          message: ErrorMessages[ErrorCode.GUILD_NO_PERMISSION],
        });
      }

      // 职位没变化
      if (targetPos === newPosition) {
        return {
          success: true,
          operationTime: Date.now(),
          message: '职位无变化',
        };
      }

      // 副会长数量限制
      if (executorPos === 1 && newPosition === 2 && targetPos !== 2) {
        if (guild.vicePresident >= 2) {
          throw new BadRequestException({
            code: ErrorCode.VICE_PRESIDENT_LIMIT,
            message: ErrorMessages[ErrorCode.VICE_PRESIDENT_LIMIT],
          });
        }
        guild.vicePresident++;
      }

      // 会长转让
      if (executorPos === 1 && newPosition === 1) {
        if (targetPos === 2) {
          guild.vicePresident--;
        }
        this.setCharacterPosition(guild, characterId, 3); // 原会长变成普通成员
      }

      // 撤销副会长
      if (executorPos === 1 && newPosition === 3 && targetPos === 2) {
        guild.vicePresident--;
      }

      // 设置新职位
      this.setCharacterPosition(guild, targetCharacterId, newPosition);

      // 添加公会日志
      const targetCharacter = guild.allCharacter.find(p => p.characterId === targetCharacterId);
      this.addNotify(guild, 'position_change', targetCharacter?.characterName, newPosition);

      await guild.save();

      this.logger.log(`职位变更成功: ${guildId}, ${targetCharacterId} -> 职位${newPosition}`);

      return {
        success: true,
        operationTime: Date.now(),
        message: '职位变更成功',
        newPosition,
      };
    } catch (error) {
      this.logger.error('职位变更失败', error);
      throw error;
    }
  }

  /**
   * 转让会长
   * 对应old项目中的changeAssociationPosition方法（会长转让逻辑）
   */
  async transferPresidency(characterId: string, guildId: string, newPresidentId: string): Promise<GuildOperationResultDto> {
    try {
      const guild = await this.guildRepository.findByGuildId(guildId);
      if (!guild) {
        throw new NotFoundException({
          code: ErrorCode.GUILD_NOT_FOUND,
          message: ErrorMessages[ErrorCode.GUILD_NOT_FOUND],
        });
      }

      // 检查是否是会长
      const executorPos = this.getCharacterPosition(guild, characterId);
      if (executorPos !== 1) {
        throw new BadRequestException({
          code: ErrorCode.ONLY_PRESIDENT_CAN_TRANSFER,
          message: ErrorMessages[ErrorCode.ONLY_PRESIDENT_CAN_TRANSFER],
        });
      }

      const targetPos = this.getCharacterPosition(guild, newPresidentId);
      if (targetPos === 0) {
        throw new BadRequestException({
          code: ErrorCode.CHARACTER_NOT_IN_GUILD,
          message: ErrorMessages[ErrorCode.CHARACTER_NOT_IN_GUILD],
        });
      }

      // 如果新会长原来是副会长，减少副会长数量
      if (targetPos === 2) {
        guild.vicePresident--;
      }

      // 设置新会长
      this.setCharacterPosition(guild, newPresidentId, 1);
      // 原会长变成普通成员
      this.setCharacterPosition(guild, characterId, 3);

      // 更新创建者
      guild.creator = newPresidentId;

      // 添加公会日志
      const newPresident = guild.allCharacter.find(p => p.characterId === newPresidentId);
      this.addNotify(guild, 'presidency_transfer', newPresident?.characterName);

      await guild.save();

      this.logger.log(`会长转让成功: ${guildId}, 新会长: ${newPresidentId}`);

      return {
        success: true,
        operationTime: Date.now(),
        message: '会长转让成功',
        newPresident: newPresidentId,
      };
    } catch (error) {
      this.logger.error('会长转让失败', error);
      throw error;
    }
  }

  /**
   * 搜索公会
   * 对应old项目中的searchGuilds方法
   */
  async searchGuilds(keyword: string, page: number = 1, limit: number = 20): Promise<any> {
    try {
      const guilds = await this.guildRepository.searchGuilds(keyword, page, limit);

      const guildList = guilds.map(guild => ({
        guildId: guild.guildId,
        guildName: guild.guildName,
        guildLevel: guild.guildLevel,
        memberCount: guild.allCharacter.length,
        maxMembers: 10,
        creator: guild.creator,
        guildNotice: guild.guildNotice,
        isJoinable: guild.allCharacter.length < 10,
      }));

      this.logger.log(`搜索公会: ${keyword}, 找到: ${guildList.length}个`);

      return {
        guilds: guildList,
        total: guildList.length,
        page,
        limit,
      };
    } catch (error) {
      this.logger.error('搜索公会失败', error);
      throw error;
    }
  }

  /**
   * 获取公会排行榜
   * 对应old项目中的getGuildRanking方法
   */
  async getGuildRanking(page: number = 1, limit: number = 50): Promise<any> {
    try {
      const rankings = await this.guildRepository.getGuildRanking(page, limit);

      const rankingList = rankings.map((guild, index) => ({
        rank: (page - 1) * limit + index + 1,
        guildId: guild.guildId,
        guildName: guild.guildName,
        guildLevel: guild.guildLevel,
        memberCount: guild.allCharacter.length,
        totalStrength: guild.allCharacter.reduce((sum, character) => sum + (character.strength || 0), 0),
        creator: guild.creator,
      }));

      this.logger.log(`获取公会排行榜: 第${page}页, ${rankingList.length}个公会`);

      return {
        rankings: rankingList,
        page,
        limit,
        total: rankingList.length,
      };
    } catch (error) {
      this.logger.error('获取公会排行榜失败', error);
      throw error;
    }
  }

  /**
   * 权限检查
   * 对应old项目中的checkJurisdiction方法
   */
  checkJurisdiction(guild: any, characterId: string): boolean {
    const character = guild.allCharacter.find(p => p.characterId === characterId);
    if (!character) {
      return false;
    }

    // 只有会长和副会长有管理权限
    return character.pos === 1 || character.pos === 2;
  }

  /**
   * 添加公会日志
   * 对应old项目中的addNotify方法
   */
  addNotify(guild: any, type: string, characterName?: string, extra?: any): void {
    const notify = {
      type,
      characterName,
      extra,
      time: Date.now(),
      message: this.generateNotifyMessage(type, characterName, extra),
    };

    // 添加到日志列表
    if (!guild.notifyList) {
      guild.notifyList = [];
    }

    guild.notifyList.unshift(notify);

    // 保持最多50条日志
    if (guild.notifyList.length > 50) {
      guild.notifyList = guild.notifyList.slice(0, 50);
    }

    this.logger.log(`添加公会日志: ${guild.guildId}, 类型: ${type}, 消息: ${notify.message}`);
  }

  // ==================== 辅助方法 ====================

  /**
   * 获取玩家职位
   */
  private getCharacterPosition(guild: any, characterId: string): number {
    const character = guild.allCharacter.find(p => p.characterId === characterId);
    return character ? character.pos : 0;
  }

  /**
   * 设置玩家职位
   */
  private setCharacterPosition(guild: any, characterId: string, position: number): void {
    const character = guild.allCharacter.find(p => p.characterId === characterId);
    if (character) {
      character.pos = position;
    }
  }

  /**
   * 生成通知消息
   */
  private generateNotifyMessage(type: string, characterName?: string, extra?: any): string {
    switch (type) {
      case 'member_join':
        return `${characterName} 加入了公会`;
      case 'member_leave':
        return `${characterName} 离开了公会`;
      case 'member_kick':
        return `${characterName} 被踢出公会`;
      case 'position_change':
        const positionNames = { 1: '会长', 2: '副会长', 3: '成员' };
        return `${characterName} 被任命为${positionNames[extra] || '成员'}`;
      case 'presidency_transfer':
        return `${characterName} 成为新的会长`;
      case 'guild_upgrade':
        return `公会升级到 ${extra} 级`;
      default:
        return `公会活动: ${type}`;
    }
  }
}
