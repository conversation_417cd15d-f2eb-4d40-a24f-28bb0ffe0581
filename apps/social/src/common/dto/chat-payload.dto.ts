/**
 * Chat模块的Payload DTO定义
 * 
 * 为chat.controller.ts中的所有@MessagePattern接口生成对应的Payload DTO
 * 所有DTO都继承自BasePayloadDto，基于真实的接口数据结构
 * 包含详细的校验规则和@Expose()装饰器
 */

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsNumber, IsOptional, IsEnum, IsObject, Length, Min, Max } from 'class-validator';
import { Expose } from 'class-transformer';
import { BasePayloadDto } from '@libs/common/dto/base-payload.dto';
import { ChatMessageType } from '../schemas/chat.schema';

// ==================== 1. 发送聊天消息相关 ====================

/**
 * 发送聊天消息Payload DTO
 * @MessagePattern('chat.sendMessage')
 * 基于真实接口结构：{ characterId: string; serverId: string; messageData: SendChatMessageDto; injectedContext?: InjectedContext }
 */
export class SendMessagePayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  characterId: string;

  @ApiProperty({ description: '区服ID', example: 'server_001' })
  @Expose()
  @IsString({ message: '区服ID必须是字符串' })
  serverId: string;

  @ApiProperty({ description: '消息数据', type: 'object' })
  @Expose()
  @IsObject({ message: '消息数据必须是对象' })
  messageData: {
    senderId: string;
    senderName: string;
    channelId: string;
    messageType: ChatMessageType;
    content: string;
    receiverId?: string;
    receiverName?: string;
    extraData?: any;
  };
}

// ==================== 2. 获取聊天历史相关 ====================

/**
 * 获取聊天历史Payload DTO
 * @MessagePattern('chat.getHistory')
 * 基于真实接口结构：{ query: GetChatHistoryDto; injectedContext?: InjectedContext }
 */
export class GetHistoryPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '查询参数', type: 'object' })
  @Expose()
  @IsObject({ message: '查询参数必须是对象' })
  query: {
    channelId: string;
    limit?: number;
    before?: number;
  };
}

// ==================== 3. 获取私聊历史相关 ====================

/**
 * 获取私聊历史Payload DTO
 * @MessagePattern('chat.getPrivateHistory')
 * 基于真实接口结构：{ query: GetPrivateChatDto; injectedContext?: InjectedContext }
 */
export class GetPrivateHistoryPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '查询参数', type: 'object' })
  @Expose()
  @IsObject({ message: '查询参数必须是对象' })
  query: {
    senderId: string;
    receiverId: string;
    limit?: number;
    before?: number;
  };
}

// ==================== 4. 频道管理相关 ====================

/**
 * 加入频道Payload DTO
 * @MessagePattern('chat.joinChannel')
 * 基于真实接口结构：{ characterId: string; channelId: string; serverId: string; injectedContext?: InjectedContext }
 */
export class JoinChannelPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  characterId: string;

  @ApiProperty({ description: '频道ID', example: 'world_chat' })
  @Expose()
  @IsString({ message: '频道ID必须是字符串' })
  channelId: string;

  @ApiProperty({ description: '区服ID', example: 'server_001' })
  @Expose()
  @IsString({ message: '区服ID必须是字符串' })
  serverId: string;
}

/**
 * 离开频道Payload DTO
 * @MessagePattern('chat.leaveChannel')
 * 基于真实接口结构：{ characterId: string; channelId: string; injectedContext?: InjectedContext }
 */
export class LeaveChannelPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  characterId: string;

  @ApiProperty({ description: '频道ID', example: 'world_chat' })
  @Expose()
  @IsString({ message: '频道ID必须是字符串' })
  channelId: string;
}
