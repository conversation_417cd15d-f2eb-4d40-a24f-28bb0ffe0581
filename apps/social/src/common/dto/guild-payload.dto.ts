/**
 * Guild模块的Payload DTO定义
 * 
 * 为guild.controller.ts中的所有@MessagePattern接口生成对应的Payload DTO
 * 所有DTO都继承自BasePayloadDto，基于真实的接口数据结构
 * 包含详细的校验规则和@Expose()装饰器
 */

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsNumber, IsOptional, Length, Min, Max } from 'class-validator';
import { Expose } from 'class-transformer';
import { BasePayloadDto } from '@libs/common/dto/base-payload.dto';

// ==================== 1. 公会基础操作相关 ====================

/**
 * 创建公会Payload DTO
 * @MessagePattern('guild.create')
 * 基于真实接口结构：{ characterId: string; characterName: string; guildName: string; faceId: number; strength: number; gid: string; faceUrl: string; frontendId?: string; sessionId?: string; injectedContext?: InjectedContext }
 */
export class CreateGuildPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '角色名称', example: 'PlayerName' })
  @Expose()
  @IsString({ message: '角色名称必须是字符串' })
  @Length(1, 50, { message: '角色名称长度必须在1-50个字符之间' })
  characterName: string;

  @ApiProperty({ description: '公会名称', example: 'MyGuild' })
  @Expose()
  @IsString({ message: '公会名称必须是字符串' })
  @Length(1, 50, { message: '公会名称长度必须在1-50个字符之间' })
  guildName: string;

  @ApiProperty({ description: '头像ID', example: 1001 })
  @Expose()
  @IsNumber({}, { message: '头像ID必须是数字' })
  @Min(1, { message: '头像ID不能小于1' })
  @Max(999999, { message: '头像ID不能大于999999' })
  faceId: number;

  @ApiProperty({ description: '实力值', example: 50000 })
  @Expose()
  @IsNumber({}, { message: '实力值必须是数字' })
  @Min(0, { message: '实力值不能小于0' })
  strength: number;

  @ApiProperty({ description: 'GID', example: 'gid_12345' })
  @Expose()
  @IsString({ message: 'GID必须是字符串' })
  @Length(1, 50, { message: 'GID长度必须在1-50个字符之间' })
  gid: string;

  @ApiProperty({ description: '头像URL', example: 'https://example.com/avatar.jpg' })
  @Expose()
  @IsString({ message: '头像URL必须是字符串' })
  faceUrl: string;

  @ApiPropertyOptional({ description: '前端ID', example: 'frontend_001' })
  @Expose()
  @IsOptional()
  @IsString({ message: '前端ID必须是字符串' })
  frontendId?: string;

  @ApiPropertyOptional({ description: '会话ID', example: 'session_001' })
  @Expose()
  @IsOptional()
  @IsString({ message: '会话ID必须是字符串' })
  sessionId?: string;
}

/**
 * 申请加入公会Payload DTO
 * @MessagePattern('guild.apply')
 * 基于真实接口结构：{ guildId: string; characterId: string; characterName: string; gid: string; faceUrl: string; strength: number; frontendId?: string; sessionId?: string; injectedContext?: InjectedContext }
 */
export class ApplyJoinGuildPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '公会ID', example: 'guild_12345' })
  @Expose()
  @IsString({ message: '公会ID必须是字符串' })
  @Length(1, 50, { message: '公会ID长度必须在1-50个字符之间' })
  guildId: string;

  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '角色名称', example: 'PlayerName' })
  @Expose()
  @IsString({ message: '角色名称必须是字符串' })
  @Length(1, 50, { message: '角色名称长度必须在1-50个字符之间' })
  characterName: string;

  @ApiProperty({ description: 'GID', example: 'gid_12345' })
  @Expose()
  @IsString({ message: 'GID必须是字符串' })
  @Length(1, 50, { message: 'GID长度必须在1-50个字符之间' })
  gid: string;

  @ApiProperty({ description: '头像URL', example: 'https://example.com/avatar.jpg' })
  @Expose()
  @IsString({ message: '头像URL必须是字符串' })
  faceUrl: string;

  @ApiProperty({ description: '实力值', example: 50000 })
  @Expose()
  @IsNumber({}, { message: '实力值必须是数字' })
  @Min(0, { message: '实力值不能小于0' })
  strength: number;

  @ApiPropertyOptional({ description: '前端ID', example: 'frontend_001' })
  @Expose()
  @IsOptional()
  @IsString({ message: '前端ID必须是字符串' })
  frontendId?: string;

  @ApiPropertyOptional({ description: '会话ID', example: 'session_001' })
  @Expose()
  @IsOptional()
  @IsString({ message: '会话ID必须是字符串' })
  sessionId?: string;
}

/**
 * 离开公会Payload DTO
 * @MessagePattern('guild.leave')
 * 基于真实接口结构：{ characterId: string; guildId: string; injectedContext?: InjectedContext }
 */
export class LeaveGuildPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '公会ID', example: 'guild_12345' })
  @Expose()
  @IsString({ message: '公会ID必须是字符串' })
  @Length(1, 50, { message: '公会ID长度必须在1-50个字符之间' })
  guildId: string;
}

// ==================== 2. 公会信息查询相关 ====================

/**
 * 获取公会信息Payload DTO
 * @MessagePattern('guild.getInfo')
 * 基于真实接口结构：{ guildId: string; injectedContext?: InjectedContext }
 */
export class GetGuildInfoPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '公会ID', example: 'guild_12345' })
  @Expose()
  @IsString({ message: '公会ID必须是字符串' })
  @Length(1, 50, { message: '公会ID长度必须在1-50个字符之间' })
  guildId: string;
}

/**
 * 获取申请列表Payload DTO
 * @MessagePattern('guild.getApplicationList')
 * 基于真实接口结构：{ characterId: string; guildId: string; injectedContext?: InjectedContext }
 */
export class GetApplicationListPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '公会ID', example: 'guild_12345' })
  @Expose()
  @IsString({ message: '公会ID必须是字符串' })
  @Length(1, 50, { message: '公会ID长度必须在1-50个字符之间' })
  guildId: string;
}
