/**
 * Guild模块的Payload DTO定义
 * 
 * 为guild.controller.ts中的所有@MessagePattern接口生成对应的Payload DTO
 * 所有DTO都继承自BasePayloadDto，基于真实的接口数据结构
 * 包含详细的校验规则和@Expose()装饰器
 */

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsNumber, IsOptional, Length, Min, Max } from 'class-validator';
import { Expose } from 'class-transformer';
import { BasePayloadDto } from '@libs/common/dto/base-payload.dto';

// ==================== 1. 公会基础操作相关 ====================

/**
 * 创建公会Payload DTO
 * @MessagePattern('guild.create')
 * 基于真实接口结构：{ characterId: string; characterName: string; guildName: string; faceId: number; strength: number; gid: string; faceUrl: string; frontendId?: string; sessionId?: string; injectedContext?: InjectedContext }
 */
export class CreateGuildPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '角色名称', example: 'PlayerName' })
  @Expose()
  @IsString({ message: '角色名称必须是字符串' })
  @Length(1, 50, { message: '角色名称长度必须在1-50个字符之间' })
  characterName: string;

  @ApiProperty({ description: '公会名称', example: 'MyGuild' })
  @Expose()
  @IsString({ message: '公会名称必须是字符串' })
  @Length(1, 50, { message: '公会名称长度必须在1-50个字符之间' })
  guildName: string;

  @ApiProperty({ description: '头像ID', example: 1001 })
  @Expose()
  @IsNumber({}, { message: '头像ID必须是数字' })
  @Min(1, { message: '头像ID不能小于1' })
  @Max(999999, { message: '头像ID不能大于999999' })
  faceId: number;

  @ApiProperty({ description: '实力值', example: 50000 })
  @Expose()
  @IsNumber({}, { message: '实力值必须是数字' })
  @Min(0, { message: '实力值不能小于0' })
  strength: number;

  @ApiProperty({ description: 'GID', example: 'gid_12345' })
  @Expose()
  @IsString({ message: 'GID必须是字符串' })
  @Length(1, 50, { message: 'GID长度必须在1-50个字符之间' })
  gid: string;

  @ApiProperty({ description: '头像URL', example: 'https://example.com/avatar.jpg' })
  @Expose()
  @IsString({ message: '头像URL必须是字符串' })
  faceUrl: string;

  @ApiPropertyOptional({ description: '前端ID', example: 'frontend_001' })
  @Expose()
  @IsOptional()
  @IsString({ message: '前端ID必须是字符串' })
  frontendId?: string;

  @ApiPropertyOptional({ description: '会话ID', example: 'session_001' })
  @Expose()
  @IsOptional()
  @IsString({ message: '会话ID必须是字符串' })
  sessionId?: string;
}

/**
 * 申请加入公会Payload DTO
 * @MessagePattern('guild.apply')
 * 基于真实接口结构：{ guildId: string; characterId: string; characterName: string; gid: string; faceUrl: string; strength: number; frontendId?: string; sessionId?: string; injectedContext?: InjectedContext }
 */
export class ApplyJoinGuildPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '公会ID', example: 'guild_12345' })
  @Expose()
  @IsString({ message: '公会ID必须是字符串' })
  @Length(1, 50, { message: '公会ID长度必须在1-50个字符之间' })
  guildId: string;

  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '角色名称', example: 'PlayerName' })
  @Expose()
  @IsString({ message: '角色名称必须是字符串' })
  @Length(1, 50, { message: '角色名称长度必须在1-50个字符之间' })
  characterName: string;

  @ApiProperty({ description: 'GID', example: 'gid_12345' })
  @Expose()
  @IsString({ message: 'GID必须是字符串' })
  @Length(1, 50, { message: 'GID长度必须在1-50个字符之间' })
  gid: string;

  @ApiProperty({ description: '头像URL', example: 'https://example.com/avatar.jpg' })
  @Expose()
  @IsString({ message: '头像URL必须是字符串' })
  faceUrl: string;

  @ApiProperty({ description: '实力值', example: 50000 })
  @Expose()
  @IsNumber({}, { message: '实力值必须是数字' })
  @Min(0, { message: '实力值不能小于0' })
  strength: number;

  @ApiPropertyOptional({ description: '前端ID', example: 'frontend_001' })
  @Expose()
  @IsOptional()
  @IsString({ message: '前端ID必须是字符串' })
  frontendId?: string;

  @ApiPropertyOptional({ description: '会话ID', example: 'session_001' })
  @Expose()
  @IsOptional()
  @IsString({ message: '会话ID必须是字符串' })
  sessionId?: string;
}

/**
 * 离开公会Payload DTO
 * @MessagePattern('guild.leave')
 * 基于真实接口结构：{ characterId: string; guildId: string; injectedContext?: InjectedContext }
 */
export class LeaveGuildPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '公会ID', example: 'guild_12345' })
  @Expose()
  @IsString({ message: '公会ID必须是字符串' })
  @Length(1, 50, { message: '公会ID长度必须在1-50个字符之间' })
  guildId: string;
}

// ==================== 2. 公会信息查询相关 ====================

/**
 * 获取公会信息Payload DTO
 * @MessagePattern('guild.getInfo')
 * 基于真实接口结构：{ guildId: string; injectedContext?: InjectedContext }
 */
export class GetGuildInfoPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '公会ID', example: 'guild_12345' })
  @Expose()
  @IsString({ message: '公会ID必须是字符串' })
  @Length(1, 50, { message: '公会ID长度必须在1-50个字符之间' })
  guildId: string;
}

/**
 * 获取申请列表Payload DTO
 * @MessagePattern('guild.getApplicationList')
 * 基于真实接口结构：{ characterId: string; guildId: string; injectedContext?: InjectedContext }
 */
export class GetApplicationListPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '公会ID', example: 'guild_12345' })
  @Expose()
  @IsString({ message: '公会ID必须是字符串' })
  @Length(1, 50, { message: '公会ID长度必须在1-50个字符之间' })
  guildId: string;
}

// ==================== 3. 申请处理相关 ====================

/**
 * 同意申请Payload DTO
 * @MessagePattern('guild.approveApplication')
 * 基于真实接口结构：{ characterId: string; guildId: string; agreeId: string; injectedContext?: InjectedContext }
 */
export class ApproveApplicationPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '公会ID', example: 'guild_12345' })
  @Expose()
  @IsString({ message: '公会ID必须是字符串' })
  @Length(1, 50, { message: '公会ID长度必须在1-50个字符之间' })
  guildId: string;

  @ApiProperty({ description: '同意的申请者ID', example: 'char_67890' })
  @Expose()
  @IsString({ message: '申请者ID必须是字符串' })
  @Length(1, 50, { message: '申请者ID长度必须在1-50个字符之间' })
  agreeId: string;
}

/**
 * 拒绝申请Payload DTO
 * @MessagePattern('guild.rejectApplication')
 * 基于真实接口结构：{ characterId: string; guildId: string; rejectId: string; injectedContext?: InjectedContext }
 */
export class RejectApplicationPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '公会ID', example: 'guild_12345' })
  @Expose()
  @IsString({ message: '公会ID必须是字符串' })
  @Length(1, 50, { message: '公会ID长度必须在1-50个字符之间' })
  guildId: string;

  @ApiProperty({ description: '拒绝的申请者ID', example: 'char_67890' })
  @Expose()
  @IsString({ message: '申请者ID必须是字符串' })
  @Length(1, 50, { message: '申请者ID长度必须在1-50个字符之间' })
  rejectId: string;
}

// ==================== 4. 搜索相关 ====================

/**
 * 搜索公会Payload DTO
 * @MessagePattern('guild.search')
 * 基于真实接口结构：{ keyword: string; page?: number; limit?: number; injectedContext?: InjectedContext }
 */
export class SearchGuildsPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '搜索关键词', example: 'MyGuild' })
  @Expose()
  @IsString({ message: '搜索关键词必须是字符串' })
  @Length(1, 50, { message: '搜索关键词长度必须在1-50个字符之间' })
  keyword: string;

  @ApiPropertyOptional({ description: '页码', example: 1, minimum: 1 })
  @Expose()
  @IsOptional()
  @IsNumber({}, { message: '页码必须是数字' })
  @Min(1, { message: '页码不能小于1' })
  page?: number;

  @ApiPropertyOptional({ description: '每页数量', example: 20, minimum: 1, maximum: 100 })
  @Expose()
  @IsOptional()
  @IsNumber({}, { message: '每页数量必须是数字' })
  @Min(1, { message: '每页数量不能小于1' })
  @Max(100, { message: '每页数量不能大于100' })
  limit?: number;
}

// ==================== 5. 职位管理相关 ====================

/**
 * 职位变更Payload DTO
 * @MessagePattern('guild.changePosition')
 * 基于真实接口结构：{ characterId: string; guildId: string; targetCharacterId: string; newPosition: number; injectedContext?: InjectedContext }
 */
export class ChangePositionPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '公会ID', example: 'guild_12345' })
  @Expose()
  @IsString({ message: '公会ID必须是字符串' })
  @Length(1, 50, { message: '公会ID长度必须在1-50个字符之间' })
  guildId: string;

  @ApiProperty({ description: '目标角色ID', example: 'char_67890' })
  @Expose()
  @IsString({ message: '目标角色ID必须是字符串' })
  @Length(1, 50, { message: '目标角色ID长度必须在1-50个字符之间' })
  targetCharacterId: string;

  @ApiProperty({ description: '新职位', example: 2, enum: [1, 2, 3, 4] })
  @Expose()
  @IsNumber({}, { message: '新职位必须是数字' })
  @Min(1, { message: '新职位不能小于1' })
  @Max(4, { message: '新职位不能大于4' })
  newPosition: number;
}

/**
 * 转让会长Payload DTO
 * @MessagePattern('guild.transferPresidency')
 * 基于真实接口结构：{ characterId: string; guildId: string; newPresidentId: string; injectedContext?: InjectedContext }
 */
export class TransferPresidencyPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '公会ID', example: 'guild_12345' })
  @Expose()
  @IsString({ message: '公会ID必须是字符串' })
  @Length(1, 50, { message: '公会ID长度必须在1-50个字符之间' })
  guildId: string;

  @ApiProperty({ description: '新会长ID', example: 'char_67890' })
  @Expose()
  @IsString({ message: '新会长ID必须是字符串' })
  @Length(1, 50, { message: '新会长ID长度必须在1-50个字符之间' })
  newPresidentId: string;
}

// ==================== 6. 排行榜相关 ====================

/**
 * 获取公会排行榜Payload DTO
 * @MessagePattern('guild.getRanking')
 * 基于真实接口结构：{ page?: number; limit?: number; injectedContext?: InjectedContext }
 */
export class GetGuildRankingPayloadDto extends BasePayloadDto {
  @ApiPropertyOptional({ description: '页码', example: 1, minimum: 1 })
  @Expose()
  @IsOptional()
  @IsNumber({}, { message: '页码必须是数字' })
  @Min(1, { message: '页码不能小于1' })
  page?: number;

  @ApiPropertyOptional({ description: '每页数量', example: 50, minimum: 1, maximum: 100 })
  @Expose()
  @IsOptional()
  @IsNumber({}, { message: '每页数量必须是数字' })
  @Min(1, { message: '每页数量不能小于1' })
  @Max(100, { message: '每页数量不能大于100' })
  limit?: number;
}
