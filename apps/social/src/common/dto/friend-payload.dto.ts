/**
 * Friend模块的Payload DTO定义
 * 
 * 为friend.controller.ts中的所有@MessagePattern接口生成对应的Payload DTO
 * 所有DTO都继承自BasePayloadDto，基于真实的接口数据结构
 * 包含详细的校验规则和@Expose()装饰器
 */

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsNumber, IsOptional, IsBoolean, IsObject, ValidateNested, Length, Min, Max } from 'class-validator';
import { Expose, Type } from 'class-transformer';
import { BasePayloadDto } from '@libs/common/dto/base-payload.dto';
import { AddFriendDto, HandleFriendApplyDto, RemoveFriendDto, UpdateLocationDto, BlockCharacterDto, SearchFriendDto, NearbyCharactersDto } from './friend.dto';

// ==================== 1. 好友基础操作相关 ====================

/**
 * 添加好友Payload DTO
 * @MessagePattern('friend.add')
 * 基于真实接口结构：{ characterId: string; addFriendDto: AddFriendDto; injectedContext?: InjectedContext }
 */
export class AddFriendPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  characterId: string;

  @ApiProperty({ description: '添加好友数据', type: AddFriendDto })
  @Expose()
  @IsObject({ message: '添加好友数据必须是对象' })
  @ValidateNested({ message: '添加好友数据格式不正确' })
  @Type(() => AddFriendDto)
  addFriendDto: AddFriendDto;
}

/**
 * 处理好友申请Payload DTO
 * @MessagePattern('friend.handleApply')
 * 基于真实接口结构：{ characterId: string; serverId: string; applyData: HandleFriendApplyDto; injectedContext?: InjectedContext }
 */
export class HandleApplyPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  characterId: string;

  @ApiProperty({ description: '区服ID', example: 'server_001' })
  @Expose()
  @IsString({ message: '区服ID必须是字符串' })
  serverId: string;

  @ApiProperty({ description: '申请处理数据', type: HandleFriendApplyDto })
  @Expose()
  @IsObject({ message: '申请处理数据必须是对象' })
  @ValidateNested({ message: '申请处理数据格式不正确' })
  @Type(() => HandleFriendApplyDto)
  applyData: HandleFriendApplyDto;
}

/**
 * 删除好友Payload DTO
 * @MessagePattern('friend.remove')
 * 基于真实接口结构：{ characterId: string; serverId: string; removeData: RemoveFriendDto; injectedContext?: InjectedContext }
 */
export class RemoveFriendPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  characterId: string;

  @ApiProperty({ description: '区服ID', example: 'server_001' })
  @Expose()
  @IsString({ message: '区服ID必须是字符串' })
  serverId: string;

  @ApiProperty({ description: '删除好友数据', type: RemoveFriendDto })
  @Expose()
  @IsObject({ message: '删除好友数据必须是对象' })
  @ValidateNested({ message: '删除好友数据格式不正确' })
  @Type(() => RemoveFriendDto)
  removeData: RemoveFriendDto;
}

// ==================== 2. 好友列表查询相关 ====================

/**
 * 获取好友列表Payload DTO
 * @MessagePattern('friend.getList')
 * 基于真实接口结构：{ characterId: string; injectedContext?: InjectedContext }
 */
export class GetFriendListPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  characterId: string;
}

/**
 * 获取好友申请列表Payload DTO
 * @MessagePattern('friend.applies')
 * 基于真实接口结构：{ characterId: string; serverId: string; injectedContext?: InjectedContext }
 */
export class GetAppliesPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  characterId: string;

  @ApiProperty({ description: '区服ID', example: 'server_001' })
  @Expose()
  @IsString({ message: '区服ID必须是字符串' })
  serverId: string;
}

// ==================== 3. 位置和搜索相关 ====================

/**
 * 更新位置Payload DTO
 * @MessagePattern('friend.updateLocation')
 * 基于真实接口结构：{ characterId: string; serverId: string; locationData: UpdateLocationDto; injectedContext?: InjectedContext }
 */
export class UpdateLocationPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  characterId: string;

  @ApiProperty({ description: '区服ID', example: 'server_001' })
  @Expose()
  @IsString({ message: '区服ID必须是字符串' })
  serverId: string;

  @ApiProperty({ description: '位置数据', type: UpdateLocationDto })
  @Expose()
  @IsObject({ message: '位置数据必须是对象' })
  @ValidateNested({ message: '位置数据格式不正确' })
  @Type(() => UpdateLocationDto)
  locationData: UpdateLocationDto;
}

/**
 * 搜索好友Payload DTO
 * @MessagePattern('friend.search')
 * 基于真实接口结构：{ characterId: string; searchDto: SearchFriendDto; injectedContext?: InjectedContext }
 */
export class SearchFriendPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  characterId: string;

  @ApiProperty({ description: '搜索条件', type: SearchFriendDto })
  @Expose()
  @IsObject({ message: '搜索条件必须是对象' })
  @ValidateNested({ message: '搜索条件格式不正确' })
  @Type(() => SearchFriendDto)
  searchDto: SearchFriendDto;
}

/**
 * 获取附近玩家Payload DTO
 * @MessagePattern('friend.nearby')
 * 基于真实接口结构：{ characterId: string; serverId: string; nearbyData: NearbyCharactersDto; injectedContext?: InjectedContext }
 */
export class GetNearbyPlayersPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  characterId: string;

  @ApiProperty({ description: '区服ID', example: 'server_001' })
  @Expose()
  @IsString({ message: '区服ID必须是字符串' })
  serverId: string;

  @ApiProperty({ description: '附近玩家查询条件', type: NearbyCharactersDto })
  @Expose()
  @IsObject({ message: '附近玩家查询条件必须是对象' })
  @ValidateNested({ message: '附近玩家查询条件格式不正确' })
  @Type(() => NearbyCharactersDto)
  nearbyData: NearbyCharactersDto;
}

// ==================== 4. 屏蔽和管理相关 ====================

/**
 * 屏蔽玩家Payload DTO
 * @MessagePattern('friend.block')
 * 基于真实接口结构：{ characterId: string; serverId: string; blockData: BlockCharacterDto; injectedContext?: InjectedContext }
 */
export class BlockCharacterPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  characterId: string;

  @ApiProperty({ description: '区服ID', example: 'server_001' })
  @Expose()
  @IsString({ message: '区服ID必须是字符串' })
  serverId: string;

  @ApiProperty({ description: '屏蔽数据', type: BlockCharacterDto })
  @Expose()
  @IsObject({ message: '屏蔽数据必须是对象' })
  @ValidateNested({ message: '屏蔽数据格式不正确' })
  @Type(() => BlockCharacterDto)
  blockData: BlockCharacterDto;
}

/**
 * 取消屏蔽玩家Payload DTO
 * @MessagePattern('friend.unblock')
 * 基于真实接口结构：{ characterId: string; serverId: string; unblockData: { targetCharacterId: string }; injectedContext?: InjectedContext }
 */
export class UnblockCharacterPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  characterId: string;

  @ApiProperty({ description: '区服ID', example: 'server_001' })
  @Expose()
  @IsString({ message: '区服ID必须是字符串' })
  serverId: string;

  @ApiProperty({ description: '取消屏蔽数据', type: 'object' })
  @Expose()
  @IsObject({ message: '取消屏蔽数据必须是对象' })
  unblockData: {
    targetCharacterId: string;
  };
}

/**
 * 获取屏蔽列表Payload DTO
 * @MessagePattern('friend.blockedList')
 * 基于真实接口结构：{ characterId: string; serverId: string; injectedContext?: InjectedContext }
 */
export class GetBlockedListPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  characterId: string;

  @ApiProperty({ description: '区服ID', example: 'server_001' })
  @Expose()
  @IsString({ message: '区服ID必须是字符串' })
  serverId: string;
}

// ==================== 5. 统计信息相关 ====================

/**
 * 获取好友统计Payload DTO
 * @MessagePattern('friend.stats')
 * 基于真实接口结构：{ characterId: string; serverId: string; injectedContext?: InjectedContext }
 */
export class GetFriendStatsPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  characterId: string;

  @ApiProperty({ description: '区服ID', example: 'server_001' })
  @Expose()
  @IsString({ message: '区服ID必须是字符串' })
  serverId: string;
}

/**
 * 获取好友推荐Payload DTO
 * @MessagePattern('friend.recommendations')
 * 基于真实接口结构：{ characterId: string; serverId: string; limit?: number; injectedContext?: InjectedContext }
 */
export class GetRecommendationsPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  characterId: string;

  @ApiProperty({ description: '区服ID', example: 'server_001' })
  @Expose()
  @IsString({ message: '区服ID必须是字符串' })
  serverId: string;

  @ApiPropertyOptional({ description: '推荐数量限制', example: 10, minimum: 1, maximum: 50 })
  @Expose()
  @IsOptional()
  @IsNumber({}, { message: '推荐数量限制必须是数字' })
  @Min(1, { message: '推荐数量限制不能小于1' })
  @Max(50, { message: '推荐数量限制不能大于50' })
  limit?: number;
}

// ==================== 6. 其他接口相关 ====================

/**
 * 获取好友推荐Payload DTO
 * @MessagePattern('friend.recommend')
 * 基于真实接口结构：{ characterId: string; injectedContext?: InjectedContext }
 */
export class GetFriendRecommendPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  characterId: string;
}

/**
 * 获取好友距离Payload DTO
 * @MessagePattern('friend.distances')
 * 基于真实接口结构：{ characterId: string; injectedContext?: InjectedContext }
 */
export class GetFriendDistancesPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  characterId: string;
}

/**
 * 获取黑名单Payload DTO
 * @MessagePattern('friend.blacklist')
 * 基于真实接口结构：{ characterId: string; injectedContext?: InjectedContext }
 */
export class GetBlacklistPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  characterId: string;
}

/**
 * 更新在线状态Payload DTO
 * @MessagePattern('friend.updateOnlineStatus')
 * 基于真实接口结构：{ characterId: string; isOnline: boolean; injectedContext?: InjectedContext }
 */
export class UpdateOnlineStatusPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  characterId: string;

  @ApiProperty({ description: '是否在线', example: true })
  @Expose()
  @IsBoolean({ message: '是否在线必须是布尔值' })
  isOnline: boolean;
}

/**
 * 清理过期申请Payload DTO
 * @MessagePattern('friend.cleanExpiredApplies')
 * 基于真实接口结构：{ expireDays?: number; injectedContext?: InjectedContext }
 */
export class CleanExpiredAppliesPayloadDto extends BasePayloadDto {
  @ApiPropertyOptional({ description: '过期天数', example: 7, minimum: 1, maximum: 365 })
  @Expose()
  @IsOptional()
  @IsNumber({}, { message: '过期天数必须是数字' })
  @Min(1, { message: '过期天数不能小于1' })
  @Max(365, { message: '过期天数不能大于365' })
  expireDays?: number;
}
