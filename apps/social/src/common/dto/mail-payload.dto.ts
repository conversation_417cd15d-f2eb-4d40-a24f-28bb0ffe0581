/**
 * Mail模块的Payload DTO定义
 * 
 * 为mail.controller.ts中的所有@MessagePattern接口生成对应的Payload DTO
 * 所有DTO都继承自BasePayloadDto，基于真实的接口数据结构
 * 包含详细的校验规则和@Expose()装饰器
 */

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsNumber, IsOptional, IsArray, ValidateNested, Length, Min, Max } from 'class-validator';
import { Expose, Type } from 'class-transformer';
import { BasePayloadDto } from '@libs/common/dto/base-payload.dto';

// ==================== 1. 发送邮件相关 ====================

/**
 * 发送邮件Payload DTO
 * @MessagePattern('mail.send')
 * 基于真实接口结构：{ receiverUid: string; senderUid: string; mailId: number; mailType: number; attachList: any[]; specialAttachInfo?: any; param1?: any; param2?: any; param3?: any; param4?: any; injectedContext?: InjectedContext }
 */
export class SendMailPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '接收者UID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '接收者UID必须是字符串' })
  @Length(1, 50, { message: '接收者UID长度必须在1-50个字符之间' })
  receiverUid: string;

  @ApiProperty({ description: '发送者UID', example: 'char_67890' })
  @Expose()
  @IsString({ message: '发送者UID必须是字符串' })
  @Length(1, 50, { message: '发送者UID长度必须在1-50个字符之间' })
  senderUid: string;

  @ApiProperty({ description: '邮件模板ID', example: 1001 })
  @Expose()
  @IsNumber({}, { message: '邮件模板ID必须是数字' })
  @Min(1, { message: '邮件模板ID不能小于1' })
  @Max(999999, { message: '邮件模板ID不能大于999999' })
  mailId: number;

  @ApiProperty({ description: '邮件类型', example: 1 })
  @Expose()
  @IsNumber({}, { message: '邮件类型必须是数字' })
  @Min(1, { message: '邮件类型不能小于1' })
  @Max(10, { message: '邮件类型不能大于10' })
  mailType: number;

  @ApiProperty({ description: '附件列表', type: [Object], example: [{ itemType: 1, resId: 1001, num: 10 }] })
  @Expose()
  @IsArray({ message: '附件列表必须是数组' })
  attachList: any[];

  @ApiPropertyOptional({ description: '特殊附件信息', example: {} })
  @Expose()
  @IsOptional()
  specialAttachInfo?: any;

  @ApiPropertyOptional({ description: '参数1', example: 'param1' })
  @Expose()
  @IsOptional()
  param1?: any;

  @ApiPropertyOptional({ description: '参数2', example: 'param2' })
  @Expose()
  @IsOptional()
  param2?: any;

  @ApiPropertyOptional({ description: '参数3', example: 'param3' })
  @Expose()
  @IsOptional()
  param3?: any;

  @ApiPropertyOptional({ description: '参数4', example: 'param4' })
  @Expose()
  @IsOptional()
  param4?: any;
}

// ==================== 2. 邮件列表查询相关 ====================

/**
 * 获取邮件列表Payload DTO
 * @MessagePattern('mail.getList')
 * 基于真实接口结构：{ characterId: string; page?: number; limit?: number; injectedContext?: InjectedContext }
 */
export class GetMailListPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiPropertyOptional({ description: '页码', example: 1, minimum: 1 })
  @Expose()
  @IsOptional()
  @IsNumber({}, { message: '页码必须是数字' })
  @Min(1, { message: '页码不能小于1' })
  page?: number;

  @ApiPropertyOptional({ description: '每页数量', example: 20, minimum: 1, maximum: 100 })
  @Expose()
  @IsOptional()
  @IsNumber({}, { message: '每页数量必须是数字' })
  @Min(1, { message: '每页数量不能小于1' })
  @Max(100, { message: '每页数量不能大于100' })
  limit?: number;
}

// ==================== 3. 邮件操作相关 ====================

/**
 * 读取邮件Payload DTO
 * @MessagePattern('mail.read')
 * 基于真实接口结构：{ characterId: string; mailUid: string; injectedContext?: InjectedContext }
 */
export class ReadMailPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '邮件UID', example: 'mail_67890' })
  @Expose()
  @IsString({ message: '邮件UID必须是字符串' })
  @Length(1, 50, { message: '邮件UID长度必须在1-50个字符之间' })
  mailUid: string;
}

/**
 * 删除邮件Payload DTO
 * @MessagePattern('mail.delete')
 * 基于真实接口结构：{ characterId: string; mailUid: string; injectedContext?: InjectedContext }
 */
export class DeleteMailPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '邮件UID', example: 'mail_67890' })
  @Expose()
  @IsString({ message: '邮件UID必须是字符串' })
  @Length(1, 50, { message: '邮件UID长度必须在1-50个字符之间' })
  mailUid: string;
}

/**
 * 领取邮件附件Payload DTO
 * @MessagePattern('mail.claimAttachment')
 * 基于真实接口结构：{ characterId: string; mailUid: string; injectedContext?: InjectedContext }
 */
export class ClaimAttachmentPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '邮件UID', example: 'mail_67890' })
  @Expose()
  @IsString({ message: '邮件UID必须是字符串' })
  @Length(1, 50, { message: '邮件UID长度必须在1-50个字符之间' })
  mailUid: string;
}

// ==================== 4. 批量操作相关 ====================

/**
 * 批量删除邮件Payload DTO
 * @MessagePattern('mail.batchDelete')
 * 基于真实接口结构：{ characterId: string; mailUids: string[]; injectedContext?: InjectedContext }
 */
export class BatchDeleteMailsPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '邮件UID列表', type: [String], example: ['mail_001', 'mail_002'] })
  @Expose()
  @IsArray({ message: '邮件UID列表必须是数组' })
  @IsString({ each: true, message: '邮件UID必须是字符串' })
  @Length(1, 50, { each: true, message: '邮件UID长度必须在1-50个字符之间' })
  mailUids: string[];
}

/**
 * 批量领取附件Payload DTO
 * @MessagePattern('mail.batchClaim')
 * 基于真实接口结构：{ characterId: string; mailUids: string[]; injectedContext?: InjectedContext }
 */
export class BatchClaimAttachmentsPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '邮件UID列表', type: [String], example: ['mail_001', 'mail_002'] })
  @Expose()
  @IsArray({ message: '邮件UID列表必须是数组' })
  @IsString({ each: true, message: '邮件UID必须是字符串' })
  @Length(1, 50, { each: true, message: '邮件UID长度必须在1-50个字符之间' })
  mailUids: string[];
}
