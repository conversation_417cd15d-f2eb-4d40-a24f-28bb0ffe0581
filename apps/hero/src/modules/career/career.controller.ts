import { Controller, Logger } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { CacheEvict, Cacheable } from '@libs/redis';
import { CareerService } from './career.service';


import { InjectedContext } from '@libs/common/types';
import {
  AddContractDaysPayloadDto,
  AddToRetirementPayloadDto, GetExpiringContractsPayloadDto, GetStatsPayloadDto,
  ProcessRetirementPayloadDto,
  PromoteStatusPayloadDto,
  RenewContractPayloadDto
} from "@hero/common/dto/career-payload.dto";

/**
 * 球员生涯管理控制器
 * 基于old项目的生涯相关功能
 */
@Controller()
export class CareerController {
  private readonly logger = new Logger(CareerController.name);

  constructor(private readonly careerService: CareerService) {}

  /**
   * 增加球员合约天数
   * 对应old项目: addHeroLeftDay
   */
  @MessagePattern('career.addContractDays')
  @CacheEvict({
    key: 'hero:info:#{payload.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async addHeroLeftDay(@Payload() payload: AddContractDaysPayloadDto) {
    this.logger.log(`增加球员合约天数: ${payload.heroId}, 天数: ${payload.days}`);
    const result = await this.careerService.addHeroLeftDay(payload.heroId, payload.days);
    return {
      code: 0,
      message: '合约天数增加成功',
      data: result,
    };
  }

  /**
   * 续约球员
   * 对应old项目: renewTheContract（支持批量续约）
   */
  @MessagePattern('career.renewContract')
  @CacheEvict({
    key: 'character:heroes:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async renewTheContract(@Payload() payload: RenewContractPayloadDto) {
    this.logger.log(`续约球员: ${JSON.stringify(payload.heroIds)}`);
    const result = await this.careerService.renewTheContract(payload.heroIds);
    return result; // 直接返回结果，因为已经包含了code和message
  }

  /**
   * 提升球员状态
   * 对应old项目: promoteHeroStatus（使用道具）
   */
  @MessagePattern('career.promoteStatus')
  @CacheEvict({
    key: 'hero:info:#{payload.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async promoteHeroStatus(@Payload() payload: PromoteStatusPayloadDto) {
    this.logger.log(`提升球员状态: ${payload.heroId}, 道具ID: ${payload.itemId}`);
    const result = await this.careerService.promoteHeroStatus(payload.heroId, payload.itemId);
    return result; // 直接返回结果，因为已经包含了code和message
  }

  /**
   * 加入退役名单
   * 对应old项目: addHeroToRetirementList
   */
  @MessagePattern('career.addToRetirement')
  @CacheEvict({
    key: 'character:heroes:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async addHeroToRetirementList(@Payload() payload: AddToRetirementPayloadDto) {
    this.logger.log(`加入退役名单: ${payload.heroId}`);
    const result = await this.careerService.addHeroToRetirementList(payload.heroId, payload.characterId);
    return {
      code: 0,
      message: '已加入退役名单',
      data: result,
    };
  }

  /**
   * 处理球员退役
   * 检查并处理到期的球员
   */
  @MessagePattern('career.processRetirement')
  @CacheEvict({
    key: 'character:heroes:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async processRetirement(@Payload() payload: ProcessRetirementPayloadDto) {
    this.logger.log(`处理球员退役: ${payload.characterId}`);
    const retiredHeroes = await this.careerService.checkAndProcessRetirement(payload.characterId);
    return {
      code: 0,
      message: '退役处理完成',
      data: {
        retiredHeroes,
        retiredCount: retiredHeroes.length,
      },
    };
  }

  /**
   * 获取球员生涯统计
   */
  @MessagePattern('career.getStats')
  @Cacheable({
    key: 'hero:career:stats:#{payload.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 600
  })
  async getCareerStats(@Payload() payload: GetStatsPayloadDto) {
    this.logger.log(`获取球员生涯统计: ${payload.heroId}`);
    const stats = await this.careerService.getCareerStats(payload.heroId);
    return {
      code: 0,
      message: '获取成功',
      data: stats,
    };
  }

  /**
   * 获取即将到期的球员列表
   */
  @MessagePattern('career.getExpiringContracts')
  @Cacheable({
    key: 'character:expiring:contracts:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 300
  })
  async getExpiringContracts(@Payload() payload: GetExpiringContractsPayloadDto) {
    this.logger.log(`获取即将到期的球员: ${payload.characterId}, ${payload.days}天内`);
    const expiringHeroes = await this.careerService.getExpiringContracts(payload.characterId, payload.days);
    return {
      code: 0,
      message: '获取成功',
      data: expiringHeroes,
    };
  }
}
