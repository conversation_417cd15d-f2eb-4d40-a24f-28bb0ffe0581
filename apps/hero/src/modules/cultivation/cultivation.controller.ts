import { Controller, Logger } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { CacheEvict, Cacheable } from '@libs/redis';
import { CultivationService } from './cultivation.service';

import { InjectedContext } from '@libs/common/types';
import {
  BreakOutPayloadDto,
  CultivatePayloadDto, OneKeyCultivatePayloadDto,
  ReBreakOutPayloadDto,
  UpStarPayloadDto
} from "@hero/common/dto/cultivation-payload.dto";

/**
 * 球员养成控制器
 * 基于old项目的养成相关功能
 */
@Controller()
export class CultivationController {
  private readonly logger = new Logger(CultivationController.name);

  constructor(private readonly cultivationService: CultivationService) {}

  /**
   * 球员养成
   * 对应old项目: cultivateHero
   */
  @MessagePattern('cultivation.cultivate')
  @CacheEvict({
    key: 'hero:info:#{payload.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async cultivateHero(@Payload() payload: CultivatePayloadDto) {
    this.logger.log(`球员养成: ${payload.heroId}`);
    const result = await this.cultivationService.cultivateHero(payload.heroId);
    return {
      code: 0,
      message: '养成成功',
      data: result,
    };
  }



  /**
   * 球员突破
   * 对应old项目: breakOutHero
   */
  @MessagePattern('cultivation.breakOut')
  @CacheEvict({
    key: 'hero:info:#{payload.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async breakOutHero(@Payload() payload: BreakOutPayloadDto) {
    this.logger.log(`球员突破: ${payload.heroId}, 次数: ${payload.index}`);
    const result = await this.cultivationService.breakOutHero(payload.heroId, payload.index, payload.arr);
    return result; // 直接返回结果，因为已经包含了code和message
  }

  /**
   * 重新突破
   * 对应old项目: reBreakOutHero
   */
  @MessagePattern('cultivation.reBreakOut')
  @CacheEvict({
    key: 'hero:info:#{payload.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async reBreakOutHero(@Payload() payload: ReBreakOutPayloadDto) {
    this.logger.log(`重新突破: ${payload.heroId}`);
    const result = await this.cultivationService.reBreakOutHero(payload.heroId);
    return {
      code: 0,
      message: '重新突破成功',
      data: result,
    };
  }

  /**
   * 球员升星
   * 对应old项目: heroUpStar
   */
  @MessagePattern('cultivation.upStar')
  @CacheEvict({
    key: 'hero:info:#{payload.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async heroUpStar(@Payload() payload: UpStarPayloadDto) {
    this.logger.log(`球员升星: ${payload.heroId}`);
    const result = await this.cultivationService.heroUpStar(payload.heroId);
    return {
      code: 0,
      message: result.isSuccess ? '升星成功' : '升星失败',
      data: result,
    };
  }



  /**
   * 一键养成
   * 对应old项目: oneKeyCultivateHero
   */
  @MessagePattern('cultivation.oneKeyCultivate')
  @CacheEvict({
    key: 'hero:info:#{payload.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async oneKeyCultivateHero(@Payload() payload: OneKeyCultivatePayloadDto) {
    this.logger.log(`一键养成: ${payload.heroId}, 类型: ${payload.type}`);
    const result = await this.cultivationService.oneKeyCultivateHero(payload.heroId, payload.type);
    return result; // 直接返回结果，因为已经包含了code和message
  }
}
