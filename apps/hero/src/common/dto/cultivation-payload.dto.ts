/**
 * Cultivation模块的Payload DTO定义
 * 
 * 为cultivation.controller.ts中的所有@MessagePattern接口生成对应的Payload DTO
 * 所有DTO都继承自BasePayloadDto，直接包含字段而不嵌套dto
 * 包含详细的校验规则和@Expose()装饰器
 */

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsNumber, IsOptional, Min, Max, Length, IsArray } from 'class-validator';
import { Expose } from 'class-transformer';
import { BasePayloadDto } from '@libs/common/dto/base-payload.dto';

// ==================== 1. 基础养成相关 ====================

/**
 * 球员养成Payload DTO
 * @MessagePattern('cultivation.cultivate')
 */
export class CultivatePayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '球员ID', example: 'hero_12345' })
  @Expose()
  @IsString({ message: '球员ID必须是字符串' })
  @Length(1, 50, { message: '球员ID长度必须在1-50个字符之间' })
  heroId: string;
}

/**
 * 球员升星Payload DTO
 * @MessagePattern('cultivation.upStar')
 */
export class UpStarPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '球员ID', example: 'hero_12345' })
  @Expose()
  @IsString({ message: '球员ID必须是字符串' })
  @Length(1, 50, { message: '球员ID长度必须在1-50个字符之间' })
  heroId: string;
}

// ==================== 2. 突破相关 ====================

/**
 * 球员突破Payload DTO
 * @MessagePattern('cultivation.breakOut')
 */
export class BreakOutPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '球员ID', example: 'hero_12345' })
  @Expose()
  @IsString({ message: '球员ID必须是字符串' })
  @Length(1, 50, { message: '球员ID长度必须在1-50个字符之间' })
  heroId: string;

  @ApiProperty({ description: '突破次数', example: 1 })
  @Expose()
  @IsNumber({}, { message: '突破次数必须是数字' })
  @Min(1, { message: '突破次数不能小于1' })
  @Max(10, { message: '突破次数不能大于10' })
  index: number;

  @ApiPropertyOptional({ description: '突破配置数组（可选）', type: [Number], example: [1, 2, 3] })
  @Expose()
  @IsOptional()
  @IsArray({ message: '突破配置必须是数组' })
  @IsNumber({}, { each: true, message: '突破配置中的每个值必须是数字' })
  @Min(0, { each: true, message: '突破配置值不能小于0' })
  @Max(999, { each: true, message: '突破配置值不能大于999' })
  arr?: number[];
}

/**
 * 重新突破Payload DTO
 * @MessagePattern('cultivation.reBreakOut')
 */
export class ReBreakOutPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '球员ID', example: 'hero_12345' })
  @Expose()
  @IsString({ message: '球员ID必须是字符串' })
  @Length(1, 50, { message: '球员ID长度必须在1-50个字符之间' })
  heroId: string;
}

// ==================== 3. 一键操作相关 ====================

/**
 * 一键养成Payload DTO
 * @MessagePattern('cultivation.oneKeyCultivate')
 */
export class OneKeyCultivatePayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '球员ID', example: 'hero_12345' })
  @Expose()
  @IsString({ message: '球员ID必须是字符串' })
  @Length(1, 50, { message: '球员ID长度必须在1-50个字符之间' })
  heroId: string;

  @ApiProperty({ description: '一键养成类型', example: 1, enum: [1, 2, 3] })
  @Expose()
  @IsNumber({}, { message: '养成类型必须是数字' })
  @Min(1, { message: '养成类型不能小于1' })
  @Max(10, { message: '养成类型不能大于10' })
  type: number;
}
