/**
 * Hero模块的Payload DTO定义
 * 
 * 为hero.controller.ts中的所有@MessagePattern接口生成对应的Payload DTO
 * 所有DTO都继承自BasePayloadDto，直接包含字段而不嵌套dto
 * 包含详细的校验规则和@Expose()装饰器
 */

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsNumber, IsOptional, IsBoolean, IsEnum, IsArray, Min, Max, Length } from 'class-validator';
import { Expose } from 'class-transformer';
import { BasePayloadDto } from '@libs/common/dto/base-payload.dto';
import { HeroPosition } from '../schemas/hero.schema';

// ==================== 1. 球员配置相关 ====================

/**
 * 获取球员配置Payload DTO
 * @MessagePattern('hero.getConfig')
 */
export class GetConfigPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '球员配置ID', example: 1001 })
  @Expose()
  @IsNumber({}, { message: '球员配置ID必须是数字' })
  @Min(1, { message: '球员配置ID不能小于1' })
  @Max(999999, { message: '球员配置ID不能大于999999' })
  heroId: number;
}

/**
 * 根据位置获取球员配置Payload DTO
 * @MessagePattern('hero.getConfigByPosition')
 */
export class GetConfigByPositionPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '球员位置', enum: HeroPosition, example: HeroPosition.GK })
  @Expose()
  @IsEnum(HeroPosition, { message: '球员位置必须是有效的枚举值' })
  position: HeroPosition;

  @ApiPropertyOptional({ description: '限制数量（可选）', example: 10 })
  @Expose()
  @IsOptional()
  @IsNumber({}, { message: '限制数量必须是数字' })
  @Min(1, { message: '限制数量不能小于1' })
  @Max(100, { message: '限制数量不能大于100' })
  limit?: number;
}

// ==================== 2. 球员状态管理相关 ====================

/**
 * 设置球员治疗状态Payload DTO
 * @MessagePattern('hero.setTreatStatus')
 */
export class SetTreatStatusPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '球员ID', example: 'hero_12345' })
  @Expose()
  @IsString({ message: '球员ID必须是字符串' })
  @Length(1, 50, { message: '球员ID长度必须在1-50个字符之间' })
  heroId: string;

  @ApiProperty({ description: '是否治疗中', example: true })
  @Expose()
  @IsBoolean({ message: '是否治疗中必须是布尔值' })
  isTreat: boolean;
}

/**
 * 更新球员疲劳值Payload DTO
 * @MessagePattern('hero.updateFatigue')
 */
export class UpdateFatiguePayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '球员ID', example: 'hero_12345' })
  @Expose()
  @IsString({ message: '球员ID必须是字符串' })
  @Length(1, 50, { message: '球员ID长度必须在1-50个字符之间' })
  heroId: string;

  @ApiProperty({ description: '疲劳值变化', example: -10 })
  @Expose()
  @IsNumber({}, { message: '疲劳值变化必须是数字' })
  @Min(-100, { message: '疲劳值变化不能小于-100' })
  @Max(100, { message: '疲劳值变化不能大于100' })
  fatigueChange: number;
}

// ==================== 3. 球员基础操作相关 ====================

/**
 * 创建球员Payload DTO
 * @MessagePattern('hero.create')
 * 扩展CreateHeroDto，合并BasePayloadDto内容
 */
export class CreatePayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '球员配置ID', example: 1001 })
  @Expose()
  @IsNumber({}, { message: '球员配置ID必须是数字' })
  @Min(1, { message: '球员配置ID不能小于1' })
  @Max(999999, { message: '球员配置ID不能大于999999' })
  resId: number;

  @ApiPropertyOptional({ description: '球员名称（可选）', example: 'Messi' })
  @Expose()
  @IsOptional()
  @IsString({ message: '球员名称必须是字符串' })
  @Length(1, 50, { message: '球员名称长度必须在1-50个字符之间' })
  name?: string;

  @ApiPropertyOptional({ description: '初始等级（可选）', example: 1 })
  @Expose()
  @IsOptional()
  @IsNumber({}, { message: '初始等级必须是数字' })
  @Min(1, { message: '初始等级不能小于1' })
  @Max(100, { message: '初始等级不能大于100' })
  level?: number;
}

/**
 * 批量获取球员信息Payload DTO
 * @MessagePattern('hero.getBatch')
 */
export class GetBatchPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '球员ID列表', type: [String], example: ['hero_12345', 'hero_67890'] })
  @Expose()
  @IsArray({ message: '球员ID列表必须是数组' })
  @IsString({ each: true, message: '球员ID必须是字符串' })
  @Length(1, 50, { each: true, message: '球员ID长度必须在1-50个字符之间' })
  heroIds: string[];
}

/**
 * 获取球员信息Payload DTO
 * @MessagePattern('hero.getInfo')
 */
export class GetInfoPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '球员ID', example: 'hero_12345' })
  @Expose()
  @IsString({ message: '球员ID必须是字符串' })
  @Length(1, 50, { message: '球员ID长度必须在1-50个字符之间' })
  heroId: string;
}

/**
 * 更新球员信息Payload DTO
 * @MessagePattern('hero.update')
 * 扩展UpdateHeroDto，合并BasePayloadDto内容
 */
export class UpdatePayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '球员ID', example: 'hero_12345' })
  @Expose()
  @IsString({ message: '球员ID必须是字符串' })
  @Length(1, 50, { message: '球员ID长度必须在1-50个字符之间' })
  heroId: string;

  @ApiPropertyOptional({ description: '球员名称（可选）', example: 'New Name' })
  @Expose()
  @IsOptional()
  @IsString({ message: '球员名称必须是字符串' })
  @Length(1, 50, { message: '球员名称长度必须在1-50个字符之间' })
  name?: string;

  @ApiPropertyOptional({ description: '等级（可选）', example: 10 })
  @Expose()
  @IsOptional()
  @IsNumber({}, { message: '等级必须是数字' })
  @Min(1, { message: '等级不能小于1' })
  @Max(100, { message: '等级不能大于100' })
  level?: number;

  @ApiPropertyOptional({ description: '经验值（可选）', example: 1500 })
  @Expose()
  @IsOptional()
  @IsNumber({}, { message: '经验值必须是数字' })
  @Min(0, { message: '经验值不能小于0' })
  exp?: number;
}
