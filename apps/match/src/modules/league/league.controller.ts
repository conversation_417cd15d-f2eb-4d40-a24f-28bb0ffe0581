import { Controller, Logger } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { LeagueService } from './league.service';
import {
  GetLeagueCopyDataDto,
  PVELeagueBattleDto,
  TakeLeagueRewardDto,
  BuyLeagueTimesDto,
  GetLeagueCopyDataResponseDto,
  PVEBattleResultResponseDto
} from '../../common/dto/league.dto';
import { Cacheable, CacheEvict } from '@libs/redis';

import {
  BuyLeagueTimesPayloadDto,
  GetLeagueCopyDataPayloadDto,
  GetStatisticsPayloadDto,
  PveBattlePayloadDto,
  TakeLeagueRewardPayloadDto
} from "@match/common/dto/league-payload.dto";

/**
 * 联赛系统控制器
 * 严格基于old项目leagueCopy.js的接口设计
 * 
 * 核心接口：
 * - league.getLeagueCopyData: 获取联赛副本数据
 * - league.pveBattle: PVE联赛战斗
 * - league.takeLeagueReward: 领取联赛奖励
 */
@Controller()
export class LeagueController {
  private readonly logger = new Logger(LeagueController.name);

  constructor(private readonly leagueService: LeagueService) {}

  /**
   * 获取联赛副本数据
   * 基于old项目的getLeagueCopyData接口
   */
  @MessagePattern('league.getLeagueCopyData')
  @Cacheable({
    key: 'league:data:#{payload.characterId}:#{payload.type || "all"}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 300 // 5分钟缓存
  })
  async getLeagueCopyData(@Payload() payload: GetLeagueCopyDataPayloadDto): Promise<GetLeagueCopyDataResponseDto> {
    this.logger.log(`获取联赛副本数据: ${payload.characterId}, 类型: ${payload.type || 'all'}`);
    
    try {
      const result = await this.leagueService.getLeagueCopyData(payload);
      
      if (result.code === 0) {
        this.logger.log(`联赛数据获取成功: ${payload.characterId}, 下一联赛: ${result.nextLeagueId}, 下一副本: ${result.nextTeamCopyId}`);
      } else {
        this.logger.warn(`联赛数据获取失败: ${payload.characterId}, code: ${result.code}`);
      }
      
      return result;
    } catch (error) {
      this.logger.error('获取联赛副本数据异常', error);
      return {
        code: -1,
        nextLeagueId: 0,
        nextTeamCopyId: 0,
        leagueCopyData: [],
      };
    }
  }

  /**
   * PVE联赛战斗
   * 基于old项目的PVELeagueCopyBattle接口
   */
  @MessagePattern('league.pveBattle')
  @CacheEvict({
    key: 'league:data:#{payload.characterId}:*',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async pveBattle(@Payload() payload: PveBattlePayloadDto): Promise<PVEBattleResultResponseDto> {
    this.logger.log(`PVE联赛战斗: ${payload.characterId}, 联赛${payload.leagueId}, 副本${payload.teamCopyId}`);
    
    try {
      const result = await this.leagueService.pveBattle(payload);
      
      if (result.code === 0) {
        this.logger.log(`PVE战斗成功: ${payload.characterId}, 获得奖励: ${result.rewards?.length || 0}个`);
      } else {
        this.logger.warn(`PVE战斗失败: ${payload.characterId}, ${result.message}`);
      }
      
      return result;
    } catch (error) {
      this.logger.error('PVE联赛战斗异常', error);
      return {
        code: -1,
        message: '战斗处理异常',
      };
    }
  }

  /**
   * 领取联赛奖励
   * 基于old项目的联赛奖励发放逻辑
   */
  @MessagePattern('league.takeLeagueReward')
  @CacheEvict({
    key: 'league:data:#{payload.characterId}:*',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async takeLeagueReward(@Payload() payload: TakeLeagueRewardPayloadDto) {
    this.logger.log(`领取联赛奖励: ${payload.characterId}, 联赛${payload.leagueId}`);
    
    try {
      const result = await this.leagueService.takeLeagueReward(payload);
      
      if (result.code === 0) {
        this.logger.log(`联赛奖励领取成功: ${payload.characterId}, 奖励: ${result.rewards?.length || 0}个`);
      } else {
        this.logger.warn(`联赛奖励领取失败: ${payload.characterId}, ${result.message}`);
      }
      
      return result;
    } catch (error) {
      this.logger.error('领取联赛奖励异常', error);
      return {
        code: -1,
        message: '奖励领取异常',
      };
    }
  }

  /**
   * 购买联赛次数
   * 基于old项目的购买次数逻辑
   */
  @MessagePattern('league.buyLeagueTimes')
  @CacheEvict({
    key: 'league:data:#{payload.characterId}:*',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async buyLeagueTimes(@Payload() payload: BuyLeagueTimesPayloadDto) {
    this.logger.log(`购买联赛次数: ${payload.characterId}, 次数: ${payload.times}`);

    try {
      const result = await this.leagueService.buyLeagueTimes(payload);

      if (result.code === 0) {
        this.logger.log(`购买联赛次数成功: ${payload.characterId}, 购买次数: ${payload.times}`);
      } else {
        this.logger.warn(`购买联赛次数失败: ${payload.characterId}, ${result.message}`);
      }

      return result;
    } catch (error) {
      this.logger.error('购买联赛次数异常', error);
      return {
        code: -1,
        message: '购买次数异常',
      };
    }
  }

  /**
   * 获取联赛统计信息（管理接口）
   */
  @MessagePattern('league.getStatistics')
  async getStatistics(@Payload() payload: GetStatisticsPayloadDto) {
    this.logger.log('获取联赛统计信息');

    try {
      // 这里可以添加统计信息的获取逻辑
      return {
        code: 0,
        message: '获取成功',
        data: {
          totalCharacters: 0,
          activeToday: 0,
          timestamp: new Date(),
        },
      };
    } catch (error) {
      this.logger.error('获取联赛统计信息异常', error);
      return {
        code: -1,
        message: '获取统计信息失败',
      };
    }
  }
}
