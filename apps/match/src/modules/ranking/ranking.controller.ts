import { Controller, Logger } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { RankingService } from './ranking.service';
import { 
  GetGlobalRankingDto,
  GetCharacterRankingDto,
  ClaimRankingRewardDto,
  UpdateRankingDto,
  GetGlobalRankingResponseDto,
  GetCharacterRankingResponseDto,
  ClaimRankingRewardResponseDto,
  UpdateRankingResponseDto
} from '../../common/dto/ranking.dto';
import { Cacheable, CacheEvict } from '@libs/redis';

import {
  ClaimRankingRewardPayloadDto,
  CleanExpiredRankingsPayloadDto,
  GetCharacterRankingPayloadDto,
  GetGlobalRankingPayloadDto,
  GetStatisticsPayloadDto,
  UpdateAllRankingsPayloadDto,
  UpdateRankingPayloadDto
} from "@match/common/dto/ranking-payload.dto";

/**
 * 排名系统控制器
 * 基于old项目中各种排名功能的接口设计
 * 
 * 核心接口：
 * - ranking.getGlobalRanking: 获取全球排名
 * - ranking.getCharacterRanking: 获取玩家排名信息
 * - ranking.claimRankingReward: 领取排名奖励
 * - ranking.updateRanking: 更新排名数据（管理接口）
 */
@Controller()
export class RankingController {
  private readonly logger = new Logger(RankingController.name);

  constructor(private readonly rankingService: RankingService) {}

  /**
   * 获取全球排名
   * 基于old项目的全球排名功能
   */
  @MessagePattern('ranking.getGlobalRanking')
  @Cacheable({
    key: 'ranking:global:#{payload.rankType}:#{payload.limit}:#{payload.offset}',
    dataType: 'global',
    ttl: 600 // 10分钟缓存
  })
  async getGlobalRanking(@Payload() payload: GetGlobalRankingPayloadDto): Promise<GetGlobalRankingResponseDto> {
    this.logger.log(`获取全球排名: ${payload.rankType}, 限制: ${payload.limit || 100}`);
    
    try {
      const result = await this.rankingService.getGlobalRanking(payload);
      
      if (result.code === 0) {
        this.logger.log(`全球排名获取成功: ${payload.rankType}, 数量: ${result.rankings?.length || 0}`);
      } else {
        this.logger.warn(`全球排名获取失败: ${payload.rankType}, ${result.message}`);
      }
      
      return result;
    } catch (error) {
      this.logger.error('获取全球排名异常', error);
      return {
        code: -1,
        message: '获取全球排名异常',
      };
    }
  }

  /**
   * 获取玩家排名信息
   * 基于old项目的玩家个人排名功能
   */
  @MessagePattern('ranking.getCharacterRanking')
  @Cacheable({
    key: 'ranking:character:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 300 // 5分钟缓存
  })
  async getCharacterRanking(@Payload() payload: GetCharacterRankingPayloadDto): Promise<GetCharacterRankingResponseDto> {
    this.logger.log(`获取玩家排名信息: ${payload.characterId}`);
    
    try {
      const result = await this.rankingService.getCharacterRanking(payload);
      
      if (result.code === 0) {
        this.logger.log(`玩家排名信息获取成功: ${payload.characterId}, 可领取奖励: ${result.availableRewards?.length || 0}`);
      } else {
        this.logger.warn(`玩家排名信息获取失败: ${payload.characterId}, ${result.message}`);
      }
      
      return result;
    } catch (error) {
      this.logger.error('获取玩家排名信息异常', error);
      return {
        code: -1,
        message: '获取玩家排名信息异常',
      };
    }
  }

  /**
   * 领取排名奖励
   * 基于old项目的排名奖励发放功能
   */
  @MessagePattern('ranking.claimRankingReward')
  @CacheEvict({
    key: 'ranking:character:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async claimRankingReward(@Payload() payload: ClaimRankingRewardPayloadDto): Promise<ClaimRankingRewardResponseDto> {
    this.logger.log(`领取排名奖励: ${payload.characterId}, 类型: ${payload.rankType}, 赛季: ${payload.season}`);
    
    try {
      const result = await this.rankingService.claimRankingReward(payload);
      
      if (result.code === 0) {
        this.logger.log(`排名奖励领取成功: ${payload.characterId}, 排名: ${result.rank}, 奖励数量: ${result.rewards?.length || 0}`);
      } else {
        this.logger.warn(`排名奖励领取失败: ${payload.characterId}, ${result.message}`);
      }
      
      return result;
    } catch (error) {
      this.logger.error('领取排名奖励异常', error);
      return {
        code: -1,
        message: '领取排名奖励异常',
      };
    }
  }

  /**
   * 更新排名数据（管理接口）
   * 基于old项目的排名更新功能
   */
  @MessagePattern('ranking.updateRanking')
  @CacheEvict({
    key: 'ranking:global:#{payload.rankType}:*',
    dataType: 'global'
  })
  async updateRanking(@Payload() payload: UpdateRankingPayloadDto): Promise<UpdateRankingResponseDto> {
    this.logger.log(`更新排名数据: ${payload.rankType}, 赛季: ${payload.season || 'current'}`);
    
    try {
      const result = await this.rankingService.updateRanking(payload);
      
      if (result.code === 0) {
        this.logger.log(`排名数据更新成功: ${payload.rankType}, 更新数量: ${result.updatedCount}`);
      } else {
        this.logger.warn(`排名数据更新失败: ${payload.rankType}, ${result.message}`);
      }
      
      return result;
    } catch (error) {
      this.logger.error('更新排名数据异常', error);
      return {
        code: -1,
        message: '更新排名数据异常',
      };
    }
  }

  /**
   * 获取排名统计信息（管理接口）
   */
  @MessagePattern('ranking.getStatistics')
  async getStatistics(@Payload() payload: GetStatisticsPayloadDto) {
    this.logger.log('获取排名统计信息');
    
    try {
      const statistics = await this.rankingService.getStatistics();
      
      return {
        code: 0,
        message: '获取成功',
        data: statistics,
      };
    } catch (error) {
      this.logger.error('获取排名统计信息异常', error);
      return {
        code: -1,
        message: '获取统计信息失败',
      };
    }
  }

  /**
   * 清理过期排名数据（管理接口）
   */
  @MessagePattern('ranking.cleanExpiredRankings')
  async cleanExpiredRankings(@Payload() payload: CleanExpiredRankingsPayloadDto) {
    this.logger.log('清理过期排名数据');
    
    try {
      // 这里可以添加清理逻辑
      return {
        code: 0,
        message: '清理完成',
        data: { cleanedCount: 0 },
      };
    } catch (error) {
      this.logger.error('清理过期排名数据异常', error);
      return {
        code: -1,
        message: '清理失败',
      };
    }
  }

  /**
   * 批量更新所有排名（管理接口）
   */
  @MessagePattern('ranking.updateAllRankings')
  async updateAllRankings(@Payload() payload: UpdateAllRankingsPayloadDto) {
    this.logger.log(`批量更新所有排名: 赛季 ${payload.season || 'current'}`);
    
    try {
      const rankTypes = ['fans', 'strength', 'level'];
      const results = [];

      for (const rankType of rankTypes) {
        const result = await this.rankingService.updateRanking({
          rankType,
          season: payload.season,
        });
        results.push({ rankType, ...result });
      }

      const successCount = results.filter(r => r.code === 0).length;

      this.logger.log(`批量更新结果统计: 成功 ${successCount}/${rankTypes.length}`);
      this.logger.log(`详细结果: ${JSON.stringify(results)}`);

      const response = {
        code: successCount === rankTypes.length ? 0 : -1,
        message: `批量更新完成，成功: ${successCount}/${rankTypes.length}`,
        data: { results },
      };

      this.logger.log(`返回响应: ${JSON.stringify(response)}`);
      return response;
    } catch (error) {
      this.logger.error('批量更新所有排名异常', error);
      return {
        code: -1,
        message: '批量更新失败',
      };
    }
  }
}
