import { Controller, Logger } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { BusinessService } from './business.service';
import { 
  GetBusinessMatchInfoDto,
  BusinessSearchDto,
  BusinessMatchDto,
  BuyBusinessMatchDto,
  GetBusinessMatchInfoResponseDto,
  BusinessSearchResponseDto,
  BusinessMatchResponseDto
} from '../../common/dto/business.dto';
import { Cacheable, CacheEvict } from '@libs/redis';

import {
  BusinessMatchPayloadDto,
  BusinessSearchPayloadDto,
  BuyBusinessMatchPayloadDto,
  GetBusinessMatchInfoPayloadDto,
  GetStatisticsPayloadDto
} from "@match/common/dto/business-payload.dto";

/**
 * 商业赛系统控制器
 * 严格基于old项目businessMatch.js的接口设计
 * 
 * 核心接口：
 * - business.getBusinessMatchInfo: 获取商业赛信息
 * - business.businessSearch: 商业赛搜索
 * - business.businessMatch: 商业赛匹配
 * - business.buyBusinessMatch: 购买商业赛次数
 */
@Controller()
export class BusinessController {
  private readonly logger = new Logger(BusinessController.name);

  constructor(private readonly businessService: BusinessService) {}

  /**
   * 获取商业赛信息
   * 基于old项目的getBusinessMatchInfo接口
   */
  @MessagePattern('business.getBusinessMatchInfo')
  @Cacheable({
    key: 'business:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 300 // 5分钟缓存
  })
  async getBusinessMatchInfo(@Payload() payload: GetBusinessMatchInfoPayloadDto): Promise<GetBusinessMatchInfoResponseDto> {
    this.logger.log(`获取商业赛信息: ${payload.characterId}`);
    
    try {
      const result = await this.businessService.getBusinessMatchInfo(payload);
      
      if (result.code === 0) {
        this.logger.log(`商业赛信息获取成功: ${payload.characterId}`);
      } else {
        this.logger.warn(`商业赛信息获取失败: ${payload.characterId}, ${result.message}`);
      }
      
      return result;
    } catch (error) {
      this.logger.error('获取商业赛信息异常', error);
      return {
        code: -1,
        message: '获取商业赛信息异常',
      };
    }
  }

  /**
   * 商业赛搜索
   * 基于old项目的businessSearch接口
   */
  @MessagePattern('business.businessSearch')
  @CacheEvict({
    key: 'business:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async businessSearch(@Payload() payload: BusinessSearchPayloadDto): Promise<BusinessSearchResponseDto> {
    this.logger.log(`商业赛搜索: ${payload.characterId}, 搜索: ${payload.name}`);
    
    try {
      const result = await this.businessService.businessSearch(payload);
      
      if (result.code === 0) {
        this.logger.log(`商业赛搜索成功: ${payload.characterId}, 找到对手: ${result.enemyInfo?.name}`);
      } else {
        this.logger.warn(`商业赛搜索失败: ${payload.characterId}, ${result.message}`);
      }
      
      return result;
    } catch (error) {
      this.logger.error('商业赛搜索异常', error);
      return {
        code: -1,
        message: '搜索异常',
      };
    }
  }

  /**
   * 商业赛匹配
   * 基于old项目的businessMatch接口
   */
  @MessagePattern('business.businessMatch')
  @CacheEvict({
    key: 'business:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async businessMatch(@Payload() payload: BusinessMatchPayloadDto): Promise<BusinessMatchResponseDto> {
    this.logger.log(`商业赛匹配: ${payload.characterId} vs ${payload.enemyUid}`);
    
    try {
      const result = await this.businessService.businessMatch(payload);
      
      if (result.code === 0) {
        this.logger.log(`商业赛匹配成功: ${payload.characterId} vs ${payload.enemyUid}, 结果: ${result.matchRecord?.result}`);
      } else {
        this.logger.warn(`商业赛匹配失败: ${payload.characterId} vs ${payload.enemyUid}, ${result.message}`);
      }
      
      return result;
    } catch (error) {
      this.logger.error('商业赛匹配异常', error);
      return {
        code: -1,
        message: '匹配异常',
      };
    }
  }

  /**
   * 购买商业赛次数
   * 基于old项目的buyBusinessMatch接口
   */
  @MessagePattern('business.buyBusinessMatch')
  @CacheEvict({
    key: 'business:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async buyBusinessMatch(@Payload() payload: BuyBusinessMatchPayloadDto) {
    this.logger.log(`购买商业赛次数: ${payload.characterId}, 数量: ${payload.num}`);
    
    try {
      const result = await this.businessService.buyBusinessMatch(payload);
      
      if (result.code === 0) {
        this.logger.log(`购买商业赛次数成功: ${payload.characterId}, 增加: ${payload.num}次`);
      } else {
        this.logger.warn(`购买商业赛次数失败: ${payload.characterId}, ${result.message}`);
      }
      
      return result;
    } catch (error) {
      this.logger.error('购买商业赛次数异常', error);
      return {
        code: -1,
        message: '购买失败',
      };
    }
  }

  /**
   * 获取商业赛统计信息（管理接口）
   */
  @MessagePattern('business.getStatistics')
  async getStatistics(@Payload() payload: GetStatisticsPayloadDto) {
    this.logger.log('获取商业赛统计信息');
    
    try {
      // 这里可以添加统计信息的获取逻辑
      return {
        code: 0,
        message: '获取成功',
        data: {
          totalCharacters: 0,
          activeToday: 0,
          totalMatches: 0,
          timestamp: new Date(),
        },
      };
    } catch (error) {
      this.logger.error('获取商业赛统计信息异常', error);
      return {
        code: -1,
        message: '获取统计信息失败',
      };
    }
  }

  /**
   * 重置每日战斗次数（管理接口）
   */
  @MessagePattern('business.resetDailyFightTimes')
  async resetDailyFightTimes(@Payload() payload: GetStatisticsPayloadDto) {
    this.logger.log(`重置每日战斗次数: ${payload.characterId || 'all'}`);
    
    try {
      // 这里可以添加重置逻辑
      return {
        code: 0,
        message: '重置成功',
      };
    } catch (error) {
      this.logger.error('重置每日战斗次数异常', error);
      return {
        code: -1,
        message: '重置失败',
      };
    }
  }
}
