import { Controller, Logger } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { BusinessService } from './business.service';
import { 
  GetBusinessMatchInfoDto,
  BusinessSearchDto,
  BusinessMatchDto,
  BuyBusinessMatchDto,
  GetBusinessMatchInfoResponseDto,
  BusinessSearchResponseDto,
  BusinessMatchResponseDto
} from '../../common/dto/business.dto';
import { Cacheable, CacheEvict } from '@libs/redis';

import { InjectedContext } from '@libs/common/types';

/**
 * 商业赛系统控制器
 * 严格基于old项目businessMatch.js的接口设计
 * 
 * 核心接口：
 * - business.getBusinessMatchInfo: 获取商业赛信息
 * - business.businessSearch: 商业赛搜索
 * - business.businessMatch: 商业赛匹配
 * - business.buyBusinessMatch: 购买商业赛次数
 */
@Controller()
export class BusinessController {
  private readonly logger = new Logger(BusinessController.name);

  constructor(private readonly businessService: BusinessService) {}

  /**
   * 获取商业赛信息
   * 基于old项目的getBusinessMatchInfo接口
   */
  @MessagePattern('business.getBusinessMatchInfo')
  @Cacheable({
    key: 'business:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 300 // 5分钟缓存
  })
  async getBusinessMatchInfo(@Payload() payload: {getBusinessMatchInfoDto: GetBusinessMatchInfoDto, injectedContext?: InjectedContext}): Promise<GetBusinessMatchInfoResponseDto> {
    this.logger.log(`获取商业赛信息: ${payload.getBusinessMatchInfoDto.characterId}`);
    
    try {
      const result = await this.businessService.getBusinessMatchInfo(payload.getBusinessMatchInfoDto);
      
      if (result.code === 0) {
        this.logger.log(`商业赛信息获取成功: ${payload.getBusinessMatchInfoDto.characterId}`);
      } else {
        this.logger.warn(`商业赛信息获取失败: ${payload.getBusinessMatchInfoDto.characterId}, ${result.message}`);
      }
      
      return result;
    } catch (error) {
      this.logger.error('获取商业赛信息异常', error);
      return {
        code: -1,
        message: '获取商业赛信息异常',
      };
    }
  }

  /**
   * 商业赛搜索
   * 基于old项目的businessSearch接口
   */
  @MessagePattern('business.businessSearch')
  @CacheEvict({
    key: 'business:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async businessSearch(@Payload() payload: {businessSearchDto: BusinessSearchDto, injectedContext?: InjectedContext}): Promise<BusinessSearchResponseDto> {
    this.logger.log(`商业赛搜索: ${payload.businessSearchDto.characterId}, 搜索: ${payload.businessSearchDto.name}`);
    
    try {
      const result = await this.businessService.businessSearch(payload.businessSearchDto);
      
      if (result.code === 0) {
        this.logger.log(`商业赛搜索成功: ${payload.businessSearchDto.characterId}, 找到对手: ${result.enemyInfo?.name}`);
      } else {
        this.logger.warn(`商业赛搜索失败: ${payload.businessSearchDto.characterId}, ${result.message}`);
      }
      
      return result;
    } catch (error) {
      this.logger.error('商业赛搜索异常', error);
      return {
        code: -1,
        message: '搜索异常',
      };
    }
  }

  /**
   * 商业赛匹配
   * 基于old项目的businessMatch接口
   */
  @MessagePattern('business.businessMatch')
  @CacheEvict({
    key: 'business:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async businessMatch(@Payload() payload: {businessMatchDto: BusinessMatchDto, injectedContext?: InjectedContext}): Promise<BusinessMatchResponseDto> {
    this.logger.log(`商业赛匹配: ${payload.businessMatchDto.characterId} vs ${payload.businessMatchDto.enemyUid}`);
    
    try {
      const result = await this.businessService.businessMatch(payload.businessMatchDto);
      
      if (result.code === 0) {
        this.logger.log(`商业赛匹配成功: ${payload.businessMatchDto.characterId} vs ${payload.businessMatchDto.enemyUid}, 结果: ${result.matchRecord?.result}`);
      } else {
        this.logger.warn(`商业赛匹配失败: ${payload.businessMatchDto.characterId} vs ${payload.businessMatchDto.enemyUid}, ${result.message}`);
      }
      
      return result;
    } catch (error) {
      this.logger.error('商业赛匹配异常', error);
      return {
        code: -1,
        message: '匹配异常',
      };
    }
  }

  /**
   * 购买商业赛次数
   * 基于old项目的buyBusinessMatch接口
   */
  @MessagePattern('business.buyBusinessMatch')
  @CacheEvict({
    key: 'business:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async buyBusinessMatch(@Payload() payload: {buyBusinessMatchDto: BuyBusinessMatchDto, injectedContext?: InjectedContext}) {
    this.logger.log(`购买商业赛次数: ${payload.buyBusinessMatchDto.characterId}, 数量: ${payload.buyBusinessMatchDto.num}`);
    
    try {
      const result = await this.businessService.buyBusinessMatch(payload.buyBusinessMatchDto);
      
      if (result.code === 0) {
        this.logger.log(`购买商业赛次数成功: ${payload.buyBusinessMatchDto.characterId}, 增加: ${payload.buyBusinessMatchDto.num}次`);
      } else {
        this.logger.warn(`购买商业赛次数失败: ${payload.buyBusinessMatchDto.characterId}, ${result.message}`);
      }
      
      return result;
    } catch (error) {
      this.logger.error('购买商业赛次数异常', error);
      return {
        code: -1,
        message: '购买失败',
      };
    }
  }

  /**
   * 获取商业赛统计信息（管理接口）
   */
  @MessagePattern('business.getStatistics')
  async getStatistics(@Payload() payload: {injectedContext?: InjectedContext}) {
    this.logger.log('获取商业赛统计信息');
    
    try {
      // 这里可以添加统计信息的获取逻辑
      return {
        code: 0,
        message: '获取成功',
        data: {
          totalCharacters: 0,
          activeToday: 0,
          totalMatches: 0,
          timestamp: new Date(),
        },
      };
    } catch (error) {
      this.logger.error('获取商业赛统计信息异常', error);
      return {
        code: -1,
        message: '获取统计信息失败',
      };
    }
  }

  /**
   * 重置每日战斗次数（管理接口）
   */
  @MessagePattern('business.resetDailyFightTimes')
  async resetDailyFightTimes(@Payload() payload: { characterId?: string; injectedContext?: InjectedContext }) {
    this.logger.log(`重置每日战斗次数: ${payload.characterId || 'all'}`);
    
    try {
      // 这里可以添加重置逻辑
      return {
        code: 0,
        message: '重置成功',
      };
    } catch (error) {
      this.logger.error('重置每日战斗次数异常', error);
      return {
        code: -1,
        message: '重置失败',
      };
    }
  }
}
