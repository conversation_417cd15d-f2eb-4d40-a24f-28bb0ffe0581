import { <PERSON>, Logger } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { TournamentService } from './tournament.service';
import { 
  GetWorldCupInfoDto,
  JoinWorldCupDto,
  WorldCupBattleDto,
  BuyWorldCupTimesDto,
  GetWorldCupRewardDto,
  JoinRegionalCupDto,
  RegionalCupBattleDto,
  WorldCupInfoResponseDto,
  JoinWorldCupResponseDto,
  WorldCupBattleResponseDto,
  RegionalCupInfoResponseDto
} from '../../common/dto/tournament.dto';
import { Cacheable, CacheEvict } from '@libs/redis';

import { InjectedContext } from '@libs/common/types';
import {
  BuyWorldCupTimesPayloadDto,
  GetStatisticsPayloadDto,
  GetWorldCupInfoPayloadDto,
  GetWorldCupRewardPayloadDto,
  JoinRegionalCupPayloadDto,
  JoinWorldCupPayloadDto,
  RegionalCupBattlePayloadDto,
  ResetDailyDataPayloadDto,
  WorldCupBattlePayloadDto
} from "@match/common/dto/tournament-payload.dto";

/**
 * 锦标赛系统控制器
 * 严格基于old项目worldCup.js、middleEastCup.js等的接口设计
 * 
 * 核心接口：
 * - tournament.getWorldCupInfo: 获取世界杯信息
 * - tournament.joinWorldCup: 参加世界杯
 * - tournament.worldCupBattle: 世界杯战斗
 * - tournament.buyWorldCupTimes: 购买世界杯次数
 * - tournament.getWorldCupReward: 领取世界杯奖励
 * - tournament.joinRegionalCup: 参加区域杯赛
 * - tournament.regionalCupBattle: 区域杯赛战斗
 */
@Controller()
export class TournamentController {
  private readonly logger = new Logger(TournamentController.name);

  constructor(private readonly tournamentService: TournamentService) {}

  /**
   * 获取世界杯信息
   * 基于old项目的getWorldCupInfo接口
   */
  @MessagePattern('tournament.getWorldCupInfo')
  @Cacheable({
    key: 'tournament:worldcup:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 300 // 5分钟缓存
  })
  async getWorldCupInfo(@Payload() payload: GetWorldCupInfoPayloadDto): Promise<WorldCupInfoResponseDto> {
    this.logger.log(`获取世界杯信息: ${payload.characterId}`);
    
    try {
      const result = await this.tournamentService.getWorldCupInfo(payload);
      
      if (result.code === 0) {
        this.logger.log(`世界杯信息获取成功: ${payload.characterId}, 参赛状态: ${result.isJoin}`);
      } else {
        this.logger.warn(`世界杯信息获取失败: ${payload.characterId}, ${result.message}`);
      }
      
      return result;
    } catch (error) {
      this.logger.error('获取世界杯信息异常', error);
      return {
        code: -1,
        message: '获取世界杯信息异常',
      };
    }
  }

  /**
   * 参加世界杯
   * 基于old项目的joinWorldCup接口
   */
  @MessagePattern('tournament.joinWorldCup')
  @CacheEvict({
    key: 'tournament:worldcup:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async joinWorldCup(@Payload() payload: JoinWorldCupPayloadDto): Promise<JoinWorldCupResponseDto> {
    this.logger.log(`参加世界杯: ${payload.characterId}, 副本${payload.worldCupId}`);
    
    try {
      const result = await this.tournamentService.joinWorldCup(payload);
      
      if (result.code === 0) {
        this.logger.log(`参加世界杯成功: ${payload.characterId}, 副本${payload.worldCupId}`);
      } else {
        this.logger.warn(`参加世界杯失败: ${payload.characterId}, ${result.message}`);
      }
      
      return result;
    } catch (error) {
      this.logger.error('参加世界杯异常', error);
      return {
        code: -1,
        message: '参加世界杯异常',
      };
    }
  }

  /**
   * 世界杯战斗
   * 基于old项目的worldCupBattle接口
   */
  @MessagePattern('tournament.worldCupBattle')
  @CacheEvict({
    key: 'tournament:worldcup:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async worldCupBattle(@Payload() payload: WorldCupBattlePayloadDto): Promise<WorldCupBattleResponseDto> {
    this.logger.log(`世界杯战斗: ${payload.characterId}`);
    
    try {
      const result = await this.tournamentService.worldCupBattle(payload);
      
      if (result.code === 0) {
        this.logger.log(`世界杯战斗成功: ${payload.characterId}, 结果: ${result.result}, 轮次: ${result.groupNum}`);
      } else {
        this.logger.warn(`世界杯战斗失败: ${payload.characterId}, ${result.message}`);
      }
      
      return result;
    } catch (error) {
      this.logger.error('世界杯战斗异常', error);
      return {
        code: -1,
        message: '战斗处理异常',
      };
    }
  }

  /**
   * 购买世界杯次数
   * 基于old项目的buyWorldCupTimes接口
   */
  @MessagePattern('tournament.buyWorldCupTimes')
  @CacheEvict({
    key: 'tournament:worldcup:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async buyWorldCupTimes(@Payload() payload: BuyWorldCupTimesPayloadDto) {
    this.logger.log(`购买世界杯次数: ${payload.characterId}`);
    
    try {
      const result = await this.tournamentService.buyWorldCupTimes(payload);
      
      if (result.code === 0) {
        this.logger.log(`购买世界杯次数成功: ${payload.characterId}, 费用: ${result.totalCost}`);
      } else {
        this.logger.warn(`购买世界杯次数失败: ${payload.characterId}, ${result.message}`);
      }
      
      return result;
    } catch (error) {
      this.logger.error('购买世界杯次数异常', error);
      return {
        code: -1,
        message: '购买失败',
      };
    }
  }

  /**
   * 领取世界杯奖励
   * 基于old项目的getWorldCupReward接口
   */
  @MessagePattern('tournament.getWorldCupReward')
  @CacheEvict({
    key: 'tournament:worldcup:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async getWorldCupReward(@Payload() payload: GetWorldCupRewardPayloadDto) {
    this.logger.log(`领取世界杯奖励: ${payload.characterId}`);
    
    try {
      const result = await this.tournamentService.getWorldCupReward(payload);
      
      if (result.code === 0) {
        this.logger.log(`领取世界杯奖励成功: ${payload.characterId}`);
      } else {
        this.logger.warn(`领取世界杯奖励失败: ${payload.characterId}, ${result.message}`);
      }
      
      return result;
    } catch (error) {
      this.logger.error('领取世界杯奖励异常', error);
      return {
        code: -1,
        message: '奖励领取异常',
      };
    }
  }

  /**
   * 参加区域杯赛
   * 基于old项目的joinRegionalCup接口
   */
  @MessagePattern('tournament.joinRegionalCup')
  @CacheEvict({
    key: 'tournament:regional:#{payload.characterId}:#{payload.cupType}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async joinRegionalCup(@Payload() payload: JoinRegionalCupPayloadDto): Promise<RegionalCupInfoResponseDto> {
    this.logger.log(`参加区域杯赛: ${payload.characterId}, 类型: ${payload.cupType}`);

    try {
      const result = await this.tournamentService.joinRegionalCup(payload);

      if (result.code === 0) {
        this.logger.log(`参加区域杯赛成功: ${payload.characterId}, 类型: ${payload.cupType}`);
      } else {
        this.logger.warn(`参加区域杯赛失败: ${payload.characterId}, ${result.message}`);
      }

      return result;
    } catch (error) {
      this.logger.error('参加区域杯赛异常', error);
      return {
        code: -1,
        message: '参加失败',
      };
    }
  }

  /**
   * 区域杯赛战斗
   * 基于old项目的MiddleEastCupBattle、gulfCupBattle等接口
   * 修复：实现缺失的区域杯赛战斗功能
   */
  @MessagePattern('tournament.regionalCupBattle')
  @CacheEvict({
    key: 'tournament:regional:#{payload.characterId}:#{payload.cupType}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async regionalCupBattle(@Payload() payload: RegionalCupBattlePayloadDto) {
    this.logger.log(`区域杯赛战斗: ${payload.characterId}, 类型: ${payload.cupType}`);

    try {
      const result = await this.tournamentService.regionalCupBattle(payload);

      if (result.code === 0) {
        this.logger.log(`区域杯赛战斗成功: ${payload.characterId}, 类型: ${payload.cupType}, 结果: ${result.result}`);
      } else {
        this.logger.warn(`区域杯赛战斗失败: ${payload.characterId}, ${result.message}`);
      }

      return result;
    } catch (error) {
      this.logger.error('区域杯赛战斗异常', error);
      return {
        code: -1,
        message: '战斗处理异常',
      };
    }
  }

  /**
   * 获取锦标赛统计信息（管理接口）
   */
  @MessagePattern('tournament.getStatistics')
  async getStatistics(@Payload() payload: GetStatisticsPayloadDto) {
    this.logger.log('获取锦标赛统计信息');
    
    try {
      // 这里可以添加统计信息的获取逻辑
      return {
        code: 0,
        message: '获取成功',
        data: {
          totalCharacters: 0,
          activeToday: 0,
          worldCupParticipants: 0,
          timestamp: new Date(),
        },
      };
    } catch (error) {
      this.logger.error('获取锦标赛统计信息异常', error);
      return {
        code: -1,
        message: '获取统计信息失败',
      };
    }
  }

  /**
   * 重置每日锦标赛数据（管理接口）
   */
  @MessagePattern('tournament.resetDailyData')
  async resetDailyData(@Payload() payload: ResetDailyDataPayloadDto) {
    this.logger.log(`重置每日锦标赛数据: ${payload.characterId || 'all'}`);
    
    try {
      // 这里可以添加重置逻辑
      return {
        code: 0,
        message: '重置成功',
      };
    } catch (error) {
      this.logger.error('重置每日锦标赛数据异常', error);
      return {
        code: -1,
        message: '重置失败',
      };
    }
  }
}
