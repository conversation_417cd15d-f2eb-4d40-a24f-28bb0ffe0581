/**
 * Ranking模块的Payload DTO定义
 * 
 * 为ranking.controller.ts中的所有@MessagePattern接口生成对应的Payload DTO
 * 所有DTO都继承自BasePayloadDto，基于真实的接口数据结构
 * 包含详细的校验规则和@Expose()装饰器
 */

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsNumber, IsOptional } from 'class-validator';
import { Expose } from 'class-transformer';
import { BasePayloadDto } from '@libs/common/dto/base-payload.dto';

// ==================== 1. 全球排名相关 ====================

/**
 * 获取全球排名Payload DTO
 * @MessagePattern('ranking.getGlobalRanking')
 * 扩展GetGlobalRankingDto，合并BasePayloadDto内容
 */
export class GetGlobalRankingPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '排名类型', example: 'fans', enum: ['fans', 'strength', 'level'] })
  @Expose()
  @IsString({ message: '排名类型必须是字符串' })
  rankType: string;

  @ApiPropertyOptional({ description: '获取数量限制（可选）', example: 100 })
  @Expose()
  @IsOptional()
  @IsNumber({}, { message: '获取数量限制必须是数字' })
  limit?: number;

  @ApiPropertyOptional({ description: '偏移量（可选）', example: 0 })
  @Expose()
  @IsOptional()
  @IsNumber({}, { message: '偏移量必须是数字' })
  offset?: number;

  @ApiPropertyOptional({ description: '服务器ID（可选）', example: 'server_001' })
  @Expose()
  @IsOptional()
  @IsString({ message: '服务器ID必须是字符串' })
  serverId?: string;
}

// ==================== 2. 玩家排名相关 ====================

/**
 * 获取玩家排名信息Payload DTO
 * @MessagePattern('ranking.getCharacterRanking')
 * 扩展GetCharacterRankingDto，合并BasePayloadDto内容
 */
export class GetCharacterRankingPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  characterId: string;

  @ApiPropertyOptional({ description: '服务器ID（可选）', example: 'server_001' })
  @Expose()
  @IsOptional()
  @IsString({ message: '服务器ID必须是字符串' })
  serverId?: string;
}

// ==================== 3. 排名奖励相关 ====================

/**
 * 领取排名奖励Payload DTO
 * @MessagePattern('ranking.claimRankingReward')
 * 扩展ClaimRankingRewardDto，合并BasePayloadDto内容
 */
export class ClaimRankingRewardPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  characterId: string;

  @ApiProperty({ description: '排名类型', example: 'fans', enum: ['fans', 'strength', 'level'] })
  @Expose()
  @IsString({ message: '排名类型必须是字符串' })
  rankType: string;

  @ApiProperty({ description: '赛季', example: 1 })
  @Expose()
  @IsNumber({}, { message: '赛季必须是数字' })
  season: number;

  @ApiPropertyOptional({ description: '服务器ID（可选）', example: 'server_001' })
  @Expose()
  @IsOptional()
  @IsString({ message: '服务器ID必须是字符串' })
  serverId?: string;
}

// ==================== 4. 排名管理相关 ====================

/**
 * 更新排名数据Payload DTO
 * @MessagePattern('ranking.updateRanking')
 * 扩展UpdateRankingDto，合并BasePayloadDto内容
 */
export class UpdateRankingPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '排名类型', example: 'fans', enum: ['fans', 'strength', 'level'] })
  @Expose()
  @IsString({ message: '排名类型必须是字符串' })
  rankType: string;

  @ApiPropertyOptional({ description: '赛季（可选）', example: 1 })
  @Expose()
  @IsOptional()
  @IsNumber({}, { message: '赛季必须是数字' })
  season?: number;

  @ApiPropertyOptional({ description: '服务器ID（可选）', example: 'server_001' })
  @Expose()
  @IsOptional()
  @IsString({ message: '服务器ID必须是字符串' })
  serverId?: string;
}

// ==================== 5. 统计信息相关 ====================

/**
 * 获取排名统计信息Payload DTO
 * @MessagePattern('ranking.getStatistics')
 */
export class GetStatisticsPayloadDto extends BasePayloadDto {
  // 仅继承BasePayloadDto，无需额外字段
}
