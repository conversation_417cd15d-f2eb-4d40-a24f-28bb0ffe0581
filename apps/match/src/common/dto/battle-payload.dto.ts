/**
 * Battle模块的Payload DTO定义
 * 
 * 为battle.controller.ts中的所有@MessagePattern接口生成对应的Payload DTO
 * 所有DTO都继承自BasePayloadDto，直接包含字段而不嵌套dto
 * 包含详细的校验规则和@Expose()装饰器
 */

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsNumber, IsOptional, IsEnum, IsArray, Min, Max, Length } from 'class-validator';
import { Expose } from 'class-transformer';
import { BasePayloadDto } from '@libs/common/dto/base-payload.dto';

// 战斗类型枚举
export enum BattleType {
  LEAGUE = 'league',           // 联赛
  TOURNAMENT = 'tournament',   // 锦标赛
  FRIENDLY = 'friendly',       // 友谊赛
  TRAINING = 'training',       // 训练赛
  CHALLENGE = 'challenge'      // 挑战赛
}

// PVE战斗类型枚举
export enum PveBattleType {
  STORY = 'story',             // 剧情模式
  CHALLENGE = 'challenge',     // 挑战模式
  TRAINING = 'training',       // 训练模式
  BOSS = 'boss'               // Boss战
}

// ==================== 1. PVE战斗相关 ====================

/**
 * PVE战斗Payload DTO
 * @MessagePattern('battle.pveBattle')
 * 扩展PveBattleDto，合并BasePayloadDto内容
 */
export class PveBattlePayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: 'PVE战斗类型', enum: PveBattleType, example: PveBattleType.STORY })
  @Expose()
  @IsEnum(PveBattleType, { message: 'PVE战斗类型必须是有效的枚举值' })
  battleType: PveBattleType;

  @ApiProperty({ description: '关卡ID', example: 'level_001' })
  @Expose()
  @IsString({ message: '关卡ID必须是字符串' })
  @Length(1, 50, { message: '关卡ID长度必须在1-50个字符之间' })
  levelId: string;

  @ApiProperty({ description: '阵容ID', example: 'formation_67890' })
  @Expose()
  @IsString({ message: '阵容ID必须是字符串' })
  @Length(1, 50, { message: '阵容ID长度必须在1-50个字符之间' })
  formationId: string;

  @ApiPropertyOptional({ description: '难度等级（可选）', example: 1 })
  @Expose()
  @IsOptional()
  @IsNumber({}, { message: '难度等级必须是数字' })
  @Min(1, { message: '难度等级不能小于1' })
  @Max(10, { message: '难度等级不能大于10' })
  difficulty?: number;

  @ApiPropertyOptional({ description: '是否自动战斗（可选）', example: false })
  @Expose()
  @IsOptional()
  autoBattle?: boolean;
}

// ==================== 2. PVP战斗相关 ====================

/**
 * PVP战斗Payload DTO
 * @MessagePattern('battle.pvpBattle')
 * 扩展PvpBattleDto，合并BasePayloadDto内容
 */
export class PvpBattlePayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '主场角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '主场角色ID必须是字符串' })
  @Length(1, 50, { message: '主场角色ID长度必须在1-50个字符之间' })
  homeCharacterId: string;

  @ApiProperty({ description: '客场角色ID', example: 'char_67890' })
  @Expose()
  @IsString({ message: '客场角色ID必须是字符串' })
  @Length(1, 50, { message: '客场角色ID长度必须在1-50个字符之间' })
  awayCharacterId: string;

  @ApiProperty({ description: '主场阵容ID', example: 'formation_111' })
  @Expose()
  @IsString({ message: '主场阵容ID必须是字符串' })
  @Length(1, 50, { message: '主场阵容ID长度必须在1-50个字符之间' })
  homeFormationId: string;

  @ApiProperty({ description: '客场阵容ID', example: 'formation_222' })
  @Expose()
  @IsString({ message: '客场阵容ID必须是字符串' })
  @Length(1, 50, { message: '客场阵容ID长度必须在1-50个字符之间' })
  awayFormationId: string;

  @ApiProperty({ description: '战斗类型', enum: BattleType, example: BattleType.LEAGUE })
  @Expose()
  @IsEnum(BattleType, { message: '战斗类型必须是有效的枚举值' })
  battleType: BattleType;

  @ApiPropertyOptional({ description: '比赛ID（可选）', example: 'match_001' })
  @Expose()
  @IsOptional()
  @IsString({ message: '比赛ID必须是字符串' })
  @Length(1, 50, { message: '比赛ID长度必须在1-50个字符之间' })
  matchId?: string;

  @ApiPropertyOptional({ description: '是否友谊赛（可选）', example: false })
  @Expose()
  @IsOptional()
  isFriendly?: boolean;
}

// ==================== 3. 战斗回放相关 ====================

/**
 * 获取战斗回放Payload DTO
 * @MessagePattern('battle.getBattleReplay')
 * 扩展GetBattleReplayDto，合并BasePayloadDto内容
 */
export class GetBattleReplayPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '房间唯一标识', example: 'room_abc123' })
  @Expose()
  @IsString({ message: '房间唯一标识必须是字符串' })
  @Length(1, 100, { message: '房间唯一标识长度必须在1-100个字符之间' })
  roomUid: string;

  @ApiPropertyOptional({ description: '请求者角色ID（可选）', example: 'char_12345' })
  @Expose()
  @IsOptional()
  @IsString({ message: '请求者角色ID必须是字符串' })
  @Length(1, 50, { message: '请求者角色ID长度必须在1-50个字符之间' })
  requesterId?: string;
}

/**
 * 删除战斗房间Payload DTO
 * @MessagePattern('battle.deleteBattleRoom')
 */
export class DeleteBattleRoomPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '房间唯一标识', example: 'room_abc123' })
  @Expose()
  @IsString({ message: '房间唯一标识必须是字符串' })
  @Length(1, 100, { message: '房间唯一标识长度必须在1-100个字符之间' })
  roomUid: string;
}

// ==================== 4. 战斗管理相关 ====================

/**
 * 获取战斗统计Payload DTO
 * @MessagePattern('battle.getStatistics')
 */
export class GetStatisticsPayloadDto extends BasePayloadDto {
  // 无需额外字段，仅继承BasePayloadDto
}

/**
 * 清理过期房间Payload DTO
 * @MessagePattern('battle.cleanExpiredRooms')
 */
export class CleanExpiredRoomsPayloadDto extends BasePayloadDto {
  // 无需额外字段，仅继承BasePayloadDto
}
