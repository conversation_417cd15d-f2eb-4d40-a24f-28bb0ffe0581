import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, ClientSession } from 'mongoose';
import { BusinessMatch, BusinessMatchRecord } from '../schemas/business.schema';
import { BaseRepository } from '@libs/common/repository/base-repository';
import { XResult, PaginationResult, XResultUtils } from '@libs/common/types/result.type';

/**
 * 商业赛数据访问层
 * 继承BaseRepository，提供统一的数据访问接口和Result模式错误处理
 *
 * 🎯 核心功能：
 * - 商业赛数据CRUD操作
 * - 匹配记录管理
 * - 战斗次数统计
 * - 商业赛统计分析
 * - 批量操作支持
 *
 * 🚀 性能优化：
 * - 使用BaseRepository的Lean查询优化
 * - 自动性能监控和慢查询检测
 * - 智能缓存策略
 * - 并行查询优化
 */
@Injectable()
export class BusinessRepository extends BaseRepository<BusinessMatch> {
  constructor(
    @InjectModel(BusinessMatch.name) businessMatchModel: Model<BusinessMatch>
  ) {
    super(businessMatchModel, 'BusinessRepository');
  }

  // ========== 业务特定方法 ==========

  /**
   * 根据玩家UID查找商业赛数据
   * 使用BaseRepository的findOne方法优化性能
   */
  async findByUid(uid: string): Promise<XResult<BusinessMatch | null>> {
    return this.findOne({ uid });
  }

  /**
   * 根据玩家UID查找商业赛数据（Lean查询优化版本）
   */
  async findByUidLean(uid: string): Promise<XResult<any | null>> {
    return this.findOneLean({ uid });
  }

  /**
   * 创建新的商业赛数据
   * 使用BaseRepository的createOne方法，自动应用Result模式和性能监控
   */
  async create(businessMatchData: Partial<BusinessMatch>): Promise<XResult<BusinessMatch>> {
    return this.createOne(businessMatchData);
  }

  /**
   * 更新商业赛数据
   * 使用BaseRepository的updateOne方法优化性能
   */
  async updateByUid(
    uid: string,
    updateData: Partial<BusinessMatch>,
    session?: ClientSession
  ): Promise<XResult<BusinessMatch | null>> {
    return this.updateOne(
      { uid },
      { ...updateData, lastUpdateTime: new Date() },
      session
    );
  }

  /**
   * 创建或更新商业赛数据
   * TODO 待审核
   * 使用BaseRepository的upsertOne方法优化性能
   */
  // async upsert(uid: string, businessMatchData: Partial<BusinessMatch>): Promise<XResult<BusinessMatch>> {
  //   return this.upsertOne(
  //     { uid },
  //     { ...businessMatchData, lastUpdateTime: new Date() }
  //   );
  // }

  /**
   * 添加匹配记录
   * 使用BaseRepository的事务支持确保数据一致性
   */
  async addMatchRecord(uid: string, matchRecord: BusinessMatchRecord): Promise<XResult<BusinessMatch | null>> {
    return this.withTransaction(async (session) => {
      const businessMatchResult = await this.findByUid(uid);
      if (XResultUtils.isFailure(businessMatchResult) || !businessMatchResult.data) {
        return XResultUtils.error(`商业赛数据不存在: ${uid}`, 'BUSINESS_MATCH_NOT_FOUND');
      }

      const businessMatch = businessMatchResult.data;

      // 限制最大记录数量为30条
      if (businessMatch.matchRecordList.length >= businessMatch.maxRecordNum) {
        const removeCount = businessMatch.matchRecordList.length - businessMatch.maxRecordNum + 1;
        businessMatch.matchRecordList.splice(0, removeCount);
      }

      // 添加新记录
      businessMatch.matchRecordList.push(matchRecord);

      // 按时间排序（最新的在前）
      businessMatch.matchRecordList.sort((a, b) => {
        return new Date(b.beginTime).getTime() - new Date(a.beginTime).getTime();
      });

      return await this.updateByUid(uid, { matchRecordList: businessMatch.matchRecordList }, session);
    });
  }

  /**
   * 根据房间UID查找匹配记录
   * 使用BaseRepository的findOne方法优化性能
   */
  async findMatchRecordByRoomUid(uid: string, roomUid: string): Promise<XResult<BusinessMatchRecord | null>> {
    const businessMatchResult = await this.findByUidLean(uid);
    if (XResultUtils.isFailure(businessMatchResult) || !businessMatchResult.data) {
      return XResultUtils.ok(null);
    }

    const businessMatch = businessMatchResult.data;
    const matchRecord = businessMatch.matchRecordList?.find(record => record.roomUid === roomUid) || null;
    return XResultUtils.ok(matchRecord);
  }

  /**
   * 获取最近的匹配记录
   * 使用BaseRepository的findOne方法优化性能
   */
  async getLastMatchRecords(uid: string, limit: number = 5): Promise<XResult<BusinessMatchRecord[]>> {
    const businessMatchResult = await this.findByUidLean(uid);
    if (XResultUtils.isFailure(businessMatchResult) || !businessMatchResult.data) {
      return XResultUtils.ok([]);
    }

    const businessMatch = businessMatchResult.data;
    const records = businessMatch.matchRecordList?.slice(0, limit) || [];
    return XResultUtils.ok(records);
  }

  /**
   * 更新战斗次数
   * 使用BaseRepository的updateOne方法优化性能
   */
  async updateFightTimes(uid: string, fightTimes: any): Promise<XResult<BusinessMatch | null>> {
    return this.updateByUid(uid, { fightTimes });
  }

  /**
   * 删除商业赛数据
   * 使用BaseRepository的deleteOne方法优化性能
   */
  async deleteByUid(uid: string): Promise<XResult<boolean>> {
    const result = await this.deleteOne({ uid });
    if (XResultUtils.isSuccess(result)) {
      return XResultUtils.ok(result.data !== null);
    }
    return XResultUtils.ok(false);
  }

  /**
   * 批量查询商业赛数据
   * 使用BaseRepository的findMany方法优化性能
   */
  async findByUids(uids: string[]): Promise<XResult<BusinessMatch[]>> {
    return this.findMany({ uid: { $in: uids } });
  }

  /**
   * 批量查询商业赛数据（Lean查询优化版本）
   */
  async findByUidsLean(uids: string[]): Promise<XResult<any[]>> {
    return this.findManyLean({ uid: { $in: uids } }, {
      select: ['uid', 'fightTimes', 'lastUpdateTime', 'matchRecordList']
    });
  }

  /**
   * 获取商业赛统计信息
   * 使用BaseRepository的count和aggregate方法优化性能
   */
  async getStatistics(): Promise<XResult<any>> {
    const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);

    // 并行执行统计查询
    const [totalResult, activeResult, matchStatsResult] = await Promise.all([
      this.count({}),
      this.count({
        lastUpdateTime: { $gte: oneDayAgo }
      }),
      this.aggregate([
        { $unwind: '$matchRecordList' },
        { $group: { _id: null, totalMatches: { $sum: 1 } } }
      ])
    ]);

    // 检查查询结果
    if (XResultUtils.isFailure(totalResult) ||
        XResultUtils.isFailure(activeResult) ||
        XResultUtils.isFailure(matchStatsResult)) {
      return XResultUtils.error('获取商业赛统计信息失败', 'STATISTICS_QUERY_FAILED');
    }

    const totalMatches = matchStatsResult.data.length > 0 ? matchStatsResult.data[0].totalMatches : 0;

    return XResultUtils.ok({
      totalCharacters: totalResult.data,
      activeToday: activeResult.data,
      totalMatches,
      timestamp: new Date(),
    });
  }

  // ========== 重写BaseRepository方法以添加业务特定逻辑 ==========

  /**
   * 重写数据验证方法，添加商业赛特定的验证规则
   */
  protected validateData(data: Partial<BusinessMatch>, operation: 'create' | 'update'): XResult<void> {
    if (operation === 'create') {
      if (!data.uid) {
        return XResultUtils.error('玩家UID不能为空', 'UID_REQUIRED');
      }

      if (!data.maxRecordNum || data.maxRecordNum <= 0) {
        return XResultUtils.error('最大记录数必须大于0', 'INVALID_MAX_RECORD_NUM');
      }
    }

    if (data.maxRecordNum !== undefined && (data.maxRecordNum < 1 || data.maxRecordNum > 100)) {
      return XResultUtils.error('最大记录数必须在1-100之间', 'INVALID_MAX_RECORD_NUM');
    }

    return XResultUtils.ok(undefined);
  }

  /**
   * 重写缓存TTL配置，针对商业赛数据的访问模式优化
   */
  protected getCacheTTL(operation: string): number {
    const customTTLs: Record<string, number> = {
      'findByUid': 300,                  // 商业赛数据缓存5分钟
      'findByUidLean': 180,              // 商业赛简介缓存3分钟
      'getLastMatchRecords': 120,        // 最近记录缓存2分钟
      'findMatchRecordByRoomUid': 300,   // 匹配记录缓存5分钟
      'getStatistics': 60,               // 统计信息缓存1分钟
      'findByUids': 180,                 // 批量查询缓存3分钟
    };

    return customTTLs[operation] || super.getCacheTTL(operation);
  }
}
