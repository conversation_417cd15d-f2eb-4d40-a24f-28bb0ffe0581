import {Controller, Post, Get, Delete, Body, Param, ValidationPipe, UsePipes} from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { XResult, XResponse } from '@libs/common/types';
import { MicroserviceValidationPipe, StandardMicroserviceValidationPipe, ValidateInjectedContextPipe, SkipInjectedContextPipe } from '@libs/common/pipes';
import { TransactionTestService, TransferResult, ComplexPurchaseResult } from '../services/transaction-test.service';
import { TestAccountDocument } from '../schemas/test-account.schema';
import { TestQuestProgressDocument } from '../schemas/test-quest.schema';
import {TestItemDocument} from "../schemas/test-item.schema";
import {PerformanceComparisonService} from "../services/performance-comparison.service";
import {CreateTestAccountsDto, ComplexValidationTestDto} from "../dto/transaction.dto";
import { UpdateCharacterDto, CreateCharacterDto, UpgradeEquipmentDto, BatchOperationDto } from '../dto/complex-payload.dto';
import { PayloadValidation } from '../../../../libs/common/src/pipes/complex-microservice-validation.pipe';
import { UpdateCharacterPayloadDto, CreateCharacterPayloadDto, UpgradeEquipmentPayloadDto, CustomFieldsPayloadDto } from '../dto/base-payload-example.dto';

@Controller('transaction-test')
export class TransactionTestController {
  constructor(
    private readonly transactionTestService: TransactionTestService,
    private readonly performanceComparisonService: PerformanceComparisonService
  ) {}

  /**
   * 测试成功的转账事务
   */
  @Post('successful-transfer')
  async testSuccessfulTransfer(@Body() body: {
    fromUsername: string;
    toUsername: string;
    amount: number;
  }): Promise<XResult<TransferResult>> {
    return await this.transactionTestService.testSuccessfulTransfer(
      body.fromUsername,
      body.toUsername,
      body.amount
    );
  }

  /**
   * 测试失败的转账事务
   */
  @Post('failed-transfer')
  async testFailedTransfer(@Body() body: {
    fromUsername: string;
    toUsername: string;
    amount: number;
  }): Promise<XResult<TransferResult>> {
    return await this.transactionTestService.testFailedTransfer(
      body.fromUsername,
      body.toUsername,
      body.amount
    );
  }

  /**
   * 创建测试账户
   */
  @Post('create-accounts')
  async createTestAccounts(@Body(ValidationPipe) body: CreateTestAccountsDto): Promise<XResult<TestAccountDocument[]>> {
    return await this.transactionTestService.createTestAccounts(
      body.usernameA,
      body.usernameB
    );
  }

  /**
   * 查询账户余额
   */
  @Get('balance/:username')
  async getAccountBalance(@Param('username') username: string): Promise<XResult<{ username: string; balance: number }>> {
    return await this.transactionTestService.getAccountBalance(username);
  }

  /**
   * 查询账户道具
   */
  @Get('item/:username/:itemId')
  async getAccountItem(
    @Param('username') username: string,
    @Param('itemId') itemId: string
  ): Promise<XResult<TestItemDocument>> {
    return await this.transactionTestService.getAccountItem(username,itemId);
  }

  @Get('items/:username')
  async getAccountItems(
    @Param('username') username: string
  ): Promise<XResult<TestItemDocument[]>> {
    return await this.transactionTestService.getAccountItems(username);
  }

  /**
   * 查询任务进度
   */
  @Get('quest/:username/:questId')
  async getQuestProgress(
    @Param('username') username: string,
    @Param('questId') questId: string
  ): Promise<XResult<TestQuestProgressDocument | null>> {
    return await this.transactionTestService.getAccountQuest(username, questId);
  }

  /**
   * 复杂事务回滚测试
   */
  @Post('complex-rollback-test')
  async testComplexTransactionRollback(@Body() body: {
    fromUsername: string;
    toUsername: string;
    amount: number;
  }): Promise<XResult<{ message: string; rollbackVerified: boolean }>> {
    return await this.transactionTestService.testComplexTransactionRollback(
      body.fromUsername,
      body.toUsername,
      body.amount
    );
  }

  /**
   * 初始化购买任务
   */
  @Post('init-purchase-quest')
  async initializePurchaseQuest(@Body() body: {
    username: string;
    questId?: string;
    targetPurchases?: number;
  }): Promise<XResult<TestQuestProgressDocument>> {
    return await this.transactionTestService.initializePurchaseQuest(
      body.username,
      body.questId,
      body.targetPurchases
    );
  }

  /**
   * 复杂购买道具事务测试
   */
  @Post('complex-purchase')
  async testComplexItemPurchase(@Body() body: {
    username: string;
    itemId: string;
    itemName: string;
    itemPrice: number;
    questId?: string;
  }): Promise<XResult<ComplexPurchaseResult>> {
    return await this.transactionTestService.testComplexItemPurchase(
      body.username,
      body.itemId,
      body.itemName,
      body.itemPrice,
      body.questId
    );
  }

  /**
   * 故意失败的复杂购买事务测试
   */
  @Post('failed-complex-purchase')
  async testFailedComplexPurchase(@Body() body: {
    username: string;
    itemId: string;
    itemName: string;
    itemPrice: number;
  }): Promise<XResult<ComplexPurchaseResult>> {
    return await this.transactionTestService.testFailedComplexPurchase(
      body.username,
      body.itemId,
      body.itemName,
      body.itemPrice
    );
  }

  /**
   * 高性能查询测试
   */
  @Get('performance-queries/:username')
  async testHighPerformanceQueries(@Param('username') username: string): Promise<XResult<any>> {
    return await this.transactionTestService.testHighPerformanceQueries(username);
  }

  /**
   * 性能对比测试
   */
  @Get('performance-comparison/:username')
  async testPerformanceComparison(@Param('username') username: string): Promise<XResult<any>> {
    return await this.transactionTestService.testPerformanceComparison(username);
  }

  @Get('enhanced-performance-comparison/:username')
  async testEnhancedPerformanceComparison(@Param('username') username: string): Promise<XResult<any>> {
    return await this.performanceComparisonService.runFullPerformanceComparison(username);
  }

  /**
   * 清理测试数据
   */
  @Delete('cleanup')
  async cleanupTestData(): Promise<XResult<void>> {
    return await this.transactionTestService.cleanupTestData();
  }

  // ========== 微服务参数验证测试接口 ==========

  /**
   * 基础参数验证测试 - 使用@UsePipes装饰器
   * 测试标准的class-validator验证功能
   */
  @MessagePattern('validation.test.basic')
  @UsePipes(StandardMicroserviceValidationPipe)
  async testBasicValidation(@Payload() payload: ComplexValidationTestDto): Promise<XResponse<{
    message: string;
    validatedData: ComplexValidationTestDto;
    timestamp: number;
  }>> {
    console.log('🎯 [Controller] testBasicValidation 接收到请求');
    console.log('📥 [Controller] 原始payload:', JSON.stringify(payload, null, 2));
    console.log('🔍 [Controller] payload类型:', typeof payload);
    console.log('🔍 [Controller] payload构造函数:', payload?.constructor?.name);
    console.log('🔍 [Controller] payload是否为ComplexValidationTestDto实例:', payload instanceof ComplexValidationTestDto);

    const result = {
      code: 0,
      message: '基础参数验证测试通过',
      data: {
        message: '参数验证成功！所有字段都通过了验证',
        validatedData: payload,
        timestamp: Date.now(),
      },
      timestamp: Date.now(),
    };

    console.log('📤 [Controller] 返回结果:', JSON.stringify(result, null, 2));
    return result;
  }

  /**
   * 严格参数验证测试 - 使用自定义验证管道
   * 测试严格模式下的验证行为
   */
  @MessagePattern('validation.test.strict')
  @UsePipes(new MicroserviceValidationPipe({
    transform: true,
    whitelist: true,
    forbidNonWhitelisted: true,
    stopAtFirstError: true,
    skipMissingProperties: false,
  }))
  async testStrictValidation(@Payload() payload: ComplexValidationTestDto): Promise<XResponse<{
    message: string;
    validatedData: ComplexValidationTestDto;
    validationMode: string;
    timestamp: number;
  }>> {
    return {
      code: 0,
      message: '严格参数验证测试通过',
      data: {
        message: '严格参数验证成功！',
        validatedData: payload,
        validationMode: 'strict',
        timestamp: Date.now(),
      },
      timestamp: Date.now(),
    };
  }

  /**
   * 宽松参数验证测试 - 允许额外字段
   * 测试向后兼容性场景
   */
  @MessagePattern('validation.test.lenient')
  @UsePipes(new MicroserviceValidationPipe({
    transform: true,
    whitelist: false,
    forbidNonWhitelisted: false,
    stopAtFirstError: false,
    skipMissingProperties: true,
  }))
  async testLenientValidation(@Payload() payload: any): Promise<XResponse<{
    message: string;
    receivedData: any;
    validationMode: string;
    timestamp: number;
  }>> {
    return {
      code: 0,
      message: '宽松参数验证测试通过',
      data: {
        message: '宽松参数验证成功！允许额外字段和缺失的可选字段',
        receivedData: payload,
        validationMode: 'lenient',
        timestamp: Date.now(),
      },
      timestamp: Date.now(),
    };
  }

  /**
   * 无验证测试接口 - 用于对比
   * 展示没有验证管道时的行为
   */
  @MessagePattern('validation.test.none')
  async testNoValidation(@Payload() payload: any): Promise<XResponse<{
    message: string;
    rawData: any;
    dataType: string;
    timestamp: number;
  }>> {
    return {
      code: 0,
      message: '无验证测试完成',
      data: {
        message: '无验证模式 - 直接接收原始数据',
        rawData: payload,
        dataType: typeof payload,
        timestamp: Date.now(),
      },
      timestamp: Date.now(),
    };
  }

  // ==================== 复杂Payload验证测试接口 ====================

  /**
   * 测试复杂Payload验证 - 角色更新
   * payload: { characterId: string; updateDto: UpdateCharacterDto; serverId?: string; injectedContext?: InjectedContext }
   */
  @MessagePattern('complex.validation.character.update')
  @UsePipes(PayloadValidation.idDto('characterId', 'updateDto', UpdateCharacterDto))
  async testComplexCharacterUpdate(@Payload() payload: any): Promise<XResponse<any>> {
    console.log('🎯 [Controller] testComplexCharacterUpdate 接收到请求');
    console.log('📥 [Controller] 复杂payload:', JSON.stringify(payload, null, 2));
    console.log('🔍 [Controller] payload结构分析:', {
      hasCharacterId: 'characterId' in payload,
      hasUpdateDto: 'updateDto' in payload,
      hasServerId: 'serverId' in payload,
      hasInjectedContext: 'injectedContext' in payload,
      updateDtoType: typeof payload.updateDto,
      updateDtoKeys: payload.updateDto ? Object.keys(payload.updateDto) : [],
    });

    return {
      code: 0,
      message: '复杂Payload验证测试通过 - 角色更新',
      data: {
        message: '复杂payload验证成功！所有字段都通过了验证和转换',
        validatedPayload: payload,
        payloadStructure: {
          characterId: typeof payload.characterId,
          updateDto: {
            type: typeof payload.updateDto,
            keys: payload.updateDto ? Object.keys(payload.updateDto) : [],
            isInstance: payload.updateDto?.constructor?.name,
          },
          serverId: typeof payload.serverId,
          injectedContext: payload.injectedContext ? 'present (ignored)' : 'absent',
        },
      },
      timestamp: Date.now(),
    };
  }

  /**
   * 测试复杂Payload验证 - 角色创建
   * payload: { userId: string; createDto: CreateCharacterDto; serverId?: string; injectedContext?: InjectedContext }
   */
  @MessagePattern('complex.validation.character.create')
  @UsePipes(PayloadValidation.idDto('userId', 'createDto', CreateCharacterDto))
  async testComplexCharacterCreate(@Payload() payload: any): Promise<XResponse<any>> {
    console.log('🎯 [Controller] testComplexCharacterCreate 接收到请求');
    console.log('📥 [Controller] 创建角色payload:', JSON.stringify(payload, null, 2));

    return {
      code: 0,
      message: '复杂Payload验证测试通过 - 角色创建',
      data: {
        message: '角色创建payload验证成功！',
        validatedPayload: payload,
      },
      timestamp: Date.now(),
    };
  }

  /**
   * 测试复杂Payload验证 - 装备升级（双ID模式）
   * payload: { characterId: string; equipmentId: string; upgradeDto: UpgradeEquipmentDto; serverId?: string; injectedContext?: InjectedContext }
   */
  @MessagePattern('complex.validation.equipment.upgrade')
  @UsePipes(PayloadValidation.doubleIdDto('characterId', 'equipmentId', 'upgradeDto', UpgradeEquipmentDto))
  async testComplexEquipmentUpgrade(@Payload() payload: any): Promise<XResponse<any>> {
    console.log('🎯 [Controller] testComplexEquipmentUpgrade 接收到请求');
    console.log('📥 [Controller] 装备升级payload:', JSON.stringify(payload, null, 2));

    return {
      code: 0,
      message: '复杂Payload验证测试通过 - 装备升级',
      data: {
        message: '装备升级payload验证成功！',
        validatedPayload: payload,
      },
      timestamp: Date.now(),
    };
  }

  /**
   * 测试复杂Payload验证 - 批量操作
   * payload: { userId: string; batchDto: BatchOperationDto; serverId?: string; injectedContext?: InjectedContext }
   */
  @MessagePattern('complex.validation.batch.operation')
  @UsePipes(PayloadValidation.idDto('userId', 'batchDto', BatchOperationDto))
  async testComplexBatchOperation(@Payload() payload: any): Promise<XResponse<any>> {
    console.log('🎯 [Controller] testComplexBatchOperation 接收到请求');
    console.log('📥 [Controller] 批量操作payload:', JSON.stringify(payload, null, 2));

    return {
      code: 0,
      message: '复杂Payload验证测试通过 - 批量操作',
      data: {
        message: '批量操作payload验证成功！',
        validatedPayload: payload,
      },
      timestamp: Date.now(),
    };
  }

  /**
   * 测试复杂Payload验证 - 自定义字段配置
   * payload: { gameId: string; roomId: string; maxPlayers: number; isPrivate: boolean; injectedContext?: InjectedContext }
   */
  @MessagePattern('complex.validation.custom.fields')
  @UsePipes(PayloadValidation.fields({
    gameId: 'string:required:1-50',
    roomId: 'string:required:1-50',
    maxPlayers: 'number:required:1-100',
    isPrivate: 'boolean:required',
  }))
  async testComplexCustomFields(@Payload() payload: any): Promise<XResponse<any>> {
    console.log('🎯 [Controller] testComplexCustomFields 接收到请求');
    console.log('📥 [Controller] 自定义字段payload:', JSON.stringify(payload, null, 2));

    return {
      code: 0,
      message: '复杂Payload验证测试通过 - 自定义字段',
      data: {
        message: '自定义字段payload验证成功！',
        validatedPayload: payload,
      },
      timestamp: Date.now(),
    };
  }

  // ==================== BasePayloadDto测试接口 ====================

  /**
   * 测试BasePayloadDto - 角色更新（使用IdDtoPayloadDto基类）
   * 使用简单的StandardMicroserviceValidationPipe
   */
  @MessagePattern('base.payload.character.update')
  @UsePipes(StandardMicroserviceValidationPipe)
  async testBasePayloadCharacterUpdate(@Payload() payload: UpdateCharacterPayloadDto): Promise<XResponse<any>> {
    console.log('🎯 [Controller] testBasePayloadCharacterUpdate 接收到请求');
    console.log('📥 [Controller] BasePayload:', JSON.stringify(payload, null, 2));
    console.log('🔍 [Controller] payload结构分析:', {
      hasId: 'id' in payload,
      hasDto: 'dto' in payload,
      hasServerId: 'serverId' in payload,
      hasInjectedContext: 'injectedContext' in payload,
      payloadType: payload.constructor.name,
      dtoType: payload.dto?.constructor?.name,
    });

    return {
      code: 0,
      message: 'BasePayloadDto测试通过 - 角色更新',
      data: {
        message: 'BasePayloadDto验证成功！使用简单的StandardMicroserviceValidationPipe',
        characterId: payload.id, // 使用实际的属性名
        updateDto: payload.dto,  // 使用实际的属性名
        injectedContextPresent: !!payload.injectedContext,
        payloadStructure: {
          payloadType: payload.constructor.name,
          hasAllFields: !!(payload.id && payload.dto),
        },
      },
      timestamp: Date.now(),
    };
  }

  /**
   * 测试BasePayloadDto - 角色创建（使用DtoPayloadDto基类）
   */
  @MessagePattern('base.payload.character.create')
  @UsePipes(StandardMicroserviceValidationPipe)
  async testBasePayloadCharacterCreate(@Payload() payload: CreateCharacterPayloadDto): Promise<XResponse<any>> {
    console.log('🎯 [Controller] testBasePayloadCharacterCreate 接收到请求');
    console.log('📥 [Controller] 创建角色BasePayload:', JSON.stringify(payload, null, 2));

    return {
      code: 0,
      message: 'BasePayloadDto测试通过 - 角色创建',
      data: {
        message: '角色创建BasePayloadDto验证成功！',
        createDto: payload.dto, // 使用实际的属性名
        injectedContextPresent: !!payload.injectedContext,
      },
      timestamp: Date.now(),
    };
  }

  /**
   * 测试BasePayloadDto - 装备升级（使用DoubleIdDtoPayloadDto基类）
   */
  @MessagePattern('base.payload.equipment.upgrade')
  @UsePipes(StandardMicroserviceValidationPipe)
  async testBasePayloadEquipmentUpgrade(@Payload() payload: UpgradeEquipmentPayloadDto): Promise<XResponse<any>> {
    console.log('🎯 [Controller] testBasePayloadEquipmentUpgrade 接收到请求');
    console.log('📥 [Controller] 装备升级BasePayload:', JSON.stringify(payload, null, 2));

    return {
      code: 0,
      message: 'BasePayloadDto测试通过 - 装备升级',
      data: {
        message: '装备升级BasePayloadDto验证成功！',
        characterId: payload.id1, // 使用实际的属性名
        equipmentId: payload.id2,  // 使用实际的属性名
        upgradeDto: payload.dto,   // 使用实际的属性名
        injectedContextPresent: !!payload.injectedContext,
      },
      timestamp: Date.now(),
    };
  }

  /**
   * 测试BasePayloadDto - 自定义字段（直接继承BasePayloadDto）
   */
  @MessagePattern('base.payload.custom.fields')
  @UsePipes(StandardMicroserviceValidationPipe)
  async testBasePayloadCustomFields(@Payload() payload: CustomFieldsPayloadDto): Promise<XResponse<any>> {
    console.log('🎯 [Controller] testBasePayloadCustomFields 接收到请求');
    console.log('📥 [Controller] 自定义字段BasePayload:', JSON.stringify(payload, null, 2));

    return {
      code: 0,
      message: 'BasePayloadDto测试通过 - 自定义字段',
      data: {
        message: '自定义字段BasePayloadDto验证成功！',
        gameId: payload.gameId,
        roomId: payload.roomId,
        maxPlayers: payload.maxPlayers,
        isPrivate: payload.isPrivate,
        injectedContextPresent: !!payload.injectedContext,
      },
      timestamp: Date.now(),
    };
  }

  // ==================== injectedContext配置测试接口 ====================

  /**
   * 测试验证injectedContext - 启用验证
   * 使用ValidateInjectedContextPipe，会验证injectedContext字段
   */
  @MessagePattern('injected.context.validate.enabled')
  @UsePipes(ValidateInjectedContextPipe)
  async testValidateInjectedContextEnabled(@Payload() payload: UpdateCharacterPayloadDto): Promise<XResponse<any>> {
    console.log('🎯 [Controller] testValidateInjectedContextEnabled 接收到请求');
    console.log('📥 [Controller] 启用injectedContext验证:', JSON.stringify(payload, null, 2));

    return {
      code: 0,
      message: 'injectedContext验证测试 - 启用验证',
      data: {
        message: 'injectedContext字段被验证了',
        characterId: payload.id, // 使用实际的属性名
        updateDto: payload.dto,  // 使用实际的属性名
        injectedContext: payload.injectedContext,
        injectedContextType: typeof payload.injectedContext,
        injectedContextKeys: payload.injectedContext ? Object.keys(payload.injectedContext) : [],
      },
      timestamp: Date.now(),
    };
  }

  /**
   * 测试跳过injectedContext验证 - 禁用验证
   * 使用SkipInjectedContextPipe，会跳过injectedContext字段验证
   */
  @MessagePattern('injected.context.validate.disabled')
  @UsePipes(SkipInjectedContextPipe)
  async testValidateInjectedContextDisabled(@Payload() payload: UpdateCharacterPayloadDto): Promise<XResponse<any>> {
    console.log('🎯 [Controller] testValidateInjectedContextDisabled 接收到请求');
    console.log('📥 [Controller] 跳过injectedContext验证:', JSON.stringify(payload, null, 2));

    return {
      code: 0,
      message: 'injectedContext验证测试 - 跳过验证',
      data: {
        message: 'injectedContext字段被跳过验证，但数据完整保留',
        characterId: payload.id, // 使用实际的属性名
        updateDto: payload.dto,  // 使用实际的属性名
        injectedContext: payload.injectedContext,
        injectedContextType: typeof payload.injectedContext,
        injectedContextKeys: payload.injectedContext ? Object.keys(payload.injectedContext) : [],
        injectedContextPreserved: !!payload.injectedContext,
      },
      timestamp: Date.now(),
    };
  }

  /**
   * 测试默认行为 - StandardMicroserviceValidationPipe
   * 默认跳过injectedContext验证
   */
  @MessagePattern('injected.context.validate.default')
  @UsePipes(StandardMicroserviceValidationPipe)
  async testValidateInjectedContextDefault(@Payload() payload: UpdateCharacterPayloadDto): Promise<XResponse<any>> {
    console.log('🎯 [Controller] testValidateInjectedContextDefault 接收到请求');
    console.log('📥 [Controller] 默认injectedContext处理:', JSON.stringify(payload, null, 2));

    return {
      code: 0,
      message: 'injectedContext验证测试 - 默认行为',
      data: {
        message: 'StandardMicroserviceValidationPipe默认跳过injectedContext验证',
        characterId: payload.id, // 使用实际的属性名
        updateDto: payload.dto,  // 使用实际的属性名
        injectedContext: payload.injectedContext,
        injectedContextType: typeof payload.injectedContext,
        injectedContextKeys: payload.injectedContext ? Object.keys(payload.injectedContext) : [],
        injectedContextPreserved: !!payload.injectedContext,
      },
      timestamp: Date.now(),
    };
  }
}
