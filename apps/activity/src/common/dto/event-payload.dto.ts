/**
 * Event模块的Payload DTO定义
 * 
 * 为event.controller.ts中的所有@MessagePattern接口生成对应的Payload DTO
 * 所有DTO都继承自BasePayloadDto，基于真实的接口数据结构
 * 包含详细的校验规则和@Expose()装饰器
 */

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsNumber, IsOptional, IsEnum, Length, Min, Max } from 'class-validator';
import { Expose } from 'class-transformer';
import { BasePayloadDto } from '@libs/common/dto/base-payload.dto';
import { EventType } from '../schemas/event.schema';

// ==================== 1. 活动信息相关 ====================

/**
 * 获取活动列表Payload DTO
 * @MessagePattern('event.getList')
 * 基于真实接口结构：{ characterId: string; injectedContext?: InjectedContext }
 */
export class GetEventListPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;
}

// ==================== 2. 活动参与相关 ====================

/**
 * 加入活动Payload DTO
 * @MessagePattern('event.join')
 * 基于真实接口结构：{ characterId: string; eventId: number; injectedContext?: InjectedContext }
 */
export class JoinEventPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '活动ID', example: 1001 })
  @Expose()
  @IsNumber({}, { message: '活动ID必须是数字' })
  @Min(1, { message: '活动ID不能小于1' })
  @Max(999999, { message: '活动ID不能大于999999' })
  eventId: number;
}

// ==================== 3. 活动奖励相关 ====================

/**
 * 领取活动奖励Payload DTO
 * @MessagePattern('event.claimReward')
 * 基于真实接口结构：{ characterId: string; eventId: number; rewardId: number; injectedContext?: InjectedContext }
 */
export class ClaimEventRewardPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '活动ID', example: 1001 })
  @Expose()
  @IsNumber({}, { message: '活动ID必须是数字' })
  @Min(1, { message: '活动ID不能小于1' })
  @Max(999999, { message: '活动ID不能大于999999' })
  eventId: number;

  @ApiProperty({ description: '奖励ID', example: 2001 })
  @Expose()
  @IsNumber({}, { message: '奖励ID必须是数字' })
  @Min(1, { message: '奖励ID不能小于1' })
  @Max(999999, { message: '奖励ID不能大于999999' })
  rewardId: number;
}

/**
 * 获取活动进度Payload DTO
 * @MessagePattern('event.getProgress')
 * 基于真实接口结构：{ characterId: string; eventId: number; injectedContext?: InjectedContext }
 */
export class GetEventProgressPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '活动ID', example: 1001 })
  @Expose()
  @IsNumber({}, { message: '活动ID必须是数字' })
  @Min(1, { message: '活动ID不能小于1' })
  @Max(999999, { message: '活动ID不能大于999999' })
  eventId: number;
}

/**
 * 购买最佳足球Payload DTO
 * @MessagePattern('event.buyBestFootball')
 * 基于真实接口结构：{ characterId: string; index: number; serverId?: string; injectedContext?: InjectedContext }
 */
export class BuyBestFootballPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '索引', example: 1 })
  @Expose()
  @IsNumber({}, { message: '索引必须是数字' })
  @Min(0, { message: '索引不能小于0' })
  @Max(99, { message: '索引不能大于99' })
  index: number;

  @ApiPropertyOptional({ description: '服务器ID（可选）', example: 'server_001' })
  @Expose()
  @IsOptional()
  @IsString({ message: '服务器ID必须是字符串' })
  @Length(1, 50, { message: '服务器ID长度必须在1-50个字符之间' })
  serverId?: string;
}

/**
 * 购买转盘Payload DTO
 * @MessagePattern('event.buyTurntable')
 * 基于真实接口结构：{ characterId: string; frequencyType: number; injectedContext?: InjectedContext }
 */
export class BuyTurntablePayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '频率类型', example: 1 })
  @Expose()
  @IsNumber({}, { message: '频率类型必须是数字' })
  @Min(1, { message: '频率类型不能小于1' })
  @Max(10, { message: '频率类型不能大于10' })
  frequencyType: number;
}

/**
 * 购买老虎机Payload DTO
 * @MessagePattern('event.buySlots')
 * 基于真实接口结构：{ characterId: string; frequencyType: number; securityMoney: number; serverId?: string; injectedContext?: InjectedContext }
 */
export class BuySlotsPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '频率类型', example: 1 })
  @Expose()
  @IsNumber({}, { message: '频率类型必须是数字' })
  @Min(1, { message: '频率类型不能小于1' })
  @Max(10, { message: '频率类型不能大于10' })
  frequencyType: number;

  @ApiProperty({ description: '保证金', example: 1000 })
  @Expose()
  @IsNumber({}, { message: '保证金必须是数字' })
  @Min(1, { message: '保证金不能小于1' })
  @Max(999999999, { message: '保证金不能大于999999999' })
  securityMoney: number;

  @ApiPropertyOptional({ description: '服务器ID（可选）', example: 'server_001' })
  @Expose()
  @IsOptional()
  @IsString({ message: '服务器ID必须是字符串' })
  @Length(1, 50, { message: '服务器ID长度必须在1-50个字符之间' })
  serverId?: string;
}

/**
 * 工作日安可Payload DTO
 * @MessagePattern('event.weekDayEncore')
 * 基于真实接口结构：{ characterId: string; serverId?: string; injectedContext?: InjectedContext }
 */
export class WeekDayEncorePayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiPropertyOptional({ description: '服务器ID（可选）', example: 'server_001' })
  @Expose()
  @IsOptional()
  @IsString({ message: '服务器ID必须是字符串' })
  @Length(1, 50, { message: '服务器ID长度必须在1-50个字符之间' })
  serverId?: string;
}

// ==================== 4. 活动进度相关 ====================

/**
 * 更新活动进度Payload DTO
 * @MessagePattern('event.updateProgress')
 * 基于真实接口结构：{ uid: string; serverId: string; eventId: number; progress: number; injectedContext?: InjectedContext }
 */
export class UpdateEventProgressPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '用户ID', example: 'user_12345' })
  @Expose()
  @IsString({ message: '用户ID必须是字符串' })
  @Length(1, 50, { message: '用户ID长度必须在1-50个字符之间' })
  uid: string;

  @ApiProperty({ description: '服务器ID', example: 'server_001' })
  @Expose()
  @IsString({ message: '服务器ID必须是字符串' })
  @Length(1, 50, { message: '服务器ID长度必须在1-50个字符之间' })
  serverId: string;

  @ApiProperty({ description: '活动ID', example: 1001 })
  @Expose()
  @IsNumber({}, { message: '活动ID必须是数字' })
  @Min(1, { message: '活动ID不能小于1' })
  @Max(999999, { message: '活动ID不能大于999999' })
  eventId: number;

  @ApiProperty({ description: '进度值', example: 50 })
  @Expose()
  @IsNumber({}, { message: '进度值必须是数字' })
  @Min(0, { message: '进度值不能小于0' })
  @Max(999999, { message: '进度值不能大于999999' })
  progress: number;
}

// ==================== 5. 统计信息相关 ====================

/**
 * 获取活动统计信息Payload DTO
 * @MessagePattern('event.getStats')
 * 基于真实接口结构：{ adminToken?: string; injectedContext?: InjectedContext }
 */
export class GetEventStatsPayloadDto extends BasePayloadDto {
  @ApiPropertyOptional({ description: '管理员令牌', example: 'admin_token_123' })
  @Expose()
  @IsOptional()
  @IsString({ message: '管理员令牌必须是字符串' })
  adminToken?: string;
}
