/**
 * Task模块的Payload DTO定义
 * 
 * 为task.controller.ts中的所有@MessagePattern接口生成对应的Payload DTO
 * 所有DTO都继承自BasePayloadDto，基于真实的接口数据结构
 * 包含详细的校验规则和@Expose()装饰器
 */

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsNumber, IsOptional, IsEnum, IsArray, IsObject, ValidateNested, Length, Min, Max } from 'class-validator';
import { Expose, Type } from 'class-transformer';
import { BasePayloadDto } from '@libs/common/dto/base-payload.dto';
import { TaskType } from '../schemas/task.schema';

// ==================== 1. 任务列表相关 ====================

/**
 * 获取任务列表Payload DTO
 * @MessagePattern('task.getList')
 * 基于真实接口结构：{ characterId: string; serverId: string; injectedContext?: InjectedContext }
 */
export class GetTaskListPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '服务器ID', example: 'server_001' })
  @Expose()
  @IsString({ message: '服务器ID必须是字符串' })
  @Length(1, 50, { message: '服务器ID长度必须在1-50个字符之间' })
  serverId: string;
}

// ==================== 2. 任务进度相关 ====================

/**
 * 更新任务进度Payload DTO
 * @MessagePattern('task.updateProgress')
 * 基于真实接口结构：{ characterId: string; serverId: string; taskId: number; progress: number; injectedContext?: InjectedContext }
 */
export class UpdateTaskProgressPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '服务器ID', example: 'server_001' })
  @Expose()
  @IsString({ message: '服务器ID必须是字符串' })
  @Length(1, 50, { message: '服务器ID长度必须在1-50个字符之间' })
  serverId: string;

  @ApiProperty({ description: '任务ID', example: 1001 })
  @Expose()
  @IsNumber({}, { message: '任务ID必须是数字' })
  @Min(1, { message: '任务ID不能小于1' })
  @Max(999999, { message: '任务ID不能大于999999' })
  taskId: number;

  @ApiProperty({ description: '进度值', example: 50 })
  @Expose()
  @IsNumber({}, { message: '进度值必须是数字' })
  @Min(0, { message: '进度值不能小于0' })
  @Max(999999, { message: '进度值不能大于999999' })
  progress: number;
}

// ==================== 3. 任务奖励相关 ====================

/**
 * 领取任务奖励Payload DTO
 * @MessagePattern('task.claimReward')
 * 基于真实接口结构：{ characterId: string; serverId: string; taskId: number; injectedContext?: InjectedContext }
 */
export class ClaimTaskRewardPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '服务器ID', example: 'server_001' })
  @Expose()
  @IsString({ message: '服务器ID必须是字符串' })
  @Length(1, 50, { message: '服务器ID长度必须在1-50个字符之间' })
  serverId: string;

  @ApiProperty({ description: '任务ID', example: 1001 })
  @Expose()
  @IsNumber({}, { message: '任务ID必须是数字' })
  @Min(1, { message: '任务ID不能小于1' })
  @Max(999999, { message: '任务ID不能大于999999' })
  taskId: number;
}

/**
 * 批量领取奖励Payload DTO
 * @MessagePattern('task.batchClaimRewards')
 * 基于真实接口结构：{ characterId: string; serverId: string; taskIds: number[]; injectedContext?: InjectedContext }
 */
export class BatchClaimRewardsPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '服务器ID', example: 'server_001' })
  @Expose()
  @IsString({ message: '服务器ID必须是字符串' })
  @Length(1, 50, { message: '服务器ID长度必须在1-50个字符之间' })
  serverId: string;

  @ApiProperty({ description: '任务ID列表', type: [Number], example: [1001, 1002, 1003] })
  @Expose()
  @IsArray({ message: '任务ID列表必须是数组' })
  @IsNumber({}, { each: true, message: '任务ID必须是数字' })
  @Min(1, { each: true, message: '任务ID不能小于1' })
  @Max(999999, { each: true, message: '任务ID不能大于999999' })
  taskIds: number[];
}

// ==================== 4. 任务刷新相关 ====================

/**
 * 刷新任务Payload DTO
 * @MessagePattern('task.refresh')
 * 基于真实接口结构：{ characterId: string; serverId: string; taskType: TaskType; injectedContext?: InjectedContext }
 */
export class RefreshTaskPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '服务器ID', example: 'server_001' })
  @Expose()
  @IsString({ message: '服务器ID必须是字符串' })
  @Length(1, 50, { message: '服务器ID长度必须在1-50个字符之间' })
  serverId: string;

  @ApiProperty({ description: '任务类型', enum: TaskType, example: TaskType.DAILY })
  @Expose()
  @IsEnum(TaskType, { message: '任务类型必须是有效的枚举值' })
  taskType: TaskType;
}

/**
 * 批量刷新任务Payload DTO
 * @MessagePattern('task.batchRefresh')
 * 基于真实接口结构：{ taskType: TaskType; injectedContext?: InjectedContext }
 */
export class BatchRefreshTasksPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '任务类型', enum: TaskType, example: TaskType.DAILY })
  @Expose()
  @IsEnum(TaskType, { message: '任务类型必须是有效的枚举值' })
  taskType: TaskType;
}

// ==================== 5. 任务管理相关 ====================

/**
 * 清理过期任务Payload DTO
 * @MessagePattern('task.cleanExpired')
 * 基于真实接口结构：{ injectedContext?: InjectedContext }
 */
export class CleanExpiredTasksPayloadDto extends BasePayloadDto {
  // 仅继承BasePayloadDto，无需额外字段
}

/**
 * 删除任务Payload DTO
 * @MessagePattern('task.delete')
 * 基于真实接口结构：{ characterId: string; serverId: string; taskType: TaskType; taskId: number; injectedContext?: InjectedContext }
 */
export class DeleteTaskPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '服务器ID', example: 'server_001' })
  @Expose()
  @IsString({ message: '服务器ID必须是字符串' })
  @Length(1, 50, { message: '服务器ID长度必须在1-50个字符之间' })
  serverId: string;

  @ApiProperty({ description: '任务类型', enum: TaskType, example: TaskType.DAILY })
  @Expose()
  @IsEnum(TaskType, { message: '任务类型必须是有效的枚举值' })
  taskType: TaskType;

  @ApiProperty({ description: '任务ID', example: 1001 })
  @Expose()
  @IsNumber({}, { message: '任务ID必须是数字' })
  @Min(1, { message: '任务ID不能小于1' })
  @Max(999999, { message: '任务ID不能大于999999' })
  taskId: number;
}
