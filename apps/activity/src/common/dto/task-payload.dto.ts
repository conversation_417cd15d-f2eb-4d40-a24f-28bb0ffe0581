/**
 * Task模块的Payload DTO定义
 * 
 * 为task.controller.ts中的所有@MessagePattern接口生成对应的Payload DTO
 * 所有DTO都继承自BasePayloadDto，基于真实的接口数据结构
 * 包含详细的校验规则和@Expose()装饰器
 */

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsNumber, IsOptional, IsEnum, IsArray, IsObject, ValidateNested, Length, Min, Max } from 'class-validator';
import { Expose, Type } from 'class-transformer';
import { BasePayloadDto } from '@libs/common/dto/base-payload.dto';
import { TaskType } from '../schemas/task.schema';

// ==================== 1. 任务列表相关 ====================

/**
 * 获取任务列表Payload DTO
 * @MessagePattern('task.getList')
 * 基于真实接口结构：{ characterId: string; serverId: string; injectedContext?: InjectedContext }
 */
export class GetTaskListPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '服务器ID', example: 'server_001' })
  @Expose()
  @IsString({ message: '服务器ID必须是字符串' })
  @Length(1, 50, { message: '服务器ID长度必须在1-50个字符之间' })
  serverId: string;
}

// ==================== 2. 任务进度相关 ====================

/**
 * 更新任务进度Payload DTO
 * @MessagePattern('task.updateProgress')
 * 基于真实接口结构：{ characterId: string; serverId: string; taskId: number; progress: number; injectedContext?: InjectedContext }
 */
export class UpdateTaskProgressPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '服务器ID', example: 'server_001' })
  @Expose()
  @IsString({ message: '服务器ID必须是字符串' })
  @Length(1, 50, { message: '服务器ID长度必须在1-50个字符之间' })
  serverId: string;

  @ApiProperty({ description: '任务ID', example: 1001 })
  @Expose()
  @IsNumber({}, { message: '任务ID必须是数字' })
  @Min(1, { message: '任务ID不能小于1' })
  @Max(999999, { message: '任务ID不能大于999999' })
  taskId: number;

  @ApiProperty({ description: '进度值', example: 50 })
  @Expose()
  @IsNumber({}, { message: '进度值必须是数字' })
  @Min(0, { message: '进度值不能小于0' })
  @Max(999999, { message: '进度值不能大于999999' })
  progress: number;
}

/**
 * 批量更新任务进度Payload DTO
 * @MessagePattern('task.batchUpdateProgress')
 * 基于真实接口结构：{ characterId: string; serverId: string; batchDto: BatchUpdateTaskProgressDto; injectedContext?: InjectedContext }
 */
export class BatchUpdateTaskProgressPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '服务器ID', example: 'server_001' })
  @Expose()
  @IsString({ message: '服务器ID必须是字符串' })
  @Length(1, 50, { message: '服务器ID长度必须在1-50个字符之间' })
  serverId: string;

  @ApiProperty({
    description: '批量更新数据',
    type: 'object',
    example: {
      updates: [
        { taskId: 1001, progress: 50 },
        { taskId: 1002, progress: 100 }
      ]
    }
  })
  @Expose()
  @IsObject({ message: '批量更新数据必须是对象' })
  batchDto: {
    updates: Array<{
      taskId: number;
      progress: number;
    }>;
  };
}

// ==================== 3. 任务奖励相关 ====================

/**
 * 领取任务奖励Payload DTO
 * @MessagePattern('task.claimReward')
 * 基于真实接口结构：{ characterId: string; serverId: string; taskId: number; injectedContext?: InjectedContext }
 */
export class ClaimTaskRewardPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '服务器ID', example: 'server_001' })
  @Expose()
  @IsString({ message: '服务器ID必须是字符串' })
  @Length(1, 50, { message: '服务器ID长度必须在1-50个字符之间' })
  serverId: string;

  @ApiProperty({ description: '任务ID', example: 1001 })
  @Expose()
  @IsNumber({}, { message: '任务ID必须是数字' })
  @Min(1, { message: '任务ID不能小于1' })
  @Max(999999, { message: '任务ID不能大于999999' })
  taskId: number;
}

/**
 * 添加任务Payload DTO
 * @MessagePattern('task.add')
 * 基于真实接口结构：{ characterId: string; serverId: string; taskConfig: any; injectedContext?: InjectedContext }
 */
export class AddTaskPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '服务器ID', example: 'server_001' })
  @Expose()
  @IsString({ message: '服务器ID必须是字符串' })
  @Length(1, 50, { message: '服务器ID长度必须在1-50个字符之间' })
  serverId: string;

  @ApiProperty({
    description: '任务配置',
    type: 'object',
    example: { taskId: 1001, taskType: 1, targetValue: 100, rewardItems: [] }
  })
  @Expose()
  @IsObject({ message: '任务配置必须是对象' })
  taskConfig: any;
}

/**
 * 获取任务统计Payload DTO
 * @MessagePattern('task.getStats')
 * 基于真实接口结构：{ adminToken?: string; injectedContext?: InjectedContext }
 */
export class GetTaskStatsPayloadDto extends BasePayloadDto {
  @ApiPropertyOptional({ description: '管理员令牌', example: 'admin_token_123' })
  @Expose()
  @IsOptional()
  @IsString({ message: '管理员令牌必须是字符串' })
  adminToken?: string;
}

/**
 * 获取任务排行榜Payload DTO
 * @MessagePattern('task.getLeaderboard')
 * 基于真实接口结构：{ taskType: TaskType; limit?: number; injectedContext?: InjectedContext }
 */
export class GetTaskLeaderboardPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '任务类型', enum: TaskType, example: TaskType.DAILY })
  @Expose()
  @IsEnum(TaskType, { message: '任务类型必须是有效的枚举值' })
  taskType: TaskType;

  @ApiPropertyOptional({ description: '排行榜数量限制', example: 100, minimum: 1, maximum: 500 })
  @Expose()
  @IsOptional()
  @IsNumber({}, { message: '排行榜数量限制必须是数字' })
  @Min(1, { message: '排行榜数量限制不能小于1' })
  @Max(500, { message: '排行榜数量限制不能大于500' })
  limit?: number;
}

/**
 * 批量领取奖励Payload DTO
 * @MessagePattern('task.batchClaimRewards')
 * 基于真实接口结构：{ characterId: string; serverId: string; taskIds: number[]; injectedContext?: InjectedContext }
 */
export class BatchClaimRewardsPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '服务器ID', example: 'server_001' })
  @Expose()
  @IsString({ message: '服务器ID必须是字符串' })
  @Length(1, 50, { message: '服务器ID长度必须在1-50个字符之间' })
  serverId: string;

  @ApiProperty({ description: '任务ID列表', type: [Number], example: [1001, 1002, 1003] })
  @Expose()
  @IsArray({ message: '任务ID列表必须是数组' })
  @IsNumber({}, { each: true, message: '任务ID必须是数字' })
  @Min(1, { each: true, message: '任务ID不能小于1' })
  @Max(999999, { each: true, message: '任务ID不能大于999999' })
  taskIds: number[];
}

// ==================== 4. 任务刷新相关 ====================

/**
 * 刷新任务Payload DTO
 * @MessagePattern('task.refresh')
 * 基于真实接口结构：{ characterId: string; serverId: string; taskType: TaskType; injectedContext?: InjectedContext }
 */
export class RefreshTaskPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '服务器ID', example: 'server_001' })
  @Expose()
  @IsString({ message: '服务器ID必须是字符串' })
  @Length(1, 50, { message: '服务器ID长度必须在1-50个字符之间' })
  serverId: string;

  @ApiProperty({ description: '任务类型', enum: TaskType, example: TaskType.DAILY })
  @Expose()
  @IsEnum(TaskType, { message: '任务类型必须是有效的枚举值' })
  taskType: TaskType;
}

/**
 * 批量刷新任务Payload DTO
 * @MessagePattern('task.batchRefresh')
 * 基于真实接口结构：{ taskType: TaskType; injectedContext?: InjectedContext }
 */
export class BatchRefreshTasksPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '任务类型', enum: TaskType, example: TaskType.DAILY })
  @Expose()
  @IsEnum(TaskType, { message: '任务类型必须是有效的枚举值' })
  taskType: TaskType;
}

// ==================== 5. 任务管理相关 ====================

/**
 * 清理过期任务Payload DTO
 * @MessagePattern('task.cleanExpired')
 * 基于真实接口结构：{ injectedContext?: InjectedContext }
 */
export class CleanExpiredTasksPayloadDto extends BasePayloadDto {
  // 仅继承BasePayloadDto，无需额外字段
}

/**
 * 删除任务Payload DTO
 * @MessagePattern('task.delete')
 * 基于真实接口结构：{ characterId: string; serverId: string; taskType: TaskType; taskId: number; injectedContext?: InjectedContext }
 */
export class DeleteTaskPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '服务器ID', example: 'server_001' })
  @Expose()
  @IsString({ message: '服务器ID必须是字符串' })
  @Length(1, 50, { message: '服务器ID长度必须在1-50个字符之间' })
  serverId: string;

  @ApiProperty({ description: '任务类型', enum: TaskType, example: TaskType.DAILY })
  @Expose()
  @IsEnum(TaskType, { message: '任务类型必须是有效的枚举值' })
  taskType: TaskType;

  @ApiProperty({ description: '任务ID', example: 1001 })
  @Expose()
  @IsNumber({}, { message: '任务ID必须是数字' })
  @Min(1, { message: '任务ID不能小于1' })
  @Max(999999, { message: '任务ID不能大于999999' })
  taskId: number;
}

// ==================== 6. 特定任务类型相关 ====================

/**
 * 获取每日任务Payload DTO
 * @MessagePattern('task.getDailyTasks')
 * 基于真实接口结构：{ characterId: string; serverId: string; injectedContext?: InjectedContext }
 */
export class GetDailyTasksPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '服务器ID', example: 'server_001' })
  @Expose()
  @IsString({ message: '服务器ID必须是字符串' })
  @Length(1, 50, { message: '服务器ID长度必须在1-50个字符之间' })
  serverId: string;
}

/**
 * 获取周任务Payload DTO
 * @MessagePattern('task.getWeeklyTasks')
 * 基于真实接口结构：{ characterId: string; serverId: string; injectedContext?: InjectedContext }
 */
export class GetWeeklyTasksPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '服务器ID', example: 'server_001' })
  @Expose()
  @IsString({ message: '服务器ID必须是字符串' })
  @Length(1, 50, { message: '服务器ID长度必须在1-50个字符之间' })
  serverId: string;
}

/**
 * 获取成就任务Payload DTO
 * @MessagePattern('task.getAchievementTasks')
 * 基于真实接口结构：{ characterId: string; serverId: string; injectedContext?: InjectedContext }
 */
export class GetAchievementTasksPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '服务器ID', example: 'server_001' })
  @Expose()
  @IsString({ message: '服务器ID必须是字符串' })
  @Length(1, 50, { message: '服务器ID长度必须在1-50个字符之间' })
  serverId: string;
}

/**
 * 获取新手任务Payload DTO
 * @MessagePattern('task.getNewbieTasks')
 * 基于真实接口结构：{ characterId: string; serverId: string; injectedContext?: InjectedContext }
 */
export class GetNewbieTasksPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '服务器ID', example: 'server_001' })
  @Expose()
  @IsString({ message: '服务器ID必须是字符串' })
  @Length(1, 50, { message: '服务器ID长度必须在1-50个字符之间' })
  serverId: string;
}

// ==================== 7. 任务触发相关 ====================

/**
 * 触发任务Payload DTO
 * @MessagePattern('task.trigger')
 * 基于真实接口结构：{ characterId: string; serverId: string; triggerType: number; arg1?: any; arg2?: any; arg3?: any; arg4?: any; injectedContext?: InjectedContext }
 */
export class TriggerTaskPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '服务器ID', example: 'server_001' })
  @Expose()
  @IsString({ message: '服务器ID必须是字符串' })
  @Length(1, 50, { message: '服务器ID长度必须在1-50个字符之间' })
  serverId: string;

  @ApiProperty({ description: '触发类型', example: 1 })
  @Expose()
  @IsNumber({}, { message: '触发类型必须是数字' })
  @Min(1, { message: '触发类型不能小于1' })
  @Max(999, { message: '触发类型不能大于999' })
  triggerType: number;

  @ApiPropertyOptional({ description: '参数1（可选）', example: 'param1' })
  @Expose()
  @IsOptional()
  arg1?: any;

  @ApiPropertyOptional({ description: '参数2（可选）', example: 'param2' })
  @Expose()
  @IsOptional()
  arg2?: any;

  @ApiPropertyOptional({ description: '参数3（可选）', example: 'param3' })
  @Expose()
  @IsOptional()
  arg3?: any;

  @ApiPropertyOptional({ description: '参数4（可选）', example: 'param4' })
  @Expose()
  @IsOptional()
  arg4?: any;
}

// ==================== 8. 任务检查相关 ====================

/**
 * 检查任务列表是否已满Payload DTO
 * @MessagePattern('task.checkFull')
 * 基于真实接口结构：{ characterId: string; serverId: string; taskType: TaskType; injectedContext?: InjectedContext }
 */
export class CheckTaskListFullPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '服务器ID', example: 'server_001' })
  @Expose()
  @IsString({ message: '服务器ID必须是字符串' })
  @Length(1, 50, { message: '服务器ID长度必须在1-50个字符之间' })
  serverId: string;

  @ApiProperty({ description: '任务类型', enum: TaskType, example: TaskType.DAILY })
  @Expose()
  @IsEnum(TaskType, { message: '任务类型必须是有效的枚举值' })
  taskType: TaskType;
}

/**
 * 检查任务是否存在Payload DTO
 * @MessagePattern('task.checkExists')
 * 基于真实接口结构：{ characterId: string; serverId: string; taskId: number; injectedContext?: InjectedContext }
 */
export class CheckTaskExistsPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '服务器ID', example: 'server_001' })
  @Expose()
  @IsString({ message: '服务器ID必须是字符串' })
  @Length(1, 50, { message: '服务器ID长度必须在1-50个字符之间' })
  serverId: string;

  @ApiProperty({ description: '任务ID', example: 1001 })
  @Expose()
  @IsNumber({}, { message: '任务ID必须是数字' })
  @Min(1, { message: '任务ID不能小于1' })
  @Max(999999, { message: '任务ID不能大于999999' })
  taskId: number;
}

// ==================== 9. 活动任务相关 ====================

/**
 * 更新活动任务进度Payload DTO
 * @MessagePattern('task.updateActivityProgress')
 * 基于真实接口结构：{ characterId: string; serverId: string; activityId: string; taskId: number; progress: number; injectedContext?: InjectedContext }
 */
export class UpdateActivityTaskProgressPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '服务器ID', example: 'server_001' })
  @Expose()
  @IsString({ message: '服务器ID必须是字符串' })
  @Length(1, 50, { message: '服务器ID长度必须在1-50个字符之间' })
  serverId: string;

  @ApiProperty({ description: '活动ID', example: 'activity_001' })
  @Expose()
  @IsString({ message: '活动ID必须是字符串' })
  @Length(1, 50, { message: '活动ID长度必须在1-50个字符之间' })
  activityId: string;

  @ApiProperty({ description: '任务ID', example: 1001 })
  @Expose()
  @IsNumber({}, { message: '任务ID必须是数字' })
  @Min(1, { message: '任务ID不能小于1' })
  @Max(999999, { message: '任务ID不能大于999999' })
  taskId: number;

  @ApiProperty({ description: '进度值', example: 50 })
  @Expose()
  @IsNumber({}, { message: '进度值必须是数字' })
  @Min(0, { message: '进度值不能小于0' })
  @Max(999999, { message: '进度值不能大于999999' })
  progress: number;
}

/**
 * 更新金牌教练任务进度Payload DTO
 * @MessagePattern('task.updateGoldCoachProgress')
 * 基于真实接口结构：{ characterId: string; serverId: string; coachAction: string; param?: any; injectedContext?: InjectedContext }
 */
export class UpdateGoldCoachTaskProgressPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '服务器ID', example: 'server_001' })
  @Expose()
  @IsString({ message: '服务器ID必须是字符串' })
  @Length(1, 50, { message: '服务器ID长度必须在1-50个字符之间' })
  serverId: string;

  @ApiProperty({ description: '教练行为', example: 'train_player' })
  @Expose()
  @IsString({ message: '教练行为必须是字符串' })
  @Length(1, 50, { message: '教练行为长度必须在1-50个字符之间' })
  coachAction: string;

  @ApiPropertyOptional({ description: '参数（可选）', example: { playerId: 'player_001', value: 10 } })
  @Expose()
  @IsOptional()
  @IsObject({ message: '参数必须是对象' })
  param?: any;
}

/**
 * 批量更新活动任务Payload DTO
 * @MessagePattern('task.batchUpdateActivity')
 * 基于真实接口结构：{ characterId: string; serverId: string; updates: Array<{ activityId: string; taskId: number; progress: number; }>; injectedContext?: InjectedContext }
 */
export class BatchUpdateActivityTasksPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '服务器ID', example: 'server_001' })
  @Expose()
  @IsString({ message: '服务器ID必须是字符串' })
  @Length(1, 50, { message: '服务器ID长度必须在1-50个字符之间' })
  serverId: string;

  @ApiProperty({
    description: '更新列表',
    type: 'array',
    example: [
      { activityId: 'activity_001', taskId: 1001, progress: 50 },
      { activityId: 'activity_002', taskId: 1002, progress: 100 }
    ]
  })
  @Expose()
  @IsArray({ message: '更新列表必须是数组' })
  @ValidateNested({ each: true, message: '更新项格式不正确' })
  @Type(() => Object)
  updates: Array<{
    activityId: string;
    taskId: number;
    progress: number;
  }>;
}
