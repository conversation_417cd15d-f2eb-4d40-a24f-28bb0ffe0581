/**
 * Guide模块的Payload DTO定义
 * 
 * 为guide.controller.ts中的所有@MessagePattern接口生成对应的Payload DTO
 * 所有DTO都继承自BasePayloadDto，基于真实的接口数据结构
 * 包含详细的校验规则和@Expose()装饰器
 */

import { ApiProperty } from '@nestjs/swagger';
import { IsString, Length } from 'class-validator';
import { Expose } from 'class-transformer';
import { BasePayloadDto } from '@libs/common/dto/base-payload.dto';

// ==================== 1. 引导信息相关 ====================

/**
 * 获取引导信息Payload DTO
 * @MessagePattern('guide.getInfo')
 * 基于真实接口结构：{ characterId: string; injectedContext?: InjectedContext }
 */
export class GetGuideInfoPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;
}

// ==================== 2. 引导操作相关 ====================

/**
 * 跳过引导Payload DTO
 * @MessagePattern('guide.skip')
 * 基于真实接口结构：{ characterId: string; injectedContext?: InjectedContext }
 */
export class SkipGuidePayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;
}

/**
 * 重置引导Payload DTO
 * @MessagePattern('guide.reset')
 * 基于真实接口结构：{ characterId: string; injectedContext?: InjectedContext }
 */
export class ResetGuidePayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;
}
