/**
 * Guide模块的Payload DTO定义
 *
 * 为guide.controller.ts中的所有@MessagePattern接口生成对应的Payload DTO
 * 所有DTO都继承自BasePayloadDto，基于真实的接口数据结构
 * 包含详细的校验规则和@Expose()装饰器
 */

import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNumber, Length, Min, Max } from 'class-validator';
import { Expose } from 'class-transformer';
import { BasePayloadDto } from '@libs/common/dto/base-payload.dto';

// ==================== 1. 引导状态相关 ====================

/**
 * 获取引导状态Payload DTO
 * @MessagePattern('guide.getStatus')
 * 基于真实接口结构：{ characterId: string; injectedContext?: InjectedContext }
 */
export class GetGuideStatusPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;
}

// ==================== 2. 引导步骤相关 ====================

/**
 * 完成引导步骤Payload DTO
 * @MessagePattern('guide.completeStep')
 * 基于真实接口结构：{ characterId: string; stepId: number; injectedContext?: InjectedContext }
 */
export class CompleteGuideStepPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '步骤ID', example: 1001 })
  @Expose()
  @IsNumber({}, { message: '步骤ID必须是数字' })
  @Min(1, { message: '步骤ID不能小于1' })
  @Max(999999, { message: '步骤ID不能大于999999' })
  stepId: number;
}

// ==================== 2. 引导操作相关 ====================

/**
 * 跳过引导Payload DTO
 * @MessagePattern('guide.skip')
 * 基于真实接口结构：{ characterId: string; injectedContext?: InjectedContext }
 */
export class SkipGuidePayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;
}

/**
 * 重置引导Payload DTO
 * @MessagePattern('guide.reset')
 * 基于真实接口结构：{ characterId: string; injectedContext?: InjectedContext }
 */
export class ResetGuidePayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;
}
