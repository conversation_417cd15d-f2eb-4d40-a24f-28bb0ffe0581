import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, FilterQuery, UpdateQuery, ClientSession } from 'mongoose';
import { Task, TaskDocument, TaskType, TaskStatus } from '../schemas/task.schema';
import { GetTaskListDto, TaskStatsDto } from '../dto/task.dto';
import { BaseRepository } from '@libs/common/repository/base-repository';
import { XResult, PaginationResult, XResultUtils } from '@libs/common/types/result.type';

/**
 * 任务数据访问层
 * 继承BaseRepository，提供统一的数据访问接口和Result模式错误处理
 *
 * 🎯 核心功能：
 * - 任务记录CRUD操作
 * - 任务进度管理
 * - 任务完成奖励处理
 * - 任务统计分析
 * - 批量操作支持
 *
 * 🚀 性能优化：
 * - 使用BaseRepository的Lean查询优化
 * - 自动性能监控和慢查询检测
 * - 智能缓存策略
 * - 并行查询优化
 */
@Injectable()
export class TaskRepository extends BaseRepository<TaskDocument> {
  constructor(
    @InjectModel(Task.name) taskModel: Model<TaskDocument>
  ) {
    super(taskModel, 'TaskRepository');
  }

  // ========== 业务特定方法 ==========

  /**
   * 创建任务记录
   * 使用BaseRepository的createOne方法，自动应用Result模式和性能监控
   */
  async createTask(taskData: Partial<Task>): Promise<XResult<TaskDocument>> {
    const taskWithTime = {
      ...taskData,
      lastUpdateTime: Date.now(),
    };
    return this.createOne(taskWithTime);
  }

  /**
   * 根据玩家ID查找任务记录
   * 使用BaseRepository的findOne方法优化性能
   */
  async findTaskByCharacterId(characterId: string): Promise<XResult<TaskDocument | null>> {
    return this.findOne({ characterId });
  }

  /**
   * 根据玩家ID查找任务记录（Lean查询优化版本）
   */
  async findTaskByCharacterIdLean(characterId: string): Promise<XResult<any | null>> {
    return this.findOneLean({ characterId });
  }

  /**
   * 获取或创建任务记录
   * 使用BaseRepository的事务支持确保数据一致性
   */
  async getOrCreateTask(characterId: string, serverId: string): Promise<XResult<TaskDocument>> {
    const taskResult = await this.findTaskByCharacterId(characterId);

    if (XResultUtils.isSuccess(taskResult) && taskResult.data) {
      return taskResult;
    }

    // 创建新任务记录
    const taskRecordId = this.generateTaskRecordId(characterId);
    const createResult = await this.createTask({
      taskRecordId,
      characterId,
      serverId,
    });

    if (XResultUtils.isSuccess(createResult)) {
      this.logger.log(`创建新任务记录: ${taskRecordId}`);
    }

    return createResult;
  }

  /**
   * 更新任务记录
   * 使用BaseRepository的updateOne方法优化性能
   */
  async updateTask(
    characterId: string,
    updateData: UpdateQuery<TaskDocument>,
    session?: ClientSession
  ): Promise<XResult<TaskDocument | null>> {
    return this.updateOne(
      { characterId },
      { ...updateData, lastUpdateTime: Date.now() },
      session
    );
  }

  /**
   * 获取需要刷新的任务
   * 使用BaseRepository的findMany方法优化性能
   */
  async findTasksNeedingRefresh(taskType: TaskType): Promise<XResult<TaskDocument[]>> {
    const now = Date.now();
    let timeField: string;
    let intervalMs: number;

    switch (taskType) {
      case TaskType.DAILY:
        timeField = 'dailyRefreshTime';
        intervalMs = 24 * 60 * 60 * 1000; // 1天
        break;
      case TaskType.WEEKLY:
        timeField = 'weeklyRefreshTime';
        intervalMs = 7 * 24 * 60 * 60 * 1000; // 7天
        break;
      default:
        return XResultUtils.ok([]);
    }

    const filter: FilterQuery<TaskDocument> = {
      [timeField]: { $lte: now - intervalMs }
    };

    return this.findMany(filter);
  }

  /**
   * 获取需要刷新的任务（Lean查询优化版本）
   */
  async findTasksNeedingRefreshLean(taskType: TaskType): Promise<XResult<any[]>> {
    const now = Date.now();
    let timeField: string;
    let intervalMs: number;

    switch (taskType) {
      case TaskType.DAILY:
        timeField = 'dailyRefreshTime';
        intervalMs = 24 * 60 * 60 * 1000;
        break;
      case TaskType.WEEKLY:
        timeField = 'weeklyRefreshTime';
        intervalMs = 7 * 24 * 60 * 60 * 1000;
        break;
      default:
        return XResultUtils.ok([]);
    }

    const filter: FilterQuery<TaskDocument> = {
      [timeField]: { $lte: now - intervalMs }
    };

    return this.findManyLean(filter, {
      select: ['taskRecordId', 'characterId', 'serverId', timeField]
    });
  }

  /**
   * 批量刷新任务
   * 使用BaseRepository的updateMany方法优化性能
   */
  async batchRefreshTasks(characterIds: string[], taskType: TaskType): Promise<XResult<any>> {
    const now = Date.now();
    let updateData: any = {};

    switch (taskType) {
      case TaskType.DAILY:
        updateData = {
          dailyTasks: [],
          dailyRefreshTime: now,
          dailyCompletedCount: 0,
        };
        break;
      case TaskType.WEEKLY:
        updateData = {
          weeklyTasks: [],
          weeklyRefreshTime: now,
          weeklyCompletedCount: 0,
        };
        break;
      default:
        return XResultUtils.error(`不支持的任务类型: ${taskType}`, 'INVALID_TASK_TYPE');
    }

    return this.updateMany(
      { characterId: { $in: characterIds } },
      { $set: { ...updateData, lastUpdateTime: now } }
    );
  }

  /**
   * 获取任务统计
   * 使用BaseRepository的findOne方法优化性能
   */
  async getTaskStats(characterId: string, days: number = 30, taskType?: TaskType): Promise<XResult<any>> {
    const taskResult = await this.findTaskByCharacterIdLean(characterId);
    if (XResultUtils.isFailure(taskResult) || !taskResult.data) {
      return XResultUtils.ok({
        totalCompletedTasks: 0,
        totalClaimedRewards: 0,
        dailyCompletedCount: 0,
        weeklyCompletedCount: 0,
        taskTypeDistribution: {},
        completionRate: {
          daily: 0,
          weekly: 0,
          achievement: 0,
          newbie: 0,
          event: 0,
          main: 0,
        },
        rewardStats: {
          totalRewards: 0,
          rewardTypes: {},
        },
      });
    }

    const task = taskResult.data;

    // 计算完成率
    const completionRate = {
      daily: this.calculateCompletionRate(task.dailyTasks),
      weekly: this.calculateCompletionRate(task.weeklyTasks),
      achievement: this.calculateCompletionRate(task.achievementTasks),
      newbie: this.calculateCompletionRate(task.newbieTasks),
      event: this.calculateCompletionRate(task.eventTasks),
      main: this.calculateCompletionRate(task.mainTasks),
    };

    // 计算任务类型分布
    const taskTypeDistribution = {
      daily: task.dailyTasks?.length || 0,
      weekly: task.weeklyTasks?.length || 0,
      achievement: task.achievementTasks?.length || 0,
      newbie: task.newbieTasks?.length || 0,
      event: task.eventTasks?.length || 0,
      main: task.mainTasks?.length || 0,
    };

    // 计算奖励统计
    const allTasks = [
      ...(task.dailyTasks || []),
      ...(task.weeklyTasks || []),
      ...(task.achievementTasks || []),
      ...(task.newbieTasks || []),
      ...(task.eventTasks || []),
      ...(task.mainTasks || []),
    ];

    const rewardTypes = allTasks
      .filter(t => t.status === TaskStatus.CLAIMED)
      .reduce((acc, t) => {
        t.rewards?.forEach(reward => {
          acc[reward.type] = (acc[reward.type] || 0) + reward.quantity;
        });
        return acc;
      }, {} as Record<string, number>);

    return XResultUtils.ok({
      totalCompletedTasks: task.totalCompletedTasks || 0,
      totalClaimedRewards: task.totalClaimedRewards || 0,
      dailyCompletedCount: task.dailyCompletedCount || 0,
      weeklyCompletedCount: task.weeklyCompletedCount || 0,
      taskTypeDistribution,
      completionRate,
      rewardStats: {
        totalRewards: task.totalClaimedRewards || 0,
        rewardTypes,
      },
    });
  }

  /**
   * 清理过期任务
   * 对于需要arrayFilters的复杂操作，直接使用原生MongoDB操作
   * 遵循Result模式但不使用try/catch，让错误自然抛出由上层处理
   */
  async cleanExpiredTasks(): Promise<XResult<any>> {
    const now = Date.now();

    // 直接使用原生MongoDB操作，不使用try/catch
    const result = await this.model.updateMany(
      {
        $or: [
          { 'dailyTasks.expireTime': { $gt: 0, $lt: now } },
          { 'weeklyTasks.expireTime': { $gt: 0, $lt: now } },
          { 'eventTasks.expireTime': { $gt: 0, $lt: now } },
        ]
      },
      {
        $set: {
          'dailyTasks.$[elem1].status': TaskStatus.EXPIRED,
          'weeklyTasks.$[elem2].status': TaskStatus.EXPIRED,
          'eventTasks.$[elem3].status': TaskStatus.EXPIRED,
          lastUpdateTime: now,
        }
      },
      {
        arrayFilters: [
          { 'elem1.expireTime': { $gt: 0, $lt: now } },
          { 'elem2.expireTime': { $gt: 0, $lt: now } },
          { 'elem3.expireTime': { $gt: 0, $lt: now } },
        ]
      }
    );

    const modifiedCount = result.modifiedCount || 0;
    this.logger.log(`清理过期任务完成，影响记录数: ${modifiedCount}`);
    return XResultUtils.ok(result);
  }

  /**
   * 获取玩家任务排行榜
   * 使用BaseRepository的aggregate方法优化性能和错误处理
   */
  async getTaskLeaderboard(serverId: string, taskType?: TaskType, limit: number = 100): Promise<XResult<any[]>> {
    const pipeline: any[] = [
      { $match: { serverId } },
      {
        $project: {
          characterId: 1,
          totalCompletedTasks: 1,
          totalClaimedRewards: 1,
          dailyCompletedCount: 1,
          weeklyCompletedCount: 1,
        }
      },
      { $sort: { totalCompletedTasks: -1 } },
      { $limit: limit },
    ];

    const result = await this.aggregate(pipeline);
    if (XResultUtils.isSuccess(result)) {
      return XResultUtils.ok(result.data);
    }

    return XResultUtils.ok([]);
  }

  // ========== 重写BaseRepository方法以添加业务特定逻辑 ==========

  /**
   * 重写数据验证方法，添加任务特定的验证规则
   */
  protected validateData(data: Partial<Task>, operation: 'create' | 'update'): XResult<void> {
    if (operation === 'create') {
      if (!data.taskRecordId) {
        return XResultUtils.error('任务记录ID不能为空', 'TASK_RECORD_ID_REQUIRED');
      }

      if (!data.characterId) {
        return XResultUtils.error('角色ID不能为空', 'CHARACTER_ID_REQUIRED');
      }

      if (!data.serverId) {
        return XResultUtils.error('服务器ID不能为空', 'SERVER_ID_REQUIRED');
      }
    }

    if (data.totalCompletedTasks !== undefined && data.totalCompletedTasks < 0) {
      return XResultUtils.error('完成任务数不能为负数', 'INVALID_COMPLETED_TASKS');
    }

    if (data.totalClaimedRewards !== undefined && data.totalClaimedRewards < 0) {
      return XResultUtils.error('领取奖励数不能为负数', 'INVALID_CLAIMED_REWARDS');
    }

    return XResultUtils.ok(undefined);
  }

  /**
   * 重写缓存TTL配置，针对任务数据的访问模式优化
   */
  protected getCacheTTL(operation: string): number {
    const customTTLs: Record<string, number> = {
      'findTaskByCharacterId': 300,      // 任务记录缓存5分钟
      'findTaskByCharacterIdLean': 180,  // 任务简介缓存3分钟
      'getTaskStats': 600,               // 统计信息缓存10分钟
      'getTaskLeaderboard': 300,         // 排行榜缓存5分钟
      'findTasksNeedingRefresh': 60,     // 刷新查询缓存1分钟
    };

    return customTTLs[operation] || super.getCacheTTL(operation);
  }

  // ==================== 私有方法 ====================

  /**
   * 生成任务记录ID
   */
  private generateTaskRecordId(characterId: string): string {
    return `task_${characterId}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 计算完成率
   */
  private calculateCompletionRate(tasks: any[]): number {
    if (tasks.length === 0) return 0;
    
    const completedTasks = tasks.filter(task => 
      task.status === TaskStatus.COMPLETED || task.status === TaskStatus.CLAIMED
    ).length;
    
    return Math.floor((completedTasks / tasks.length) * 100);
  }
}
