import { Injectable, Logger } from '@nestjs/common';
import { XResult, XResultUtils, ServiceResultHandler } from '../types/result.type';

/**
 * 业务操作选项
 */
export interface BusinessOperationOptions {
  /** 操作原因/来源 */
  reason?: string;
  /** 是否跳过验证 */
  skipValidation?: boolean;
  /** 额外的元数据 */
  metadata?: Record<string, any>;
  /** 操作超时时间（毫秒） */
  timeout?: number;
}

/**
 * 微服务调用选项
 */
export interface MicroserviceCallOptions {
  /** 超时时间（毫秒） */
  timeout?: number;
  /** 重试次数 */
  retries?: number;
  /** 是否记录调用日志 */
  logCall?: boolean;
}

/**
 * 微服务客户端接口（抽象接口，避免具体依赖）
 */
export interface IMicroserviceClient {
  call<T = any>(serviceName: string, pattern: string, payload: any): Promise<any>;
}

/**
 * 分页查询选项
 */
export interface ServicePaginationOptions {
  /** 页码（从1开始） */
  page: number;
  /** 每页记录数 */
  limit: number;
  /** 排序字段 */
  sortBy?: string;
  /** 排序方向 */
  sortOrder?: 'asc' | 'desc';
  /** 搜索关键词 */
  search?: string;
  /** 额外的过滤条件 */
  filters?: Record<string, any>;
}

/**
 * 通用BaseService基类
 * 
 * 🎯 设计目标：
 * - 统一的业务逻辑处理模式
 * - 标准化的微服务调用
 * - 完整的Result模式集成
 * - 通用的错误处理和日志记录
 * - 缓存集成支持
 * - 业务验证框架
 * 
 * 🚀 核心特性：
 * - 统一的Result模式错误处理
 * - 标准化的微服务通信
 * - 通用的业务操作模式
 * - 完整的日志记录
 * - 性能监控支持
 * - 缓存集成框架
 * 
 * 📖 使用示例：
 * ```typescript
 * @Injectable()
 * export class CharacterService extends BaseService {
 *   constructor(
 *     private readonly characterRepository: CharacterRepository,
 *     microserviceClient: MicroserviceClientService
 *   ) {
 *     super('CharacterService', microserviceClient);
 *   }
 * 
 *   async createCharacter(createDto: CreateCharacterDto): Promise<XResult<Character>> {
 *     return this.executeBusinessOperation(async () => {
 *       // 业务逻辑实现
 *       const result = await this.characterRepository.createOne(createDto);
 *       return this.handleRepositoryResult(result);
 *     }, { reason: 'create_character' });
 *   }
 * }
 * ```
 */
@Injectable()
export abstract class BaseService {
  protected readonly logger: Logger;

  constructor(
    protected readonly serviceName: string,
    protected readonly microserviceClient?: IMicroserviceClient
  ) {
    this.logger = new Logger(serviceName);
  }

  // ========== 核心业务操作方法 ==========

  /**
   * 执行业务操作（带性能监控和错误处理）
   * @param operation 业务操作函数
   * @param options 操作选项
   *
   * ⚠️ 注意事项：
   * 由于Repository层已经将所有异常转换为XResult，所以简单的业务逻辑如果使用
   * executeBusinessOperation那么catch块永远不会被执行！
   * 对于简单的Repository调用，直接使用handleRepositoryResult
   *
   * 📖 使用示例：
   * ```typescript
   * // 适合使用executeBusinessOperation的场景
   * async transferItemBetweenPlayers(
   *   fromCharacterId: string,
   *   toCharacterId: string,
   *   itemId: string
   * ): Promise<XResult<TransferResult>> {
   *   return this.executeBusinessOperation(async () => {  // ← 这里有价值
   *     // 1. 复杂的业务逻辑验证
   *     const validation = await this.validateTransferRules(fromCharacterId, toCharacterId, itemId);
   *     if (XResultUtils.isFailure(validation)) return validation;
   *
   *     // 2. 多个Repository调用
   *     const fromResult = await this.inventoryRepository.findById(fromCharacterId);
   *     const fromError = this.handleRepositoryResult(fromResult);
   *     if (XResultUtils.isFailure(fromError)) return fromError;
   *
   *     // 3. 微服务调用
   *     const economyResult = await this.callMicroservice('economy', 'checkTransferFee', {...});
   *     if (XResultUtils.isFailure(economyResult)) return economyResult;
   *
   *     // 4. 事务操作
   *     return await this.executeTransfer(fromCharacterId, toCharacterId, itemId);
   *   }, { reason: 'transfer_item' });  // ← 性能监控和日志记录有价值
   * }
   * ```
   */
  protected async executeBusinessOperation<T>(
    operation: () => Promise<XResult<T>>,
    options: BusinessOperationOptions = {}
  ): Promise<XResult<T>> {
    const startTime = Date.now();
    const operationId = this.generateOperationId();

    try {
      this.logOperationStart(operationId, options);

      const result = await operation();

      this.logOperationEnd(operationId, startTime, XResultUtils.isSuccess(result));
      return result;

    } catch (error) {
      this.logOperationError(operationId, startTime, error);
      return XResultUtils.error(
        `业务操作失败: ${error.message}`,
        'BUSINESS_OPERATION_ERROR'
      );
    }
  }

  /**
   * 处理Repository结果
   * @param repositoryResult Repository返回的结果
   * @param errorMessage 自定义错误消息
   */
  protected handleRepositoryResult<T>(
    repositoryResult: XResult<T>,
    errorMessage?: string
  ): XResult<T> {
    if (XResultUtils.isFailure(repositoryResult)) {
      const message = errorMessage || `数据访问失败: ${repositoryResult.message}`;
      this.logger.error(message, { originalError: repositoryResult });
      return XResultUtils.error(message, repositoryResult.code);
    }

    return repositoryResult;
  }

  /**
   * 验证业务规则
   * @param validations 验证函数数组
   */
  protected async validateBusinessRules(
    validations: Array<() => Promise<XResult<void>> | XResult<void>>
  ): Promise<XResult<void>> {
    for (const validation of validations) {
      const result = await validation();
      if (XResultUtils.isFailure(result)) {
        return result;
      }
    }
    return XResultUtils.ok(undefined);
  }

  // ========== 微服务调用方法 ==========

  /**
   * 调用微服务（带错误处理和日志）
   * @param serviceName 服务名称
   * @param pattern 消息模式
   * @param payload 请求数据
   * @param options 调用选项
   */
  protected async callMicroservice<T = any>(
    serviceName: string,
    pattern: string,
    payload: any,
    options: MicroserviceCallOptions = {}
  ): Promise<XResult<T>> {
    if (!this.microserviceClient) {
      return XResultUtils.error('微服务客户端未初始化', 'MICROSERVICE_CLIENT_NOT_INITIALIZED');
    }

    const startTime = Date.now();
    const callId = this.generateCallId();

    try {
      if (options.logCall !== false) {
        this.logger.log(`微服务调用开始: ${callId} - ${serviceName}.${pattern}`, {
          payload: this.sanitizePayload(payload)
        });
      }

      const result = await this.microserviceClient.call(serviceName, pattern, payload);

      const duration = Date.now() - startTime;
      if (options.logCall !== false) {
        this.logger.log(`微服务调用完成: ${callId} - 耗时 ${duration}ms`, {
          success: result?.code === 0
        });
      }

      // 转换微服务响应为Result格式
      if (result && result.code === 0) {
        return XResultUtils.ok(result.data);
      } else {
        const errorMessage = result?.message || '微服务调用失败';
        return XResultUtils.error(errorMessage, `MICROSERVICE_ERROR_${result?.code || 'UNKNOWN'}`);
      }

    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error(`微服务调用失败: ${callId} - 耗时 ${duration}ms`, error);
      return XResultUtils.error(
        `微服务调用异常: ${error.message}`,
        'MICROSERVICE_CALL_EXCEPTION'
      );
    }
  }

  /**
   * 批量调用微服务
   * @param calls 调用配置数组
   */
  protected async batchCallMicroservices<T = any>(
    calls: Array<{
      serviceName: string;
      pattern: string;
      payload: any;
      key?: string;
    }>
  ): Promise<XResult<Record<string, T>>> {
    try {
      const promises = calls.map(async (call, index) => {
        const key = call.key || `call_${index}`;
        const result = await this.callMicroservice(call.serviceName, call.pattern, call.payload);
        return { key, result };
      });

      const results = await Promise.all(promises);
      const successResults: Record<string, T> = {};
      const errors: string[] = [];

      for (const { key, result } of results) {
        if (XResultUtils.isSuccess(result)) {
          successResults[key] = result.data;
        } else {
          errors.push(`${key}: ${result.message}`);
        }
      }

      if (errors.length > 0) {
        return XResultUtils.error(
          `批量调用部分失败: ${errors.join(', ')}`,
          'BATCH_CALL_PARTIAL_FAILURE'
        );
      }

      return XResultUtils.ok(successResults);

    } catch (error) {
      this.logger.error('批量微服务调用失败', error);
      return XResultUtils.error(
        `批量调用异常: ${error.message}`,
        'BATCH_CALL_EXCEPTION'
      );
    }
  }

  // ========== 通用业务方法 ==========

  /**
   * 获取角色信息（通用方法）
   * @param characterId 角色ID
   */
  /*protected async getCharacterInfo(characterId: string): Promise<XResult<any>> {
    return this.callMicroservice(
      'character',
      'character.getInfo',
      { characterId }
    );
  }*/

  /**
   * 检查角色资源是否足够
   * @param characterId 角色ID
   * @param resourceType 资源类型
   * @param amount 需要的数量
   */
  /*protected async checkCharacterResource(
    characterId: string,
    resourceType: string,
    amount: number
  ): Promise<XResult<boolean>> {
    const characterResult = await this.getCharacterInfo(characterId);
    if (XResultUtils.isFailure(characterResult)) {
      return characterResult as any;
    }

    const character = characterResult.data;
    const currentAmount = character[resourceType] || 0;
    const sufficient = currentAmount >= amount;

    if (!sufficient) {
      return XResultUtils.error(
        `${resourceType}不足，需要${amount}，当前${currentAmount}`,
        'INSUFFICIENT_RESOURCE'
      );
    }

    return XResultUtils.ok(true);
  }*/

  /**
   * 扣除角色资源
   * @param characterId 角色ID
   * @param resourceType 资源类型
   * @param amount 扣除数量
   * @param reason 扣除原因
   */
  /*protected async deductCharacterResource(
    characterId: string,
    resourceType: string,
    amount: number,
    reason: string
  ): Promise<XResult<any>> {
    return this.callMicroservice(
      'character',
      'character.deductCurrency',
      { characterId, currencyType: resourceType, amount, reason }
    );
  }*/

  /**
   * 添加角色资源
   * @param characterId 角色ID
   * @param resourceType 资源类型
   * @param amount 添加数量
   * @param reason 添加原因
   */
  /*protected async addCharacterResource(
    characterId: string,
    resourceType: string,
    amount: number,
    reason: string
  ): Promise<XResult<any>> {
    return this.callMicroservice(
      'character',
      'character.addCurrency',
      { characterId, currencyType: resourceType, amount, reason }
    );
  }*/

  // ========== 工具方法 ==========

  /**
   * 生成操作ID
   */
  protected generateOperationId(): string {
    return `${this.serviceName}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 生成调用ID
   */
  protected generateCallId(): string {
    return `call_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`;
  }

  /**
   * 清理敏感数据
   * @param payload 请求数据
   */
  protected sanitizePayload(payload: any): any {
    if (!payload || typeof payload !== 'object') {
      return payload;
    }

    const sensitiveFields = ['password', 'token', 'secret', 'key'];
    const sanitized = { ...payload };

    for (const field of sensitiveFields) {
      if (field in sanitized) {
        sanitized[field] = '***';
      }
    }

    return sanitized;
  }

  /**
   * 记录操作开始
   */
  protected logOperationStart(operationId: string, options: BusinessOperationOptions): void {
    this.logger.log(`业务操作开始: ${operationId}`, {
      reason: options.reason,
      metadata: options.metadata
    });
  }

  /**
   * 记录操作结束
   */
  protected logOperationEnd(operationId: string, startTime: number, success: boolean): void {
    const duration = Date.now() - startTime;
    const level = success ? 'log' : 'warn';
    this.logger[level](`业务操作${success ? '成功' : '失败'}: ${operationId} - 耗时 ${duration}ms`);

    if (duration > 5000) {
      this.logger.warn(`慢操作检测: ${operationId} 耗时 ${duration}ms`);
    }
  }

  /**
   * 记录操作错误
   */
  protected logOperationError(operationId: string, startTime: number, error: any): void {
    const duration = Date.now() - startTime;
    this.logger.error(`业务操作异常: ${operationId} - 耗时 ${duration}ms`, {
      error: error.message,
      stack: error.stack
    });
  }

  /**
   * 构建分页查询条件
   * @param options 分页选项
   */
  protected buildPaginationQuery(options: ServicePaginationOptions): any {
    const query: any = {
      page: options.page,
      limit: options.limit
    };

    if (options.sortBy) {
      query.sortBy = options.sortBy;
      query.sortOrder = options.sortOrder || 'desc';
    }

    if (options.search) {
      query.search = options.search;
    }

    if (options.filters) {
      Object.assign(query, options.filters);
    }

    return query;
  }

  /**
   * 获取服务名称
   */
  protected getServiceName(): string {
    return this.serviceName;
  }

  // ==================== 实用业务验证方法 ====================

  /**
   * 验证唯一性约束（最常用）
   * @param value 要检查的值
   * @param fieldName 字段名称
   * @param checkExists 检查是否存在的函数，返回true表示已存在
   *
   * @example
   * ```typescript
   * // 验证角色名唯一性
   * const nameCheck = await this.validateUniqueness(
   *   createDto.name,
   *   '角色名称',
   *   (name) => this.characterRepository.existsByName(name, serverId)
   * );
   * if (XResultUtils.isFailure(nameCheck)) return nameCheck;
   *
   * // 验证邮箱唯一性
   * const emailCheck = await this.validateUniqueness(
   *   registerDto.email,
   *   '邮箱地址',
   *   (email) => this.userRepository.existsByEmail(email)
   * );
   * ```
   */
  protected async validateUniqueness(
    value: string,
    fieldName: string,
    checkExists: (value: string) => Promise<boolean>
  ): Promise<XResult<void>> {
    try {
      const exists = await checkExists(value);
      if (exists) {
        return XResultUtils.error(`${fieldName}已存在`, 'DUPLICATE_VALUE');
      }
      return XResultUtils.ok(undefined);
    } catch (error) {
      this.logger.error(`唯一性验证失败: ${fieldName}`, error);
      return XResultUtils.error(`${fieldName}验证失败`, 'VALIDATION_ERROR');
    }
  }

  /**
   * 验证资源所有权（权限验证简化版）
   * @param userId 用户ID
   * @param resourceOwnerId 资源所有者ID
   * @param resourceName 资源名称
   *
   * @example
   * ```typescript
   * // 验证角色所有权
   * const ownershipCheck = this.validateOwnership(
   *   userId,
   *   character.userId,
   *   '角色'
   * );
   * if (XResultUtils.isFailure(ownershipCheck)) return ownershipCheck;
   *
   * // 验证装备所有权
   * const equipmentCheck = this.validateOwnership(
   *   userId,
   *   equipment.ownerId,
   *   '装备'
   * );
   * ```
   */
  protected validateOwnership(
    userId: string,
    resourceOwnerId: string,
    resourceName: string
  ): XResult<void> {
    if (userId !== resourceOwnerId) {
      return XResultUtils.error(`无权操作此${resourceName}`, 'ACCESS_DENIED');
    }
    return XResultUtils.ok(undefined);
  }

  /**
   * 验证资源充足性（游戏中最常用）
   * @param currentAmount 当前数量
   * @param requiredAmount 需要数量
   * @param resourceName 资源名称
   *
   * @example
   * ```typescript
   * // 验证金币是否充足
   * const goldCheck = this.validateSufficientResource(
   *   character.gold,
   *   upgradeConfig.goldCost,
   *   '金币'
   * );
   * if (XResultUtils.isFailure(goldCheck)) return goldCheck;
   *
   * // 验证体力是否充足
   * const energyCheck = this.validateSufficientResource(
   *   character.energy,
   *   actionConfig.energyCost,
   *   '体力'
   * );
   * ```
   */
  protected validateSufficientResource(
    currentAmount: number,
    requiredAmount: number,
    resourceName: string
  ): XResult<void> {
    if (currentAmount < requiredAmount) {
      return XResultUtils.error(
        `${resourceName}不足，需要${requiredAmount}，当前${currentAmount}`,
        'INSUFFICIENT_RESOURCE'
      );
    }
    return XResultUtils.ok(undefined);
  }

  /**
   * 验证业务状态（简化版）
   * @param currentStatus 当前状态
   * @param allowedStatuses 允许的状态列表
   * @param operationName 操作名称
   *
   * @example
   * ```typescript
   * // 验证角色是否可以进行训练
   * const statusCheck = this.validateBusinessStatus(
   *   character.status,
   *   ['IDLE', 'RESTING'],
   *   '开始训练'
   * );
   * if (XResultUtils.isFailure(statusCheck)) return statusCheck;
   *
   * // 验证订单是否可以取消
   * const orderCheck = this.validateBusinessStatus(
   *   order.status,
   *   ['PENDING', 'CONFIRMED'],
   *   '取消订单'
   * );
   * ```
   */
  protected validateBusinessStatus(
    currentStatus: string,
    allowedStatuses: string[],
    operationName: string
  ): XResult<void> {
    if (!allowedStatuses.includes(currentStatus)) {
      return XResultUtils.error(
        `当前状态(${currentStatus})不允许${operationName}`,
        'INVALID_STATUS'
      );
    }
    return XResultUtils.ok(undefined);
  }

  /**
   * 验证时间限制（冷却时间、活动时间等）
   * @param lastActionTime 上次操作时间
   * @param cooldownMs 冷却时间（毫秒）
   * @param actionName 操作名称
   *
   * @example
   * ```typescript
   * // 验证签到冷却时间
   * const checkinCheck = this.validateTimeLimit(
   *   character.lastCheckinTime,
   *   24 * 60 * 60 * 1000, // 24小时
   *   '每日签到'
   * );
   * if (XResultUtils.isFailure(checkinCheck)) return checkinCheck;
   *
   * // 验证抽卡冷却时间
   * const drawCheck = this.validateTimeLimit(
   *   character.lastDrawTime,
   *   5 * 60 * 1000, // 5分钟
   *   '抽卡'
   * );
   * ```
   */
  protected validateTimeLimit(
    lastActionTime: number,
    cooldownMs: number,
    actionName: string
  ): XResult<void> {
    const now = Date.now();
    const timePassed = now - lastActionTime;

    if (timePassed < cooldownMs) {
      const remainingMs = cooldownMs - timePassed;
      const remainingMinutes = Math.ceil(remainingMs / (60 * 1000));
      return XResultUtils.error(
        `${actionName}冷却中，还需等待${remainingMinutes}分钟`,
        'COOLDOWN_ACTIVE'
      );
    }

    return XResultUtils.ok(undefined);
  }

  /**
   * 验证数量限制（每日限制、库存限制等）
   * @param currentCount 当前数量
   * @param maxCount 最大数量
   * @param limitName 限制名称
   *
   * @example
   * ```typescript
   * // 验证每日任务完成次数
   * const dailyTaskCheck = this.validateCountLimit(
   *   character.dailyTaskCount,
   *   10,
   *   '每日任务'
   * );
   * if (XResultUtils.isFailure(dailyTaskCheck)) return dailyTaskCheck;
   *
   * // 验证背包容量
   * const inventoryCheck = this.validateCountLimit(
   *   character.inventory.length,
   *   100,
   *   '背包容量'
   * );
   * ```
   */
  protected validateCountLimit(
    currentCount: number,
    maxCount: number,
    limitName: string
  ): XResult<void> {
    if (currentCount >= maxCount) {
      return XResultUtils.error(
        `${limitName}已达上限(${maxCount})`,
        'LIMIT_EXCEEDED'
      );
    }
    return XResultUtils.ok(undefined);
  }

  /**
   * 验证配置条件（等级、VIP等级等）
   * @param currentLevel 当前等级
   * @param requiredLevel 需要等级
   * @param levelType 等级类型
   *
   * @example
   * ```typescript
   * // 验证角色等级
   * const levelCheck = this.validateLevelRequirement(
   *   character.level,
   *   10,
   *   '角色等级'
   * );
   * if (XResultUtils.isFailure(levelCheck)) return levelCheck;
   *
   * // 验证VIP等级
   * const vipCheck = this.validateLevelRequirement(
   *   character.vipLevel,
   *   3,
   *   'VIP等级'
   * );
   * ```
   */
  protected validateLevelRequirement(
    currentLevel: number,
    requiredLevel: number,
    levelType: string
  ): XResult<void> {
    if (currentLevel < requiredLevel) {
      return XResultUtils.error(
        `${levelType}不足，需要${requiredLevel}级，当前${currentLevel}级`,
        'LEVEL_INSUFFICIENT'
      );
    }
    return XResultUtils.ok(undefined);
  }

  /**
   * 批量验证（组合多个验证）
   * @param validations 验证函数数组
   *
   * @example
   * 示例1：
   * ```typescript
   * // 批量验证多个条件
   * const validationResult = await this.validateBatch([
   *   () => this.validateUniqueness(name, '角色名', checkName),
   *   () => this.validateSufficientResource(gold, cost, '金币'),
   *   () => this.validateLevelRequirement(level, 10, '角色等级')
   * ]);
   * if (XResultUtils.isFailure(validationResult)) return validationResult;
   * ```
   * 示例2：
   * ```typescript
   * // 角色创建的批量验证
   * const batchResult = await this.validateBatch([
   *   // 验证用户是否存在
   *   () => this.validateResourceExists(userId, '用户', (id) => this.userRepository.findById(id)),
   *
   *   // 验证角色名称唯一性
   *   () => this.validateUniqueness(
   *     createDto.name,
   *     '角色名称',
   *     (name) => this.characterRepository.existsByName(name)
   *   ),
   *
   *   // 验证用户角色数量配额
   *   () => this.validateResourceQuota(
   *     await this.characterRepository.countByUserId(userId),
   *     5, // 最多5个角色
   *     '角色'
   *   ),
   *
   *   // 验证服务器容量
   *   async () => {
   *     const serverLoad = await this.getServerLoad(createDto.serverId);
   *     return serverLoad < 0.9
   *       ? XResultUtils.ok(undefined)
   *       : XResultUtils.error('服务器负载过高', 'SERVER_OVERLOADED');
   *   }
   * ]);
   *
   * if (XResultUtils.isFailure(batchResult)) {
   *   return batchResult; // 返回第一个失败的验证错误
   * }
   * ```
   */
  protected async validateBatch(
    validations: Array<() => Promise<XResult<any>> | XResult<any>>
  ): Promise<XResult<void>> {
    const errors: string[] = [];

    for (const validation of validations) {
      try {
        const result = await validation();
        if (XResultUtils.isFailure(result)) {
          errors.push(result.message);
        }
      } catch (error) {
        errors.push(`验证异常: ${error.message}`);
      }
    }

    if (errors.length > 0) {
      return XResultUtils.error(`验证失败: ${errors.join('; ')}`, 'BATCH_VALIDATION_FAILED');
    }

    return XResultUtils.ok(undefined);
  }

  /**
   * 验证数组长度
   * @param array 数组
   * @param fieldName 字段名称
   * @param minLength 最小长度
   * @param maxLength 最大长度
   */
  protected validateArrayLength<T>(
    array: T[],
    fieldName: string,
    minLength?: number,
    maxLength?: number
  ): XResult<void> {
    if (!Array.isArray(array)) {
      return XResultUtils.error(`${fieldName}必须是数组`, 'INVALID_ARRAY');
    }

    if (minLength !== undefined && array.length < minLength) {
      return XResultUtils.error(`${fieldName}至少需要${minLength}个元素`, 'ARRAY_TOO_SHORT');
    }

    if (maxLength !== undefined && array.length > maxLength) {
      return XResultUtils.error(`${fieldName}最多只能有${maxLength}个元素`, 'ARRAY_TOO_LONG');
    }

    return XResultUtils.ok(undefined);
  }

  /**
   * 验证外键约束
   * @param foreignId 外键ID
   * @param foreignType 外键类型
   * @param checkFunction 外键检查函数
   */
  protected async validateForeignKey(
    foreignId: string,
    foreignType: string,
    checkFunction: (id: string) => Promise<boolean>
  ): Promise<XResult<void>> {
    if (!foreignId) {
      return XResultUtils.error(`${foreignType}ID不能为空`, 'FOREIGN_KEY_REQUIRED');
    }

    try {
      const exists = await checkFunction(foreignId);
      if (!exists) {
        return XResultUtils.error(`关联的${foreignType}不存在`, 'FOREIGN_KEY_NOT_FOUND');
      }

      return XResultUtils.ok(undefined);
    } catch (error) {
      this.logger.error('外键验证失败', {
        foreignId,
        foreignType,
        error: error.message,
      });
      return XResultUtils.error('外键验证失败', 'FOREIGN_KEY_CHECK_FAILED');
    }
  }

  /**
   * 条件验证
   * @param condition 条件
   * @param validation 验证函数
   * @param errorMessage 错误消息
   * @param errorCode 错误代码
   */
  protected async validateIf(
    condition: boolean | (() => boolean | Promise<boolean>),
    validation: () => Promise<XResult<any>> | XResult<any>,
    errorMessage?: string,
    errorCode?: string
  ): Promise<XResult<void>> {
    try {
      const shouldValidate = typeof condition === 'function' ? await condition() : condition;

      if (!shouldValidate) {
        return XResultUtils.ok(undefined);
      }

      const result = await validation();
      if (XResultUtils.isFailure(result)) {
        return result;
      }

      return XResultUtils.ok(undefined);
    } catch (error) {
      return XResultUtils.error(
        errorMessage || `条件验证失败: ${error.message}`,
        errorCode || 'CONDITIONAL_VALIDATION_FAILED'
      );
    }
  }

  /**
   * 验证复杂业务规则
   * @param rules 业务规则数组
   *
   * @example
   * ```typescript
   * // 装备升级的复杂业务规则验证
   * const rulesResult = await this.validateComplexBusinessRules([
   *   {
   *     name: '等级要求',
   *     check: () => character.level >= 10,
   *     errorMessage: '角色等级必须达到10级才能升级装备',
   *     errorCode: 'LEVEL_TOO_LOW'
   *   },
   *   {
   *     name: '升级冷却时间',
   *     check: async () => {
   *       if (!equipment.lastUpgradeAt) return true;
   *       const cooldownHours = 2;
   *       const timeSinceLastUpgrade = Date.now() - equipment.lastUpgradeAt.getTime();
   *       return timeSinceLastUpgrade >= cooldownHours * 60 * 60 * 1000;
   *     },
   *     errorMessage: '升级冷却时间未到，请2小时后再试',
   *     errorCode: 'UPGRADE_COOLDOWN'
   *   },
   *   {
   *     name: '升级材料检查',
   *     check: async () => {
   *       const materials = await this.inventoryService.getMaterials(characterId);
   *       const required = this.getRequiredMaterials(equipment.level + 1);
   *       return Object.entries(required).every(([materialId, count]) =>
   *         (materials[materialId] || 0) >= count
   *       );
   *     },
   *     errorMessage: '升级材料不足',
   *     errorCode: 'INSUFFICIENT_MATERIALS'
   *   },
   *   {
   *     name: '成功率检查',
   *     check: () => {
   *       const successRate = this.calculateUpgradeSuccessRate(equipment.level);
   *       return successRate >= 0.1; // 至少10%成功率
   *     },
   *     errorMessage: '升级成功率过低，无法进行升级',
   *     errorCode: 'SUCCESS_RATE_TOO_LOW'
   *   }
   * ]);
   *
   * if (XResultUtils.isFailure(rulesResult)) {
   *   return rulesResult; // 返回第一个失败的业务规则错误
   * }
   * ```
   */
  protected async validateComplexBusinessRules(
    rules: Array<{
      name: string;
      check: () => Promise<boolean> | boolean;
      errorMessage: string;
      errorCode?: string;
    }>
  ): Promise<XResult<void>> {
    for (const rule of rules) {
      try {
        const passed = await rule.check();
        if (!passed) {
          return XResultUtils.error(rule.errorMessage, rule.errorCode || 'BUSINESS_RULE_VIOLATION');
        }
      } catch (error) {
        this.logger.error('业务规则验证异常', {
          ruleName: rule.name,
          error: error.message,
        });
        return XResultUtils.error(
          `业务规则 "${rule.name}" 验证失败: ${error.message}`,
          'BUSINESS_RULE_CHECK_FAILED'
        );
      }
    }

    return XResultUtils.ok(undefined);
  }

  /**
   * 验证时间范围
   * @param startTime 开始时间
   * @param endTime 结束时间
   * @param fieldName 字段名称前缀
   */
  protected validateTimeRange(
    startTime: Date,
    endTime: Date,
    fieldName: string = '时间'
  ): XResult<void> {
    if (!startTime || !endTime) {
      return XResultUtils.error(`${fieldName}范围不能为空`, 'TIME_RANGE_REQUIRED');
    }

    if (!(startTime instanceof Date) || !(endTime instanceof Date)) {
      return XResultUtils.error(`${fieldName}必须是有效的日期`, 'INVALID_DATE');
    }

    if (startTime >= endTime) {
      return XResultUtils.error(`${fieldName}开始时间必须早于结束时间`, 'INVALID_TIME_RANGE');
    }

    return XResultUtils.ok(undefined);
  }

  /**
   * 验证资源配额
   * @param currentCount 当前数量
   * @param maxAllowed 最大允许数量
   * @param resourceType 资源类型
   */
  protected validateResourceQuota(
    currentCount: number,
    maxAllowed: number,
    resourceType: string
  ): XResult<void> {
    if (currentCount >= maxAllowed) {
      return XResultUtils.error(
        `${resourceType}数量已达上限 (${maxAllowed})`,
        'RESOURCE_QUOTA_EXCEEDED'
      );
    }

    return XResultUtils.ok(undefined);
  }
}
