/**
 * 微服务Payload基类
 * 
 * 所有@MessagePattern接口的payload DTO都应该继承自这个基类
 * 包含了网关动态注入的通用字段
 * 
 * 使用方式：
 * ```typescript
 * export class UpdateCharacterPayloadDto extends BasePayloadDto {
 *   @Expose()
 *   @IsString()
 *   characterId: string;
 * 
 *   @Expose()
 *   @ValidateNested()
 *   @Type(() => UpdateCharacterDto)
 *   updateDto: UpdateCharacterDto;
 * }
 * 
 * @MessagePattern('character.update')
 * @UsePipes(StandardMicroserviceValidationPipe)
 * async updateCharacter(@Payload() payload: UpdateCharacterPayloadDto) {
 *   // payload.characterId: string
 *   // payload.updateDto: UpdateCharacterDto
 *   // payload.serverId?: string
 *   // payload.injectedContext?: InjectedContext (网关注入，业务代码忽略)
 * }
 * ```
 */

import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsObject } from 'class-validator';
import { Expose, Type } from 'class-transformer';
import { InjectedContext } from '../types/injected-context.interface';

/**
 * 微服务Payload基类
 * 
 * 包含所有微服务接口通用的字段：
 * - serverId: 可选的服务器ID，用于分区分服架构
 * - injectedContext: 网关注入的上下文信息，业务代码通常忽略
 */
export abstract class BasePayloadDto {
  /**
   * 网关注入的上下文信息（可选）
   * 
   * 这个字段由网关自动注入，包含请求的元数据信息
   * 业务代码通常不需要直接使用，主要用于：
   * - 链路追踪和日志记录
   * - 安全审计
   * - 性能监控
   * - 调试和故障排查
   * 
   * 注意：这个字段不会被验证管道处理，因为它是系统级字段
   */
  @ApiProperty({ 
    description: '网关注入的上下文信息（系统字段，业务代码忽略）',
    required: false,
    type: 'object'
  })
  @Expose()
  @IsOptional()
  @IsObject({ message: '注入上下文必须是对象' })
  injectedContext?: InjectedContext;
}

/**
 * 常用的Payload DTO模式
 */

/**
 * ID + DTO 模式的基类
 * 适用于大多数更新操作：{ id: string, dto: SomeDto, serverId?, injectedContext? }
 */
export abstract class IdDtoPayloadDto<T = any> extends BasePayloadDto {
  /**
   * 资源ID
   * 子类应该重写这个字段以提供更具体的验证和文档
   */
  @ApiProperty({ description: '资源ID', example: 'resource_123' })
  @Expose()
  @IsString({ message: 'ID必须是字符串' })
  abstract id: string;

  /**
   * 数据传输对象
   * 子类应该重写这个字段以指定具体的DTO类型
   */
  @ApiProperty({ description: '数据传输对象' })
  @Expose()
  abstract dto: T;
}

/**
 * 双ID + DTO 模式的基类
 * 适用于涉及两个资源的操作：{ id1: string, id2: string, dto: SomeDto, serverId?, injectedContext? }
 */
export abstract class DoubleIdDtoPayloadDto<T = any> extends BasePayloadDto {
  /**
   * 第一个资源ID
   * 子类应该重写这个字段以提供更具体的验证和文档
   */
  @ApiProperty({ description: '第一个资源ID', example: 'resource1_123' })
  @Expose()
  @IsString({ message: '第一个ID必须是字符串' })
  abstract id1: string;

  /**
   * 第二个资源ID
   * 子类应该重写这个字段以提供更具体的验证和文档
   */
  @ApiProperty({ description: '第二个资源ID', example: 'resource2_456' })
  @Expose()
  @IsString({ message: '第二个ID必须是字符串' })
  abstract id2: string;

  /**
   * 数据传输对象
   * 子类应该重写这个字段以指定具体的DTO类型
   */
  @ApiProperty({ description: '数据传输对象' })
  @Expose()
  abstract dto: T;
}

/**
 * 纯DTO模式的基类
 * 适用于创建操作：{ dto: SomeDto, serverId?, injectedContext? }
 */
export abstract class DtoPayloadDto<T = any> extends BasePayloadDto {
  /**
   * 数据传输对象
   * 子类应该重写这个字段以指定具体的DTO类型
   */
  @ApiProperty({ description: '数据传输对象' })
  @Expose()
  abstract dto: T;
}

/**
 * 批量操作模式的基类
 * 适用于批量操作：{ ids: string[], dto?: SomeDto, serverId?, injectedContext? }
 */
export abstract class BatchPayloadDto<T = any> extends BasePayloadDto {
  /**
   * 资源ID列表
   */
  @ApiProperty({ 
    description: '资源ID列表', 
    example: ['resource_123', 'resource_456'],
    type: [String]
  })
  @Expose()
  @IsString({ each: true, message: '每个ID都必须是字符串' })
  ids: string[];

  /**
   * 可选的数据传输对象
   * 用于批量更新操作
   */
  @ApiProperty({ description: '批量操作的数据（可选）', required: false })
  @Expose()
  @IsOptional()
  dto?: T;
}

// 所有类型和接口已经通过export关键字导出，无需重复导出
