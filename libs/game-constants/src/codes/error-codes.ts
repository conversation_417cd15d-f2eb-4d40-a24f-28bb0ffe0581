/**
 * 游戏错误码定义
 * 重构为字符串常量，提供更好的可读性和调试体验
 * 
 * 后续国际化支持：
 * ```typescript
 * export const ErrorMessages = {
 *   [ErrorCode.CHAR_NOT_FOUND]: {
 *     zh: '角色不存在',
 *     en: 'Character not found',
 *   },
 *   [ErrorCode.CHAR_NAME_TAKEN]: {
 *     zh: '角色名称已被使用',
 *     en: 'Character name already taken',
 *   },
 * };
 * ```
 */

// 通用错误码常量
export const ErrorCode = {
  // 通用错误
  SUCCESS: 'SUCCESS',
  UNKNOWN_ERROR: 'UNKNOWN_ERROR',
  INVALID_PARAMETER: 'INVALID_PARAMETER',
  PERMISSION_DENIED: 'PERMISSION_DENIED',
  RESOURCE_NOT_FOUND: 'RESOURCE_NOT_FOUND',
  OPERATION_FAILED: 'OPERATION_FAILED',
  TIMEOUT: 'TIMEOUT',
  RATE_LIMITED: 'RATE_LIMITED',

  // 认证相关错误
  INVALID_TOKEN: 'INVALID_TOKEN',
  TOKEN_EXPIRED: 'TOKEN_EXPIRED',
  UNAUTHORIZED: 'UNAUTHORIZED',
  LOGIN_FAILED: 'LOGIN_FAILED',
  ACCOUNT_LOCKED: 'ACCOUNT_LOCKED',
  ACCOUNT_NOT_FOUND: 'ACCOUNT_NOT_FOUND',

  // 角色相关错误
  CHARACTER_NOT_FOUND: 'CHARACTER_NOT_FOUND',
  CHARACTER_ALREADY_EXISTS: 'CHARACTER_ALREADY_EXISTS',
  CHARACTER_OFFLINE: 'CHARACTER_OFFLINE',
  INSUFFICIENT_CURRENCY: 'INSUFFICIENT_CURRENCY',
  INSUFFICIENT_ENERGY: 'INSUFFICIENT_ENERGY',
  CHARACTER_LEVEL_TOO_LOW: 'CHARACTER_LEVEL_TOO_LOW',
  CHARACTER_NAME_INVALID: 'CHARACTER_NAME_INVALID',
  CHARACTER_NAME_TAKEN: 'CHARACTER_NAME_TAKEN',
  REDEEM_CODE_ALREADY_USED: 'REDEEM_CODE_ALREADY_USED',
  INVALID_BELIEF_ID: 'INVALID_BELIEF_ID',

  // Economy相关错误
  SHOP_NOT_FOUND: 'SHOP_NOT_FOUND',
  PURCHASE_LIMIT_EXCEEDED: 'PURCHASE_LIMIT_EXCEEDED',
  ECONOMY_INSUFFICIENT_CURRENCY: 'ECONOMY_INSUFFICIENT_CURRENCY',
  MONTH_CARD_REWARD_CLAIMED: 'MONTH_CARD_REWARD_CLAIMED',
  INVALID_SHOP_TYPE: 'INVALID_SHOP_TYPE',
  ECONOMY_PAYMENT_FAILED: 'ECONOMY_PAYMENT_FAILED',
  TRADE_NOT_FOUND: 'TRADE_NOT_FOUND',
  TRADE_ALREADY_COMPLETED: 'TRADE_ALREADY_COMPLETED',
  TIME_LIMIT_EXCEEDED: 'TIME_LIMIT_EXCEEDED',
  LEVEL_REQUIREMENT_NOT_MET: 'LEVEL_REQUIREMENT_NOT_MET',
  DELIVERY_FAILED: 'DELIVERY_FAILED',
  PAYMENT_VERIFICATION_FAILED: 'PAYMENT_VERIFICATION_FAILED',



  // 球员相关错误
  HERO_NOT_FOUND: 'HERO_NOT_FOUND',
  HERO_ALREADY_EXISTS: 'HERO_ALREADY_EXISTS',
  HERO_LOCKED: 'HERO_LOCKED',
  HERO_MAX_LEVEL: 'HERO_MAX_LEVEL',
  HERO_MAX_STAR: 'HERO_MAX_STAR',
  HERO_INSUFFICIENT_EXP: 'HERO_INSUFFICIENT_EXP',
  HERO_TRAINING_FAILED: 'HERO_TRAINING_FAILED',
  HERO_SKILL_MAX_LEVEL: 'HERO_SKILL_MAX_LEVEL',
  HERO_NOT_OWNED: 'HERO_NOT_OWNED',
  HERO_TRAINING_COOLDOWN: 'HERO_TRAINING_COOLDOWN',
  HERO_CANNOT_EVOLVE: 'HERO_CANNOT_EVOLVE',
  INVALID_MATERIAL_HERO: 'INVALID_MATERIAL_HERO',
  HERO_IN_FORMATION: 'HERO_IN_FORMATION',
  HERO_ON_MARKET: 'HERO_ON_MARKET',
  HERO_BREAKTHROUGH_LIMIT: 'HERO_BREAKTHROUGH_LIMIT',
  HERO_IN_TREATMENT: 'HERO_IN_TREATMENT',
  HERO_IN_TRAINING: 'HERO_IN_TRAINING',
  INSUFFICIENT_MATERIAL: 'INSUFFICIENT_MATERIAL',
  CONTRACT_NOT_EXPIRED: 'CONTRACT_NOT_EXPIRED',
  NO_BREAKTHROUGH_TO_REVERT: 'NO_BREAKTHROUGH_TO_REVERT',
  ALL_BREAKTHROUGH_PERFECT: 'ALL_BREAKTHROUGH_PERFECT',
  INSUFFICIENT_HEROES: 'INSUFFICIENT_HEROES',
  TACTIC_NOT_FOUND: 'TACTIC_NOT_FOUND',
  TACTIC_NOT_UNLOCKED: 'TACTIC_NOT_UNLOCKED',
  TACTIC_DEFINITION_NOT_FOUND: 'TACTIC_DEFINITION_NOT_FOUND',
  TACTIC_MAX_LEVEL: 'TACTIC_MAX_LEVEL',
  HERO_ALREADY_RETIRED: 'HERO_ALREADY_RETIRED',
  HERO_MAX_CAREER: 'HERO_MAX_CAREER',
  GUILD_MEMBER_LIMIT: 'GUILD_MEMBER_LIMIT',
  APPLICATION_NOT_FOUND: 'APPLICATION_NOT_FOUND',
  VICE_PRESIDENT_LIMIT: 'VICE_PRESIDENT_LIMIT',
  ONLY_PRESIDENT_CAN_TRANSFER: 'ONLY_PRESIDENT_CAN_TRANSFER',
  CHARACTER_NOT_IN_GUILD: 'CHARACTER_NOT_IN_GUILD',
  ENERGY_TIME_INVALID: 'ENERGY_TIME_INVALID',
  ENERGY_ALREADY_TAKEN: 'ENERGY_ALREADY_TAKEN',
  ENERGY_TAKE_FAILED: 'ENERGY_TAKE_FAILED',
  REWARD_NOT_AVAILABLE: 'REWARD_NOT_AVAILABLE',
  REWARD_CLAIM_FAILED: 'REWARD_CLAIM_FAILED',
  GIFT_BUY_LIMIT_REACHED: 'GIFT_BUY_LIMIT_REACHED',
  GIFT_ALREADY_BOUGHT_TODAY: 'GIFT_ALREADY_BOUGHT_TODAY',
  GUIDE_NOT_TRIGGERED: 'GUIDE_NOT_TRIGGERED',
  GUIDE_CONFIG_NOT_FOUND: 'GUIDE_CONFIG_NOT_FOUND',
  HONOR_TASK_NOT_FOUND: 'HONOR_TASK_NOT_FOUND',
  HONOR_TASK_NOT_COMPLETED: 'HONOR_TASK_NOT_COMPLETED',
  HONOR_REWARD_CLAIM_FAILED: 'HONOR_REWARD_CLAIM_FAILED',
  HONOR_TASK_ALREADY_EXISTS: 'HONOR_TASK_ALREADY_EXISTS',
  HONOR_LEVEL_NOT_REACHED: 'HONOR_LEVEL_NOT_REACHED',
  HONOR_LEVEL_RECORD_NOT_FOUND: 'HONOR_LEVEL_RECORD_NOT_FOUND',
  ITEM_CONFIG_NOT_FOUND: 'ITEM_CONFIG_NOT_FOUND',
  ITEM_NOT_ENOUGH: 'ITEM_NOT_ENOUGH',
  REFRESH_CONFIG_NOT_FOUND: 'REFRESH_CONFIG_NOT_FOUND',
  CHIP_NOT_ENOUGH: 'CHIP_NOT_ENOUGH',
  GOLD_NOT_ENOUGH: 'GOLD_NOT_ENOUGH',
  EXCHANGE_CONFIG_NOT_FOUND: 'EXCHANGE_CONFIG_NOT_FOUND',
  ITEM_ALREADY_BOUGHT: 'ITEM_ALREADY_BOUGHT',
  RELAY_NOT_JOINED: 'RELAY_NOT_JOINED',
  RELAY_ALREADY_JOINED: 'RELAY_ALREADY_JOINED',
  RELAY_EXPIRED: 'RELAY_EXPIRED',
  AWARD_NOT_AVAILABLE: 'AWARD_NOT_AVAILABLE',
  INTEGRAL_NOT_ENOUGH: 'INTEGRAL_NOT_ENOUGH',
  DIAMOND_NOT_ENOUGH: 'DIAMOND_NOT_ENOUGH',
  AWARD_NOT_FOUND: 'AWARD_NOT_FOUND',
  AWARD_RECEIVE_FAILED: 'AWARD_RECEIVE_FAILED',
  NO_AWARDS_AVAILABLE: 'NO_AWARDS_AVAILABLE',

  // Mail相关错误
  MAIL_ALREADY_CLAIMED: 'MAIL_ALREADY_CLAIMED',
  MAIL_NO_ATTACHMENT: 'MAIL_NO_ATTACHMENT',

  // Sign相关错误
  SIGN_DATA_NOT_FOUND: 'SIGN_DATA_NOT_FOUND',
  CANNOT_SIGN_TODAY: 'CANNOT_SIGN_TODAY',
  INVALID_MAKEUP_DAY: 'INVALID_MAKEUP_DAY',
  DAY_ALREADY_SIGNED: 'DAY_ALREADY_SIGNED',
  INVALID_SEVEN_DAY: 'INVALID_SEVEN_DAY',
  INSUFFICIENT_SIGN_DAYS: 'INSUFFICIENT_SIGN_DAYS',

  // Guild相关错误
  GUILD_NAME_EXISTS: 'GUILD_NAME_EXISTS',
  GUILD_APPLICATION_EXISTS: 'GUILD_APPLICATION_EXISTS',
  GUILD_APPLICATION_NOT_FOUND: 'GUILD_APPLICATION_NOT_FOUND',
  GUILD_NO_PERMISSION: 'GUILD_NO_PERMISSION',
  GUILD_PRESIDENT_CANNOT_LEAVE: 'GUILD_PRESIDENT_CANNOT_LEAVE',
  ALREADY_IN_GUILD: 'ALREADY_IN_GUILD',
  NOT_IN_GUILD: 'NOT_IN_GUILD',
  GUILD_MEMBER_NOT_FOUND: 'GUILD_MEMBER_NOT_FOUND',
  GUILD_CANNOT_KICK_PRESIDENT: 'GUILD_CANNOT_KICK_PRESIDENT',

  // Mail相关错误（扩展）
  MAIL_DELETE_FAILED: 'MAIL_DELETE_FAILED',

  // Sign相关错误（扩展）
  SIGN_RECORD_NOT_FOUND: 'SIGN_RECORD_NOT_FOUND',
  SIGN_ALREADY_SIGNED: 'SIGN_ALREADY_SIGNED',
  SIGN_FAILED: 'SIGN_FAILED',
  SIGN_INVALID_MAKEUP_DAY: 'SIGN_INVALID_MAKEUP_DAY',
  SIGN_MAKEUP_FAILED: 'SIGN_MAKEUP_FAILED',
  SIGN_SEVEN_DAY_REWARD_FAILED: 'SIGN_SEVEN_DAY_REWARD_FAILED',

  // Event相关错误
  EVENT_NOT_PARTICIPATED: 'EVENT_NOT_PARTICIPATED',
  EVENT_REWARD_CLAIMED: 'EVENT_REWARD_CLAIMED',
  EVENT_REWARD_CONDITION_NOT_MET: 'EVENT_REWARD_CONDITION_NOT_MET',
  FIRST_CHARGE_ALREADY_ACTIVE: 'FIRST_CHARGE_ALREADY_ACTIVE',

  // 阵容相关错误
  FORMATION_NOT_FOUND: 'FORMATION_NOT_FOUND',
  FORMATION_NAME_TAKEN: 'FORMATION_NAME_TAKEN',
  INVALID_FORMATION: 'INVALID_FORMATION',
  FORMATION_NOT_OWNED: 'FORMATION_NOT_OWNED',
  CHARACTER_NOT_IN_FORMATION: 'CHARACTER_NOT_IN_FORMATION',
  FORMATION_IS_ACTIVE: 'FORMATION_IS_ACTIVE',
  FORMATION_INCOMPLETE: 'FORMATION_INCOMPLETE',
  INVALID_FORMATION_TYPE: 'INVALID_FORMATION_TYPE',

  // 物品相关错误
  ITEM_NOT_FOUND: 'ITEM_NOT_FOUND',
  ITEM_NAME_TAKEN: 'ITEM_NAME_TAKEN',
  ITEM_CANNOT_USE: 'ITEM_CANNOT_USE',
  ITEM_USE_CONDITION_NOT_MET: 'ITEM_USE_CONDITION_NOT_MET',
  ITEM_NOT_CRAFTABLE: 'ITEM_NOT_CRAFTABLE',
  INSUFFICIENT_MATERIALS: 'INSUFFICIENT_MATERIALS',
  ITEM_EXPIRED: 'ITEM_EXPIRED',
  ITEM_NOT_TRADEABLE: 'ITEM_NOT_TRADEABLE',

  // 背包相关错误
  INVENTORY_NOT_FOUND: 'INVENTORY_NOT_FOUND',
  INVENTORY_FULL: 'INVENTORY_FULL',
  SLOT_OCCUPIED: 'SLOT_OCCUPIED',
  ITEM_NOT_IN_INVENTORY: 'ITEM_NOT_IN_INVENTORY',
  INSUFFICIENT_QUANTITY: 'INSUFFICIENT_QUANTITY',
  CANNOT_EXPAND_INVENTORY: 'CANNOT_EXPAND_INVENTORY',
  INVALID_SLOT: 'INVALID_SLOT',
  INVENTORY_ALREADY_EXISTS: 'INVENTORY_ALREADY_EXISTS',

  // 技能相关错误
  SKILL_NOT_FOUND: 'SKILL_NOT_FOUND',
  SKILL_ALREADY_EXISTS: 'SKILL_ALREADY_EXISTS',
  SKILL_ALREADY_LEARNED: 'SKILL_ALREADY_LEARNED',
  SKILL_CANNOT_UPGRADE: 'SKILL_CANNOT_UPGRADE',
  SKILL_IS_LOCKED: 'SKILL_IS_LOCKED',
  HERO_SKILL_NOT_FOUND: 'HERO_SKILL_NOT_FOUND',
  INVALID_UPGRADE_LEVEL: 'INVALID_UPGRADE_LEVEL',
  EXCEED_MAX_LEVEL: 'EXCEED_MAX_LEVEL',
  INSUFFICIENT_SKILL_POINTS: 'INSUFFICIENT_SKILL_POINTS',
  SKILL_COOLDOWN_ACTIVE: 'SKILL_COOLDOWN_ACTIVE',

  // 比赛相关错误
  MATCH_NOT_FOUND: 'MATCH_NOT_FOUND',
  MATCH_ALREADY_STARTED: 'MATCH_ALREADY_STARTED',
  MATCH_ALREADY_FINISHED: 'MATCH_ALREADY_FINISHED',
  MATCH_NOT_AVAILABLE: 'MATCH_NOT_AVAILABLE',
  INSUFFICIENT_ENERGY_FOR_MATCH: 'INSUFFICIENT_ENERGY_FOR_MATCH',
  FORMATION_REQUIRED: 'FORMATION_REQUIRED',
  MATCH_COOLDOWN: 'MATCH_COOLDOWN',

  // 经济相关错误
  SHOP_ITEM_NOT_FOUND: 'SHOP_ITEM_NOT_FOUND',
  SHOP_ITEM_SOLD_OUT: 'SHOP_ITEM_SOLD_OUT',
  INSUFFICIENT_FUNDS: 'INSUFFICIENT_FUNDS',
  PURCHASE_LIMIT_REACHED: 'PURCHASE_LIMIT_REACHED',
  PAYMENT_FAILED: 'PAYMENT_FAILED',
  REFUND_FAILED: 'REFUND_FAILED',
  PRICE_CHANGED: 'PRICE_CHANGED',

  // 社交相关错误
  FRIEND_NOT_FOUND: 'FRIEND_NOT_FOUND',
  FRIEND_REQUEST_ALREADY_SENT: 'FRIEND_REQUEST_ALREADY_SENT',
  FRIEND_LIMIT_REACHED: 'FRIEND_LIMIT_REACHED',
  CANNOT_ADD_SELF: 'CANNOT_ADD_SELF',
  FRIEND_ALREADY_EXISTS: 'FRIEND_ALREADY_EXISTS',
  MAIL_NOT_FOUND: 'MAIL_NOT_FOUND',
  MAIL_ALREADY_READ: 'MAIL_ALREADY_READ',
  MAIL_EXPIRED: 'MAIL_EXPIRED',
  GUILD_NOT_FOUND: 'GUILD_NOT_FOUND',
  GUILD_FULL: 'GUILD_FULL',
  GUILD_PERMISSION_DENIED: 'GUILD_PERMISSION_DENIED',

  // 活动相关错误
  ACTIVITY_NOT_FOUND: 'ACTIVITY_NOT_FOUND',
  ACTIVITY_NOT_STARTED: 'ACTIVITY_NOT_STARTED',
  ACTIVITY_ENDED: 'ACTIVITY_ENDED',
  ACTIVITY_REQUIREMENT_NOT_MET: 'ACTIVITY_REQUIREMENT_NOT_MET',
  TASK_NOT_FOUND: 'TASK_NOT_FOUND',
  TASK_ALREADY_COMPLETED: 'TASK_ALREADY_COMPLETED',
  TASK_NOT_COMPLETED: 'TASK_NOT_COMPLETED',
  REWARD_ALREADY_CLAIMED: 'REWARD_ALREADY_CLAIMED',
  SIGN_ALREADY_DONE: 'SIGN_ALREADY_DONE',

  // 配置相关错误
  CONFIG_NOT_FOUND: 'CONFIG_NOT_FOUND',
  CONFIG_INVALID: 'CONFIG_INVALID',
  CONFIG_VERSION_MISMATCH: 'CONFIG_VERSION_MISMATCH',

  // 系统相关错误
  MAINTENANCE_MODE: 'MAINTENANCE_MODE',
  SERVER_OVERLOAD: 'SERVER_OVERLOAD',
  DATABASE_ERROR: 'DATABASE_ERROR',
  CACHE_ERROR: 'CACHE_ERROR',
  NETWORK_ERROR: 'NETWORK_ERROR',
} as const;

// 错误码类型定义
export type ErrorCodeType = typeof ErrorCode[keyof typeof ErrorCode];

// 错误消息映射（保持所有现有消息不变）
export const ErrorMessages: Record<ErrorCodeType, string> = {
  // 通用错误
  [ErrorCode.SUCCESS]: '成功',
  [ErrorCode.UNKNOWN_ERROR]: '未知错误',
  [ErrorCode.INVALID_PARAMETER]: '参数错误',
  [ErrorCode.PERMISSION_DENIED]: '权限不足',
  [ErrorCode.RESOURCE_NOT_FOUND]: '资源不存在',
  [ErrorCode.OPERATION_FAILED]: '操作失败',
  [ErrorCode.TIMEOUT]: '操作超时',
  [ErrorCode.RATE_LIMITED]: '请求过于频繁',
  
  // 认证相关错误
  [ErrorCode.INVALID_TOKEN]: '无效的令牌',
  [ErrorCode.TOKEN_EXPIRED]: '令牌已过期',
  [ErrorCode.UNAUTHORIZED]: '未授权访问',
  [ErrorCode.LOGIN_FAILED]: '登录失败',
  [ErrorCode.ACCOUNT_LOCKED]: '账号已锁定',
  [ErrorCode.ACCOUNT_NOT_FOUND]: '账号不存在',
  
  // 角色相关错误
  [ErrorCode.CHARACTER_NOT_FOUND]: '角色不存在',
  [ErrorCode.CHARACTER_ALREADY_EXISTS]: '角色已存在',
  [ErrorCode.CHARACTER_OFFLINE]: '角色离线',
  [ErrorCode.INSUFFICIENT_CURRENCY]: '货币不足',
  [ErrorCode.INSUFFICIENT_ENERGY]: '体力不足',
  [ErrorCode.CHARACTER_LEVEL_TOO_LOW]: '角色等级不足',
  [ErrorCode.CHARACTER_NAME_INVALID]: '角色名称无效',
  [ErrorCode.CHARACTER_NAME_TAKEN]: '角色名称已被使用',
  [ErrorCode.REDEEM_CODE_ALREADY_USED]: '兑换码已使用',
  [ErrorCode.INVALID_BELIEF_ID]: '无效的信仰ID',

  // Economy经济相关错误消息
  [ErrorCode.SHOP_NOT_FOUND]: '商店不存在',
  [ErrorCode.PURCHASE_LIMIT_EXCEEDED]: '超出购买限制',
  [ErrorCode.ECONOMY_INSUFFICIENT_CURRENCY]: '货币不足',
  [ErrorCode.MONTH_CARD_REWARD_CLAIMED]: '月卡奖励已领取',
  [ErrorCode.INVALID_SHOP_TYPE]: '无效的商店类型',
  [ErrorCode.ECONOMY_PAYMENT_FAILED]: '支付失败',
  [ErrorCode.TRADE_NOT_FOUND]: '交易不存在',
  [ErrorCode.TRADE_ALREADY_COMPLETED]: '交易已完成',
  [ErrorCode.TIME_LIMIT_EXCEEDED]: '超出时间限制',
  [ErrorCode.LEVEL_REQUIREMENT_NOT_MET]: '等级要求不满足',
  [ErrorCode.DELIVERY_FAILED]: '发货失败',
  [ErrorCode.PAYMENT_VERIFICATION_FAILED]: '支付验证失败',
  [ErrorCode.SHOP_ITEM_NOT_FOUND]: '商品不存在',
  [ErrorCode.SHOP_ITEM_SOLD_OUT]: '商品已售罄',
  [ErrorCode.INSUFFICIENT_FUNDS]: '资金不足',
  [ErrorCode.PURCHASE_LIMIT_REACHED]: '购买次数已达上限',
  [ErrorCode.PAYMENT_FAILED]: '支付失败',
  [ErrorCode.REFUND_FAILED]: '退款失败',
  [ErrorCode.PRICE_CHANGED]: '价格已变更',



  // 球员相关错误
  [ErrorCode.HERO_NOT_FOUND]: '球员不存在',
  [ErrorCode.HERO_ALREADY_EXISTS]: '球员已存在',
  [ErrorCode.HERO_LOCKED]: '球员已锁定',
  [ErrorCode.HERO_MAX_LEVEL]: '球员已达最大等级',
  [ErrorCode.HERO_MAX_STAR]: '球员已达最大星级',
  [ErrorCode.HERO_INSUFFICIENT_EXP]: '球员经验不足',
  [ErrorCode.HERO_TRAINING_FAILED]: '球员训练失败',
  [ErrorCode.HERO_SKILL_MAX_LEVEL]: '技能已达最大等级',
  [ErrorCode.HERO_NOT_OWNED]: '球员不属于您',
  [ErrorCode.HERO_TRAINING_COOLDOWN]: '球员训练冷却中',
  [ErrorCode.HERO_CANNOT_EVOLVE]: '球员无法升星',
  [ErrorCode.INVALID_MATERIAL_HERO]: '无效的材料球员',
  [ErrorCode.HERO_IN_FORMATION]: '球员在阵容中',
  [ErrorCode.HERO_ON_MARKET]: '球员在市场中',
  [ErrorCode.HERO_BREAKTHROUGH_LIMIT]: '球员已达到突破上限',
  [ErrorCode.HERO_IN_TREATMENT]: '球员正在治疗中',
  [ErrorCode.HERO_IN_TRAINING]: '球员正在训练中',
  [ErrorCode.INSUFFICIENT_MATERIAL]: '升星材料不足',
  [ErrorCode.CONTRACT_NOT_EXPIRED]: '合约尚未到期，无法续约',
  [ErrorCode.NO_BREAKTHROUGH_TO_REVERT]: '没有突破记录可以撤销',
  [ErrorCode.ALL_BREAKTHROUGH_PERFECT]: '所有突破都是完美的，无法撤销',
  [ErrorCode.INSUFFICIENT_HEROES]: '可用球员数量不足',
  [ErrorCode.TACTIC_NOT_FOUND]: '战术不存在',
  [ErrorCode.TACTIC_NOT_UNLOCKED]: '战术尚未解锁',
  [ErrorCode.TACTIC_DEFINITION_NOT_FOUND]: '战术配置不存在',
  [ErrorCode.TACTIC_MAX_LEVEL]: '战术已达到最高等级',
  [ErrorCode.HERO_ALREADY_RETIRED]: '球员已退役',
  [ErrorCode.HERO_MAX_CAREER]: '球员已达到最大生涯次数',
  [ErrorCode.GUILD_MEMBER_LIMIT]: '公会成员已满',
  [ErrorCode.APPLICATION_NOT_FOUND]: '申请不存在',
  [ErrorCode.VICE_PRESIDENT_LIMIT]: '副会长数量已达上限',
  [ErrorCode.ONLY_PRESIDENT_CAN_TRANSFER]: '只有会长可以转让职位',
  [ErrorCode.CHARACTER_NOT_IN_GUILD]: '玩家不在公会中',
  [ErrorCode.ENERGY_TIME_INVALID]: '不在精力领取时间段内',
  [ErrorCode.ENERGY_ALREADY_TAKEN]: '今日该时段精力已领取',
  [ErrorCode.ENERGY_TAKE_FAILED]: '精力领取失败',
  [ErrorCode.REWARD_NOT_AVAILABLE]: '奖励不可领取',
  [ErrorCode.REWARD_CLAIM_FAILED]: '奖励领取失败',
  [ErrorCode.GIFT_BUY_LIMIT_REACHED]: '礼包购买次数已达上限',
  [ErrorCode.GIFT_ALREADY_BOUGHT_TODAY]: '今日已购买过该礼包',
  [ErrorCode.GUIDE_NOT_TRIGGERED]: '引导未触发',
  [ErrorCode.GUIDE_CONFIG_NOT_FOUND]: '引导配置不存在',
  [ErrorCode.HONOR_TASK_NOT_FOUND]: '荣誉任务不存在',
  [ErrorCode.HONOR_TASK_NOT_COMPLETED]: '荣誉任务未完成',
  [ErrorCode.HONOR_REWARD_CLAIM_FAILED]: '荣誉奖励领取失败',
  [ErrorCode.HONOR_TASK_ALREADY_EXISTS]: '荣誉任务已存在',
  [ErrorCode.HONOR_LEVEL_NOT_REACHED]: '荣誉等级未达到',
  [ErrorCode.HONOR_LEVEL_RECORD_NOT_FOUND]: '荣誉等级记录不存在',
  [ErrorCode.ITEM_CONFIG_NOT_FOUND]: '物品配置不存在',
  [ErrorCode.ITEM_NOT_ENOUGH]: '物品数量不足',
  [ErrorCode.REFRESH_CONFIG_NOT_FOUND]: '刷新配置不存在',
  [ErrorCode.CHIP_NOT_ENOUGH]: '碎片不足',
  [ErrorCode.GOLD_NOT_ENOUGH]: '金币不足',
  [ErrorCode.EXCHANGE_CONFIG_NOT_FOUND]: '兑换配置不存在',
  [ErrorCode.ITEM_ALREADY_BOUGHT]: '物品已购买',
  [ErrorCode.RELAY_NOT_JOINED]: '未加入联赛转播',
  [ErrorCode.RELAY_ALREADY_JOINED]: '已加入联赛转播',
  [ErrorCode.RELAY_EXPIRED]: '联赛转播已过期',
  [ErrorCode.AWARD_NOT_AVAILABLE]: '奖励不可领取',
  [ErrorCode.INTEGRAL_NOT_ENOUGH]: '积分不足',
  [ErrorCode.DIAMOND_NOT_ENOUGH]: '钻石不足',
  [ErrorCode.AWARD_NOT_FOUND]: '奖励不存在',
  [ErrorCode.AWARD_RECEIVE_FAILED]: '奖励领取失败',
  [ErrorCode.NO_AWARDS_AVAILABLE]: '没有可领取的奖励',

  // Mail相关错误消息
  [ErrorCode.MAIL_ALREADY_CLAIMED]: '邮件附件已领取',
  [ErrorCode.MAIL_NO_ATTACHMENT]: '邮件无附件',

  // Sign相关错误消息
  [ErrorCode.SIGN_DATA_NOT_FOUND]: '签到数据不存在',
  [ErrorCode.CANNOT_SIGN_TODAY]: '今日无法签到',
  [ErrorCode.INVALID_MAKEUP_DAY]: '无效的补签天数',
  [ErrorCode.DAY_ALREADY_SIGNED]: '该天已签到',
  [ErrorCode.INVALID_SEVEN_DAY]: '无效的七日签到天数',
  [ErrorCode.INSUFFICIENT_SIGN_DAYS]: '签到天数不足',

  // Guild相关错误消息
  [ErrorCode.GUILD_NAME_EXISTS]: '公会名称已存在',
  [ErrorCode.GUILD_APPLICATION_EXISTS]: '公会申请已存在',
  [ErrorCode.GUILD_APPLICATION_NOT_FOUND]: '公会申请不存在',
  [ErrorCode.GUILD_NO_PERMISSION]: '没有公会权限',
  [ErrorCode.GUILD_PRESIDENT_CANNOT_LEAVE]: '会长不能直接退出公会',
  [ErrorCode.ALREADY_IN_GUILD]: '已经在公会中',
  [ErrorCode.NOT_IN_GUILD]: '不在公会中',
  [ErrorCode.GUILD_MEMBER_NOT_FOUND]: '公会成员不存在',
  [ErrorCode.GUILD_CANNOT_KICK_PRESIDENT]: '不能踢出会长',

  // Mail相关错误消息
  [ErrorCode.MAIL_DELETE_FAILED]: '邮件删除失败',

  // Sign相关错误消息
  [ErrorCode.SIGN_RECORD_NOT_FOUND]: '签到记录不存在',
  [ErrorCode.SIGN_ALREADY_SIGNED]: '今日已签到',
  [ErrorCode.SIGN_FAILED]: '签到失败',
  [ErrorCode.SIGN_INVALID_MAKEUP_DAY]: '无效的补签天数',
  [ErrorCode.SIGN_MAKEUP_FAILED]: '补签失败',
  [ErrorCode.SIGN_SEVEN_DAY_REWARD_FAILED]: '七日签到奖励领取失败',

  // Event相关错误消息
  [ErrorCode.EVENT_NOT_PARTICIPATED]: '未参与该活动',
  [ErrorCode.EVENT_REWARD_CLAIMED]: '活动奖励已领取',
  [ErrorCode.EVENT_REWARD_CONDITION_NOT_MET]: '活动奖励领取条件未满足',
  [ErrorCode.FIRST_CHARGE_ALREADY_ACTIVE]: '首充已激活',

  // 阵容相关错误
  [ErrorCode.FORMATION_NOT_FOUND]: '阵容不存在',
  [ErrorCode.FORMATION_NAME_TAKEN]: '阵容名称已存在',
  [ErrorCode.INVALID_FORMATION]: '无效的阵容配置',
  [ErrorCode.FORMATION_NOT_OWNED]: '阵容不属于您',
  [ErrorCode.CHARACTER_NOT_IN_FORMATION]: '球员不在阵容中',
  [ErrorCode.FORMATION_IS_ACTIVE]: '阵容正在使用中',
  [ErrorCode.FORMATION_INCOMPLETE]: '阵容配置不完整',
  [ErrorCode.INVALID_FORMATION_TYPE]: '无效的阵型',

  // 物品相关错误
  [ErrorCode.ITEM_NOT_FOUND]: '物品不存在',
  [ErrorCode.ITEM_NAME_TAKEN]: '物品名称已存在',
  [ErrorCode.ITEM_CANNOT_USE]: '物品无法使用',
  [ErrorCode.ITEM_USE_CONDITION_NOT_MET]: '物品使用条件不满足',
  [ErrorCode.ITEM_NOT_CRAFTABLE]: '物品无法合成',
  [ErrorCode.INSUFFICIENT_MATERIALS]: '合成材料不足',
  [ErrorCode.ITEM_EXPIRED]: '物品已过期',
  [ErrorCode.ITEM_NOT_TRADEABLE]: '物品无法交易',

  // 背包相关错误
  [ErrorCode.INVENTORY_NOT_FOUND]: '背包不存在',
  [ErrorCode.INVENTORY_FULL]: '背包已满',
  [ErrorCode.SLOT_OCCUPIED]: '槽位已被占用',
  [ErrorCode.ITEM_NOT_IN_INVENTORY]: '物品不在背包中',
  [ErrorCode.INSUFFICIENT_QUANTITY]: '物品数量不足',
  [ErrorCode.CANNOT_EXPAND_INVENTORY]: '无法扩展背包',
  [ErrorCode.INVALID_SLOT]: '无效的槽位',
  [ErrorCode.INVENTORY_ALREADY_EXISTS]: '背包已存在',

  // 技能相关错误
  [ErrorCode.SKILL_NOT_FOUND]: '技能不存在',
  [ErrorCode.SKILL_ALREADY_EXISTS]: '技能已存在',
  [ErrorCode.SKILL_ALREADY_LEARNED]: '技能已学会',
  [ErrorCode.SKILL_CANNOT_UPGRADE]: '技能无法升级',
  [ErrorCode.SKILL_IS_LOCKED]: '技能已锁定',
  [ErrorCode.HERO_SKILL_NOT_FOUND]: '球员技能不存在',
  [ErrorCode.INVALID_UPGRADE_LEVEL]: '无效的升级等级',
  [ErrorCode.EXCEED_MAX_LEVEL]: '超过最大等级',
  [ErrorCode.INSUFFICIENT_SKILL_POINTS]: '技能点不足',
  [ErrorCode.SKILL_COOLDOWN_ACTIVE]: '技能冷却中',
  

  
  // 比赛相关错误
  [ErrorCode.MATCH_NOT_FOUND]: '比赛不存在',
  [ErrorCode.MATCH_ALREADY_STARTED]: '比赛已开始',
  [ErrorCode.MATCH_ALREADY_FINISHED]: '比赛已结束',
  [ErrorCode.MATCH_NOT_AVAILABLE]: '比赛不可用',
  [ErrorCode.INSUFFICIENT_ENERGY_FOR_MATCH]: '体力不足，无法参加比赛',
  [ErrorCode.FORMATION_REQUIRED]: '需要设置阵容',
  [ErrorCode.MATCH_COOLDOWN]: '比赛冷却中',
  

  
  // 社交相关错误
  [ErrorCode.FRIEND_NOT_FOUND]: '好友不存在',
  [ErrorCode.FRIEND_REQUEST_ALREADY_SENT]: '好友请求已发送',
  [ErrorCode.FRIEND_LIMIT_REACHED]: '好友数量已达上限',
  [ErrorCode.CANNOT_ADD_SELF]: '不能添加自己为好友',
  [ErrorCode.FRIEND_ALREADY_EXISTS]: '已经是好友',
  [ErrorCode.MAIL_NOT_FOUND]: '邮件不存在',
  [ErrorCode.MAIL_ALREADY_READ]: '邮件已读',
  [ErrorCode.MAIL_EXPIRED]: '邮件已过期',
  [ErrorCode.GUILD_NOT_FOUND]: '公会不存在',
  [ErrorCode.GUILD_FULL]: '公会已满',
  [ErrorCode.GUILD_PERMISSION_DENIED]: '公会权限不足',
  
  // 活动相关错误
  [ErrorCode.ACTIVITY_NOT_FOUND]: '活动不存在',
  [ErrorCode.ACTIVITY_NOT_STARTED]: '活动未开始',
  [ErrorCode.ACTIVITY_ENDED]: '活动已结束',
  [ErrorCode.ACTIVITY_REQUIREMENT_NOT_MET]: '活动参与条件不满足',
  [ErrorCode.TASK_NOT_FOUND]: '任务不存在',
  [ErrorCode.TASK_ALREADY_COMPLETED]: '任务已完成',
  [ErrorCode.TASK_NOT_COMPLETED]: '任务未完成',
  [ErrorCode.REWARD_ALREADY_CLAIMED]: '奖励已领取',
  [ErrorCode.SIGN_ALREADY_DONE]: '今日已签到',
  
  // 配置相关错误
  [ErrorCode.CONFIG_NOT_FOUND]: '配置不存在',
  [ErrorCode.CONFIG_INVALID]: '配置无效',
  [ErrorCode.CONFIG_VERSION_MISMATCH]: '配置版本不匹配',
  
  // 系统相关错误
  [ErrorCode.MAINTENANCE_MODE]: '系统维护中',
  [ErrorCode.SERVER_OVERLOAD]: '服务器负载过高',
  [ErrorCode.DATABASE_ERROR]: '数据库错误',
  [ErrorCode.CACHE_ERROR]: '缓存错误',
  [ErrorCode.NETWORK_ERROR]: '网络错误',
} as const;

/**
 * 错误码工具函数
 */
export class ErrorCodeUtils {
  /**
   * 获取错误消息
   * @param code 错误码
   * @returns 错误消息
   */
  static getMessage(code: ErrorCodeType): string {
    return ErrorMessages[code] || '未知错误';
  }

  /**
   * 检查是否为成功码
   * @param code 错误码
   * @returns 是否成功
   */
  static isSuccess(code: ErrorCodeType): boolean {
    return code === ErrorCode.SUCCESS;
  }

  /**
   * 检查是否为系统级错误
   * @param code 错误码
   * @returns 是否为系统级错误
   */
  static isSystemError(code: ErrorCodeType): boolean {
    return code === ErrorCode.MAINTENANCE_MODE ||
           code === ErrorCode.SERVER_OVERLOAD ||
           code === ErrorCode.DATABASE_ERROR ||
           code === ErrorCode.CACHE_ERROR ||
           code === ErrorCode.NETWORK_ERROR;
  }

  /**
   * 检查是否为认证相关错误
   * @param code 错误码
   * @returns 是否为认证相关错误
   */
  static isAuthError(code: ErrorCodeType): boolean {
    return code === ErrorCode.INVALID_TOKEN ||
           code === ErrorCode.TOKEN_EXPIRED ||
           code === ErrorCode.UNAUTHORIZED ||
           code === ErrorCode.LOGIN_FAILED ||
           code === ErrorCode.ACCOUNT_LOCKED ||
           code === ErrorCode.ACCOUNT_NOT_FOUND;
  }

  /**
   * 检查是否为业务逻辑错误
   * @param code 错误码
   * @returns 是否为业务逻辑错误
   */
  static isBusinessError(code: ErrorCodeType): boolean {
    return !this.isSystemError(code) && !this.isAuthError(code) && code !== ErrorCode.SUCCESS;
  }
}
